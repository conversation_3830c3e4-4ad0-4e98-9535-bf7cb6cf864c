var Ct=Object.defineProperty;var Qa=e=>{throw TypeError(e)};var zt=(e,a,o)=>a in e?Ct(e,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[a]=o;var M=(e,a,o)=>zt(e,typeof a!="symbol"?a+"":a,o),za=(e,a,o)=>a.has(e)||Qa("Cannot "+o);var be=(e,a,o)=>(za(e,a,"read from private field"),o?o.call(e):a.get(e)),Ve=(e,a,o)=>a.has(e)?Qa("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(e):a.set(e,o),Sa=(e,a,o,t)=>(za(e,a,"write to private field"),t?t.call(e,o):a.set(e,o),o),Ie=(e,a,o)=>(za(e,a,"access private method"),o);var St=/(%?)(%([sdijo]))/g;function At(e,a){switch(a){case"s":return e;case"d":case"i":return Number(e);case"j":return JSON.stringify(e);case"o":{if(typeof e=="string")return e;const o=JSON.stringify(e);return o==="{}"||o==="[]"||/^\[object .+?\]$/.test(o)?e:o}}}function Ge(e,...a){if(a.length===0)return e;let o=0,t=e.replace(St,(i,s,n,r)=>{const c=a[o],h=At(c,r);return s?i:(o++,h)});return o<a.length&&(t+=` ${a.slice(o).join(" ")}`),t=t.replace(/%{2,2}/g,"%"),t}var Lt=2;function Rt(e){if(!e.stack)return;const a=e.stack.split(`
`);a.splice(1,Lt),e.stack=a.join(`
`)}var _t=class extends Error{constructor(a,...o){super(a),this.message=a,this.name="Invariant Violation",this.message=Ge(a,...o),Rt(this)}},le=(e,a,...o)=>{if(!e)throw new _t(a,...o)};le.as=(e,a,o,...t)=>{if(!a){const i=t.length===0?o:Ge(o,...t);let s;try{s=Reflect.construct(e,[i])}catch{s=e(i)}throw s}};const It="[MSW]";function Ha(e,...a){const o=Ge(e,...a);return`${It} ${o}`}function Tt(e,...a){console.warn(Ha(e,...a))}function Pt(e,...a){console.error(Ha(e,...a))}const U={formatMessage:Ha,warn:Tt,error:Pt};class Za extends Error{constructor(a){super(a),this.name="InternalError"}}const Dt=/[\/\\]msw[\/\\]src[\/\\](.+)/,Ot=/(node_modules)?[\/\\]lib[\/\\](core|browser|node|native|iife)[\/\\]|^[^\/\\]*$/;function qt(e){const a=e.stack;if(!a)return;const t=a.split(`
`).slice(1).find(s=>!(Dt.test(s)||Ot.test(s)));return t?t.replace(/\s*at [^()]*\(([^)]+)\)/,"$1").replace(/^@/,""):void 0}function Ft(e){return e?Reflect.has(e,Symbol.iterator)||Reflect.has(e,Symbol.asyncIterator):!1}const aa=class aa{constructor(a){M(this,"__kind");M(this,"info");M(this,"isUsed");M(this,"resolver");M(this,"resolverIterator");M(this,"resolverIteratorResult");M(this,"options");this.resolver=a.resolver,this.options=a.options;const o=qt(new Error);this.info={...a.info,callFrame:o},this.isUsed=!1,this.__kind="RequestHandler"}async parse(a){return{}}async test(a){const o=await this.parse({request:a.request,resolutionContext:a.resolutionContext});return this.predicate({request:a.request,parsedResult:o,resolutionContext:a.resolutionContext})}extendResolverArgs(a){return{}}cloneRequestOrGetFromCache(a){const o=aa.cache.get(a);if(typeof o<"u")return o;const t=a.clone();return aa.cache.set(a,t),t}async run(a){var p,m;if(this.isUsed&&((p=this.options)!=null&&p.once))return null;const o=this.cloneRequestOrGetFromCache(a.request),t=await this.parse({request:a.request,resolutionContext:a.resolutionContext});if(!this.predicate({request:a.request,parsedResult:t,resolutionContext:a.resolutionContext})||this.isUsed&&((m=this.options)!=null&&m.once))return null;this.isUsed=!0;const s=this.wrapResolver(this.resolver),n=this.extendResolverArgs({request:a.request,parsedResult:t}),c=await s({...n,requestId:a.requestId,request:a.request}).catch(v=>{if(v instanceof Response)return v;throw v});return this.createExecutionResult({request:o,requestId:a.requestId,response:c,parsedResult:t})}wrapResolver(a){return async o=>{var n;if(!this.resolverIterator){const r=await a(o);if(!Ft(r))return r;this.resolverIterator=Symbol.iterator in r?r[Symbol.iterator]():r[Symbol.asyncIterator]()}this.isUsed=!1;const{done:t,value:i}=await this.resolverIterator.next(),s=await i;return s&&(this.resolverIteratorResult=s.clone()),t?(this.isUsed=!0,(n=this.resolverIteratorResult)==null?void 0:n.clone()):s}}createExecutionResult(a){return{handler:this,request:a.request,requestId:a.requestId,response:a.response,parsedResult:a.parsedResult}}};M(aa,"cache",new WeakMap);let ga=aa;var Mt=async e=>{try{return{error:null,data:await e().catch(o=>{throw o})}}catch(a){return{error:a,data:null}}};const Bt=async({request:e,requestId:a,handlers:o,resolutionContext:t})=>{let i=null,s=null;for(const n of o)if(s=await n.run({request:e,requestId:a,resolutionContext:t}),s!==null&&(i=n),s!=null&&s.response)break;return i?{handler:i,parsedResult:s==null?void 0:s.parsedResult,response:s==null?void 0:s.response}:null};function ta(e){if(typeof location>"u")return e.toString();const a=e instanceof URL?e:new URL(e);return a.origin===location.origin?a.pathname:a.origin+a.pathname}function $t(e){const a=new URL(e.url);return a.protocol==="file:"||/(fonts\.googleapis\.com)/.test(a.hostname)||/node_modules/.test(a.pathname)||a.pathname.includes("@vite")?!0:/\.(s?css|less|m?jsx?|m?tsx?|html|ttf|otf|woff|woff2|eot|gif|jpe?g|png|avif|webp|svg|mp4|webm|ogg|mov|mp3|wav|ogg|flac|aac|pdf|txt|csv|json|xml|md|zip|tar|gz|rar|7z)$/i.test(a.pathname)}async function So(e,a="warn"){const o=new URL(e.url),t=ta(o)+o.search,i=e.method==="HEAD"||e.method==="GET"?null:await e.clone().text(),n=`intercepted a request without a matching request handler:${`

  • ${e.method} ${t}

${i?`  • Request body: ${i}

`:""}`}If you still wish to intercept this unhandled request, please create a request handler for it.
Read more: https://mswjs.io/docs/http/intercepting-requests`;function r(c){switch(c){case"error":throw U.error("Error: %s",n),new Za(U.formatMessage('Cannot bypass a request when using the "error" strategy for the "onUnhandledRequest" option.'));case"warn":{U.warn("Warning: %s",n);break}case"bypass":break;default:throw new Za(U.formatMessage('Failed to react to an unhandled request: unknown strategy "%s". Please provide one of the supported strategies ("bypass", "warn", "error") or a custom callback function as the value of the "onUnhandledRequest" option.',c))}}if(typeof a=="function"){a(e,{warning:r.bind(null,"warn"),error:r.bind(null,"error")});return}$t(e)||r(a)}function Ao(){if(typeof navigator<"u"&&navigator.product==="ReactNative")return!0;if(typeof process<"u"){const e=process.type;return e==="renderer"||e==="worker"?!1:!!(process.versions&&process.versions.node)}return!1}var Ut=Object.create,Lo=Object.defineProperty,Nt=Object.getOwnPropertyDescriptor,Ro=Object.getOwnPropertyNames,Ht=Object.getPrototypeOf,Wt=Object.prototype.hasOwnProperty,Gt=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(a,o)=>(typeof require<"u"?require:a)[o]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),ae=(e,a)=>function(){return a||(0,e[Ro(e)[0]])((a={exports:{}}).exports,a),a.exports},Vt=(e,a,o,t)=>{if(a&&typeof a=="object"||typeof a=="function")for(let i of Ro(a))!Wt.call(e,i)&&i!==o&&Lo(e,i,{get:()=>a[i],enumerable:!(t=Nt(a,i))||t.enumerable});return e},Jt=(e,a,o)=>(o=e!=null?Ut(Ht(e)):{},Vt(Lo(o,"default",{value:e,enumerable:!0}),e)),_o=ae({"node_modules/punycode/punycode.js"(e,a){var o=2147483647,t=36,i=1,s=26,n=38,r=700,c=72,h=128,p="-",m=/^xn--/,v=/[^\0-\x7F]/,b=/[\x2E\u3002\uFF0E\uFF61]/g,y={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},z=t-i,C=Math.floor,A=String.fromCharCode;function q(f){throw new RangeError(y[f])}function R(f,P){const V=[];let B=f.length;for(;B--;)V[B]=P(f[B]);return V}function g(f,P){const V=f.split("@");let B="";V.length>1&&(B=V[0]+"@",f=V[1]),f=f.replace(b,".");const G=f.split("."),Y=R(G,P).join(".");return B+Y}function j(f){const P=[];let V=0;const B=f.length;for(;V<B;){const G=f.charCodeAt(V++);if(G>=55296&&G<=56319&&V<B){const Y=f.charCodeAt(V++);(Y&64512)==56320?P.push(((G&1023)<<10)+(Y&1023)+65536):(P.push(G),V--)}else P.push(G)}return P}var E=f=>String.fromCodePoint(...f),w=function(f){return f>=48&&f<58?26+(f-48):f>=65&&f<91?f-65:f>=97&&f<123?f-97:t},T=function(f,P){return f+22+75*(f<26)-((P!=0)<<5)},S=function(f,P,V){let B=0;for(f=V?C(f/r):f>>1,f+=C(f/P);f>z*s>>1;B+=t)f=C(f/z);return C(B+(z+1)*f/(f+n))},_=function(f){const P=[],V=f.length;let B=0,G=h,Y=c,ue=f.lastIndexOf(p);ue<0&&(ue=0);for(let Q=0;Q<ue;++Q)f.charCodeAt(Q)>=128&&q("not-basic"),P.push(f.charCodeAt(Q));for(let Q=ue>0?ue+1:0;Q<V;){const Z=B;for(let ie=1,ne=t;;ne+=t){Q>=V&&q("invalid-input");const oe=w(f.charCodeAt(Q++));oe>=t&&q("invalid-input"),oe>C((o-B)/ie)&&q("overflow"),B+=oe*ie;const H=ne<=Y?i:ne>=Y+s?s:ne-Y;if(oe<H)break;const je=t-H;ie>C(o/je)&&q("overflow"),ie*=je}const se=P.length+1;Y=S(B-Z,se,Z==0),C(B/se)>o-G&&q("overflow"),G+=C(B/se),B%=se,P.splice(B++,0,G)}return String.fromCodePoint(...P)},I=function(f){const P=[];f=j(f);const V=f.length;let B=h,G=0,Y=c;for(const Z of f)Z<128&&P.push(A(Z));const ue=P.length;let Q=ue;for(ue&&P.push(p);Q<V;){let Z=o;for(const ie of f)ie>=B&&ie<Z&&(Z=ie);const se=Q+1;Z-B>C((o-G)/se)&&q("overflow"),G+=(Z-B)*se,B=Z;for(const ie of f)if(ie<B&&++G>o&&q("overflow"),ie===B){let ne=G;for(let oe=t;;oe+=t){const H=oe<=Y?i:oe>=Y+s?s:oe-Y;if(ne<H)break;const je=ne-H,pe=t-H;P.push(A(T(H+je%pe,0))),ne=C(je/pe)}P.push(A(T(ne,0))),Y=S(G,se,Q===ue),G=0,++Q}++G,++B}return P.join("")},W=function(f){return g(f,function(P){return m.test(P)?_(P.slice(4).toLowerCase()):P})},me=function(f){return g(f,function(P){return v.test(P)?"xn--"+I(P):P})},re={version:"2.3.1",ucs2:{decode:j,encode:E},decode:_,encode:I,toASCII:me,toUnicode:W};a.exports=re}}),Xt=ae({"node_modules/requires-port/index.js"(e,a){a.exports=function(t,i){if(i=i.split(":")[0],t=+t,!t)return!1;switch(i){case"http":case"ws":return t!==80;case"https":case"wss":return t!==443;case"ftp":return t!==21;case"gopher":return t!==70;case"file":return!1}return t!==0}}}),Kt=ae({"node_modules/querystringify/index.js"(e){var a=Object.prototype.hasOwnProperty,o;function t(r){try{return decodeURIComponent(r.replace(/\+/g," "))}catch{return null}}function i(r){try{return encodeURIComponent(r)}catch{return null}}function s(r){for(var c=/([^=?#&]+)=?([^&]*)/g,h={},p;p=c.exec(r);){var m=t(p[1]),v=t(p[2]);m===null||v===null||m in h||(h[m]=v)}return h}function n(r,c){c=c||"";var h=[],p,m;typeof c!="string"&&(c="?");for(m in r)if(a.call(r,m)){if(p=r[m],!p&&(p===null||p===o||isNaN(p))&&(p=""),m=i(m),p=i(p),m===null||p===null)continue;h.push(m+"="+p)}return h.length?c+h.join("&"):""}e.stringify=n,e.parse=s}}),Yt=ae({"node_modules/url-parse/index.js"(e,a){var o=Xt(),t=Kt(),i=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,s=/[\n\r\t]/g,n=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,r=/:\d+$/,c=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,h=/^[a-zA-Z]:/;function p(g){return(g||"").toString().replace(i,"")}var m=[["#","hash"],["?","query"],function(j,E){return y(E.protocol)?j.replace(/\\/g,"/"):j},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],v={hash:1,query:1};function b(g){var j;typeof window<"u"?j=window:typeof global<"u"?j=global:typeof self<"u"?j=self:j={};var E=j.location||{};g=g||E;var w={},T=typeof g,S;if(g.protocol==="blob:")w=new A(unescape(g.pathname),{});else if(T==="string"){w=new A(g,{});for(S in v)delete w[S]}else if(T==="object"){for(S in g)S in v||(w[S]=g[S]);w.slashes===void 0&&(w.slashes=n.test(g.href))}return w}function y(g){return g==="file:"||g==="ftp:"||g==="http:"||g==="https:"||g==="ws:"||g==="wss:"}function z(g,j){g=p(g),g=g.replace(s,""),j=j||{};var E=c.exec(g),w=E[1]?E[1].toLowerCase():"",T=!!E[2],S=!!E[3],_=0,I;return T?S?(I=E[2]+E[3]+E[4],_=E[2].length+E[3].length):(I=E[2]+E[4],_=E[2].length):S?(I=E[3]+E[4],_=E[3].length):I=E[4],w==="file:"?_>=2&&(I=I.slice(2)):y(w)?I=E[4]:w?T&&(I=I.slice(2)):_>=2&&y(j.protocol)&&(I=E[4]),{protocol:w,slashes:T||y(w),slashesCount:_,rest:I}}function C(g,j){if(g==="")return j;for(var E=(j||"/").split("/").slice(0,-1).concat(g.split("/")),w=E.length,T=E[w-1],S=!1,_=0;w--;)E[w]==="."?E.splice(w,1):E[w]===".."?(E.splice(w,1),_++):_&&(w===0&&(S=!0),E.splice(w,1),_--);return S&&E.unshift(""),(T==="."||T==="..")&&E.push(""),E.join("/")}function A(g,j,E){if(g=p(g),g=g.replace(s,""),!(this instanceof A))return new A(g,j,E);var w,T,S,_,I,W,me=m.slice(),re=typeof j,f=this,P=0;for(re!=="object"&&re!=="string"&&(E=j,j=null),E&&typeof E!="function"&&(E=t.parse),j=b(j),T=z(g||"",j),w=!T.protocol&&!T.slashes,f.slashes=T.slashes||w&&j.slashes,f.protocol=T.protocol||j.protocol||"",g=T.rest,(T.protocol==="file:"&&(T.slashesCount!==2||h.test(g))||!T.slashes&&(T.protocol||T.slashesCount<2||!y(f.protocol)))&&(me[3]=[/(.*)/,"pathname"]);P<me.length;P++){if(_=me[P],typeof _=="function"){g=_(g,f);continue}S=_[0],W=_[1],S!==S?f[W]=g:typeof S=="string"?(I=S==="@"?g.lastIndexOf(S):g.indexOf(S),~I&&(typeof _[2]=="number"?(f[W]=g.slice(0,I),g=g.slice(I+_[2])):(f[W]=g.slice(I),g=g.slice(0,I)))):(I=S.exec(g))&&(f[W]=I[1],g=g.slice(0,I.index)),f[W]=f[W]||w&&_[3]&&j[W]||"",_[4]&&(f[W]=f[W].toLowerCase())}E&&(f.query=E(f.query)),w&&j.slashes&&f.pathname.charAt(0)!=="/"&&(f.pathname!==""||j.pathname!=="")&&(f.pathname=C(f.pathname,j.pathname)),f.pathname.charAt(0)!=="/"&&y(f.protocol)&&(f.pathname="/"+f.pathname),o(f.port,f.protocol)||(f.host=f.hostname,f.port=""),f.username=f.password="",f.auth&&(I=f.auth.indexOf(":"),~I?(f.username=f.auth.slice(0,I),f.username=encodeURIComponent(decodeURIComponent(f.username)),f.password=f.auth.slice(I+1),f.password=encodeURIComponent(decodeURIComponent(f.password))):f.username=encodeURIComponent(decodeURIComponent(f.auth)),f.auth=f.password?f.username+":"+f.password:f.username),f.origin=f.protocol!=="file:"&&y(f.protocol)&&f.host?f.protocol+"//"+f.host:"null",f.href=f.toString()}function q(g,j,E){var w=this;switch(g){case"query":typeof j=="string"&&j.length&&(j=(E||t.parse)(j)),w[g]=j;break;case"port":w[g]=j,o(j,w.protocol)?j&&(w.host=w.hostname+":"+j):(w.host=w.hostname,w[g]="");break;case"hostname":w[g]=j,w.port&&(j+=":"+w.port),w.host=j;break;case"host":w[g]=j,r.test(j)?(j=j.split(":"),w.port=j.pop(),w.hostname=j.join(":")):(w.hostname=j,w.port="");break;case"protocol":w.protocol=j.toLowerCase(),w.slashes=!E;break;case"pathname":case"hash":if(j){var T=g==="pathname"?"/":"#";w[g]=j.charAt(0)!==T?T+j:j}else w[g]=j;break;case"username":case"password":w[g]=encodeURIComponent(j);break;case"auth":var S=j.indexOf(":");~S?(w.username=j.slice(0,S),w.username=encodeURIComponent(decodeURIComponent(w.username)),w.password=j.slice(S+1),w.password=encodeURIComponent(decodeURIComponent(w.password))):w.username=encodeURIComponent(decodeURIComponent(j))}for(var _=0;_<m.length;_++){var I=m[_];I[4]&&(w[I[1]]=w[I[1]].toLowerCase())}return w.auth=w.password?w.username+":"+w.password:w.username,w.origin=w.protocol!=="file:"&&y(w.protocol)&&w.host?w.protocol+"//"+w.host:"null",w.href=w.toString(),w}function R(g){(!g||typeof g!="function")&&(g=t.stringify);var j,E=this,w=E.host,T=E.protocol;T&&T.charAt(T.length-1)!==":"&&(T+=":");var S=T+(E.protocol&&E.slashes||y(E.protocol)?"//":"");return E.username?(S+=E.username,E.password&&(S+=":"+E.password),S+="@"):E.password?(S+=":"+E.password,S+="@"):E.protocol!=="file:"&&y(E.protocol)&&!w&&E.pathname!=="/"&&(S+="@"),(w[w.length-1]===":"||r.test(E.hostname)&&!E.port)&&(w+=":"),S+=w+E.pathname,j=typeof E.query=="object"?g(E.query):E.query,j&&(S+=j.charAt(0)!=="?"?"?"+j:j),E.hash&&(S+=E.hash),S}A.prototype={set:q,toString:R},A.extractProtocol=z,A.location=b,A.trimLeft=p,A.qs=t,a.exports=A}}),Qt=ae({"node_modules/psl/data/rules.json"(e,a){a.exports=["ac","com.ac","edu.ac","gov.ac","net.ac","mil.ac","org.ac","ad","nom.ad","ae","co.ae","net.ae","org.ae","sch.ae","ac.ae","gov.ae","mil.ae","aero","accident-investigation.aero","accident-prevention.aero","aerobatic.aero","aeroclub.aero","aerodrome.aero","agents.aero","aircraft.aero","airline.aero","airport.aero","air-surveillance.aero","airtraffic.aero","air-traffic-control.aero","ambulance.aero","amusement.aero","association.aero","author.aero","ballooning.aero","broker.aero","caa.aero","cargo.aero","catering.aero","certification.aero","championship.aero","charter.aero","civilaviation.aero","club.aero","conference.aero","consultant.aero","consulting.aero","control.aero","council.aero","crew.aero","design.aero","dgca.aero","educator.aero","emergency.aero","engine.aero","engineer.aero","entertainment.aero","equipment.aero","exchange.aero","express.aero","federation.aero","flight.aero","fuel.aero","gliding.aero","government.aero","groundhandling.aero","group.aero","hanggliding.aero","homebuilt.aero","insurance.aero","journal.aero","journalist.aero","leasing.aero","logistics.aero","magazine.aero","maintenance.aero","media.aero","microlight.aero","modelling.aero","navigation.aero","parachuting.aero","paragliding.aero","passenger-association.aero","pilot.aero","press.aero","production.aero","recreation.aero","repbody.aero","res.aero","research.aero","rotorcraft.aero","safety.aero","scientist.aero","services.aero","show.aero","skydiving.aero","software.aero","student.aero","trader.aero","trading.aero","trainer.aero","union.aero","workinggroup.aero","works.aero","af","gov.af","com.af","org.af","net.af","edu.af","ag","com.ag","org.ag","net.ag","co.ag","nom.ag","ai","off.ai","com.ai","net.ai","org.ai","al","com.al","edu.al","gov.al","mil.al","net.al","org.al","am","co.am","com.am","commune.am","net.am","org.am","ao","ed.ao","gv.ao","og.ao","co.ao","pb.ao","it.ao","aq","ar","bet.ar","com.ar","coop.ar","edu.ar","gob.ar","gov.ar","int.ar","mil.ar","musica.ar","mutual.ar","net.ar","org.ar","senasa.ar","tur.ar","arpa","e164.arpa","in-addr.arpa","ip6.arpa","iris.arpa","uri.arpa","urn.arpa","as","gov.as","asia","at","ac.at","co.at","gv.at","or.at","sth.ac.at","au","com.au","net.au","org.au","edu.au","gov.au","asn.au","id.au","info.au","conf.au","oz.au","act.au","nsw.au","nt.au","qld.au","sa.au","tas.au","vic.au","wa.au","act.edu.au","catholic.edu.au","nsw.edu.au","nt.edu.au","qld.edu.au","sa.edu.au","tas.edu.au","vic.edu.au","wa.edu.au","qld.gov.au","sa.gov.au","tas.gov.au","vic.gov.au","wa.gov.au","schools.nsw.edu.au","aw","com.aw","ax","az","com.az","net.az","int.az","gov.az","org.az","edu.az","info.az","pp.az","mil.az","name.az","pro.az","biz.az","ba","com.ba","edu.ba","gov.ba","mil.ba","net.ba","org.ba","bb","biz.bb","co.bb","com.bb","edu.bb","gov.bb","info.bb","net.bb","org.bb","store.bb","tv.bb","*.bd","be","ac.be","bf","gov.bf","bg","a.bg","b.bg","c.bg","d.bg","e.bg","f.bg","g.bg","h.bg","i.bg","j.bg","k.bg","l.bg","m.bg","n.bg","o.bg","p.bg","q.bg","r.bg","s.bg","t.bg","u.bg","v.bg","w.bg","x.bg","y.bg","z.bg","0.bg","1.bg","2.bg","3.bg","4.bg","5.bg","6.bg","7.bg","8.bg","9.bg","bh","com.bh","edu.bh","net.bh","org.bh","gov.bh","bi","co.bi","com.bi","edu.bi","or.bi","org.bi","biz","bj","asso.bj","barreau.bj","gouv.bj","bm","com.bm","edu.bm","gov.bm","net.bm","org.bm","bn","com.bn","edu.bn","gov.bn","net.bn","org.bn","bo","com.bo","edu.bo","gob.bo","int.bo","org.bo","net.bo","mil.bo","tv.bo","web.bo","academia.bo","agro.bo","arte.bo","blog.bo","bolivia.bo","ciencia.bo","cooperativa.bo","democracia.bo","deporte.bo","ecologia.bo","economia.bo","empresa.bo","indigena.bo","industria.bo","info.bo","medicina.bo","movimiento.bo","musica.bo","natural.bo","nombre.bo","noticias.bo","patria.bo","politica.bo","profesional.bo","plurinacional.bo","pueblo.bo","revista.bo","salud.bo","tecnologia.bo","tksat.bo","transporte.bo","wiki.bo","br","9guacu.br","abc.br","adm.br","adv.br","agr.br","aju.br","am.br","anani.br","aparecida.br","app.br","arq.br","art.br","ato.br","b.br","barueri.br","belem.br","bhz.br","bib.br","bio.br","blog.br","bmd.br","boavista.br","bsb.br","campinagrande.br","campinas.br","caxias.br","cim.br","cng.br","cnt.br","com.br","contagem.br","coop.br","coz.br","cri.br","cuiaba.br","curitiba.br","def.br","des.br","det.br","dev.br","ecn.br","eco.br","edu.br","emp.br","enf.br","eng.br","esp.br","etc.br","eti.br","far.br","feira.br","flog.br","floripa.br","fm.br","fnd.br","fortal.br","fot.br","foz.br","fst.br","g12.br","geo.br","ggf.br","goiania.br","gov.br","ac.gov.br","al.gov.br","am.gov.br","ap.gov.br","ba.gov.br","ce.gov.br","df.gov.br","es.gov.br","go.gov.br","ma.gov.br","mg.gov.br","ms.gov.br","mt.gov.br","pa.gov.br","pb.gov.br","pe.gov.br","pi.gov.br","pr.gov.br","rj.gov.br","rn.gov.br","ro.gov.br","rr.gov.br","rs.gov.br","sc.gov.br","se.gov.br","sp.gov.br","to.gov.br","gru.br","imb.br","ind.br","inf.br","jab.br","jampa.br","jdf.br","joinville.br","jor.br","jus.br","leg.br","lel.br","log.br","londrina.br","macapa.br","maceio.br","manaus.br","maringa.br","mat.br","med.br","mil.br","morena.br","mp.br","mus.br","natal.br","net.br","niteroi.br","*.nom.br","not.br","ntr.br","odo.br","ong.br","org.br","osasco.br","palmas.br","poa.br","ppg.br","pro.br","psc.br","psi.br","pvh.br","qsl.br","radio.br","rec.br","recife.br","rep.br","ribeirao.br","rio.br","riobranco.br","riopreto.br","salvador.br","sampa.br","santamaria.br","santoandre.br","saobernardo.br","saogonca.br","seg.br","sjc.br","slg.br","slz.br","sorocaba.br","srv.br","taxi.br","tc.br","tec.br","teo.br","the.br","tmp.br","trd.br","tur.br","tv.br","udi.br","vet.br","vix.br","vlog.br","wiki.br","zlg.br","bs","com.bs","net.bs","org.bs","edu.bs","gov.bs","bt","com.bt","edu.bt","gov.bt","net.bt","org.bt","bv","bw","co.bw","org.bw","by","gov.by","mil.by","com.by","of.by","bz","com.bz","net.bz","org.bz","edu.bz","gov.bz","ca","ab.ca","bc.ca","mb.ca","nb.ca","nf.ca","nl.ca","ns.ca","nt.ca","nu.ca","on.ca","pe.ca","qc.ca","sk.ca","yk.ca","gc.ca","cat","cc","cd","gov.cd","cf","cg","ch","ci","org.ci","or.ci","com.ci","co.ci","edu.ci","ed.ci","ac.ci","net.ci","go.ci","asso.ci","aéroport.ci","int.ci","presse.ci","md.ci","gouv.ci","*.ck","!www.ck","cl","co.cl","gob.cl","gov.cl","mil.cl","cm","co.cm","com.cm","gov.cm","net.cm","cn","ac.cn","com.cn","edu.cn","gov.cn","net.cn","org.cn","mil.cn","公司.cn","网络.cn","網絡.cn","ah.cn","bj.cn","cq.cn","fj.cn","gd.cn","gs.cn","gz.cn","gx.cn","ha.cn","hb.cn","he.cn","hi.cn","hl.cn","hn.cn","jl.cn","js.cn","jx.cn","ln.cn","nm.cn","nx.cn","qh.cn","sc.cn","sd.cn","sh.cn","sn.cn","sx.cn","tj.cn","xj.cn","xz.cn","yn.cn","zj.cn","hk.cn","mo.cn","tw.cn","co","arts.co","com.co","edu.co","firm.co","gov.co","info.co","int.co","mil.co","net.co","nom.co","org.co","rec.co","web.co","com","coop","cr","ac.cr","co.cr","ed.cr","fi.cr","go.cr","or.cr","sa.cr","cu","com.cu","edu.cu","org.cu","net.cu","gov.cu","inf.cu","cv","com.cv","edu.cv","int.cv","nome.cv","org.cv","cw","com.cw","edu.cw","net.cw","org.cw","cx","gov.cx","cy","ac.cy","biz.cy","com.cy","ekloges.cy","gov.cy","ltd.cy","mil.cy","net.cy","org.cy","press.cy","pro.cy","tm.cy","cz","de","dj","dk","dm","com.dm","net.dm","org.dm","edu.dm","gov.dm","do","art.do","com.do","edu.do","gob.do","gov.do","mil.do","net.do","org.do","sld.do","web.do","dz","art.dz","asso.dz","com.dz","edu.dz","gov.dz","org.dz","net.dz","pol.dz","soc.dz","tm.dz","ec","com.ec","info.ec","net.ec","fin.ec","k12.ec","med.ec","pro.ec","org.ec","edu.ec","gov.ec","gob.ec","mil.ec","edu","ee","edu.ee","gov.ee","riik.ee","lib.ee","med.ee","com.ee","pri.ee","aip.ee","org.ee","fie.ee","eg","com.eg","edu.eg","eun.eg","gov.eg","mil.eg","name.eg","net.eg","org.eg","sci.eg","*.er","es","com.es","nom.es","org.es","gob.es","edu.es","et","com.et","gov.et","org.et","edu.et","biz.et","name.et","info.et","net.et","eu","fi","aland.fi","fj","ac.fj","biz.fj","com.fj","gov.fj","info.fj","mil.fj","name.fj","net.fj","org.fj","pro.fj","*.fk","com.fm","edu.fm","net.fm","org.fm","fm","fo","fr","asso.fr","com.fr","gouv.fr","nom.fr","prd.fr","tm.fr","aeroport.fr","avocat.fr","avoues.fr","cci.fr","chambagri.fr","chirurgiens-dentistes.fr","experts-comptables.fr","geometre-expert.fr","greta.fr","huissier-justice.fr","medecin.fr","notaires.fr","pharmacien.fr","port.fr","veterinaire.fr","ga","gb","edu.gd","gov.gd","gd","ge","com.ge","edu.ge","gov.ge","org.ge","mil.ge","net.ge","pvt.ge","gf","gg","co.gg","net.gg","org.gg","gh","com.gh","edu.gh","gov.gh","org.gh","mil.gh","gi","com.gi","ltd.gi","gov.gi","mod.gi","edu.gi","org.gi","gl","co.gl","com.gl","edu.gl","net.gl","org.gl","gm","gn","ac.gn","com.gn","edu.gn","gov.gn","org.gn","net.gn","gov","gp","com.gp","net.gp","mobi.gp","edu.gp","org.gp","asso.gp","gq","gr","com.gr","edu.gr","net.gr","org.gr","gov.gr","gs","gt","com.gt","edu.gt","gob.gt","ind.gt","mil.gt","net.gt","org.gt","gu","com.gu","edu.gu","gov.gu","guam.gu","info.gu","net.gu","org.gu","web.gu","gw","gy","co.gy","com.gy","edu.gy","gov.gy","net.gy","org.gy","hk","com.hk","edu.hk","gov.hk","idv.hk","net.hk","org.hk","公司.hk","教育.hk","敎育.hk","政府.hk","個人.hk","个��.hk","箇人.hk","網络.hk","网络.hk","组織.hk","網絡.hk","网絡.hk","组织.hk","組織.hk","組织.hk","hm","hn","com.hn","edu.hn","org.hn","net.hn","mil.hn","gob.hn","hr","iz.hr","from.hr","name.hr","com.hr","ht","com.ht","shop.ht","firm.ht","info.ht","adult.ht","net.ht","pro.ht","org.ht","med.ht","art.ht","coop.ht","pol.ht","asso.ht","edu.ht","rel.ht","gouv.ht","perso.ht","hu","co.hu","info.hu","org.hu","priv.hu","sport.hu","tm.hu","2000.hu","agrar.hu","bolt.hu","casino.hu","city.hu","erotica.hu","erotika.hu","film.hu","forum.hu","games.hu","hotel.hu","ingatlan.hu","jogasz.hu","konyvelo.hu","lakas.hu","media.hu","news.hu","reklam.hu","sex.hu","shop.hu","suli.hu","szex.hu","tozsde.hu","utazas.hu","video.hu","id","ac.id","biz.id","co.id","desa.id","go.id","mil.id","my.id","net.id","or.id","ponpes.id","sch.id","web.id","ie","gov.ie","il","ac.il","co.il","gov.il","idf.il","k12.il","muni.il","net.il","org.il","im","ac.im","co.im","com.im","ltd.co.im","net.im","org.im","plc.co.im","tt.im","tv.im","in","co.in","firm.in","net.in","org.in","gen.in","ind.in","nic.in","ac.in","edu.in","res.in","gov.in","mil.in","info","int","eu.int","io","com.io","iq","gov.iq","edu.iq","mil.iq","com.iq","org.iq","net.iq","ir","ac.ir","co.ir","gov.ir","id.ir","net.ir","org.ir","sch.ir","ایران.ir","ايران.ir","is","net.is","com.is","edu.is","gov.is","org.is","int.is","it","gov.it","edu.it","abr.it","abruzzo.it","aosta-valley.it","aostavalley.it","bas.it","basilicata.it","cal.it","calabria.it","cam.it","campania.it","emilia-romagna.it","emiliaromagna.it","emr.it","friuli-v-giulia.it","friuli-ve-giulia.it","friuli-vegiulia.it","friuli-venezia-giulia.it","friuli-veneziagiulia.it","friuli-vgiulia.it","friuliv-giulia.it","friulive-giulia.it","friulivegiulia.it","friulivenezia-giulia.it","friuliveneziagiulia.it","friulivgiulia.it","fvg.it","laz.it","lazio.it","lig.it","liguria.it","lom.it","lombardia.it","lombardy.it","lucania.it","mar.it","marche.it","mol.it","molise.it","piedmont.it","piemonte.it","pmn.it","pug.it","puglia.it","sar.it","sardegna.it","sardinia.it","sic.it","sicilia.it","sicily.it","taa.it","tos.it","toscana.it","trentin-sud-tirol.it","trentin-süd-tirol.it","trentin-sudtirol.it","trentin-südtirol.it","trentin-sued-tirol.it","trentin-suedtirol.it","trentino-a-adige.it","trentino-aadige.it","trentino-alto-adige.it","trentino-altoadige.it","trentino-s-tirol.it","trentino-stirol.it","trentino-sud-tirol.it","trentino-süd-tirol.it","trentino-sudtirol.it","trentino-südtirol.it","trentino-sued-tirol.it","trentino-suedtirol.it","trentino.it","trentinoa-adige.it","trentinoaadige.it","trentinoalto-adige.it","trentinoaltoadige.it","trentinos-tirol.it","trentinostirol.it","trentinosud-tirol.it","trentinosüd-tirol.it","trentinosudtirol.it","trentinosüdtirol.it","trentinosued-tirol.it","trentinosuedtirol.it","trentinsud-tirol.it","trentinsüd-tirol.it","trentinsudtirol.it","trentinsüdtirol.it","trentinsued-tirol.it","trentinsuedtirol.it","tuscany.it","umb.it","umbria.it","val-d-aosta.it","val-daosta.it","vald-aosta.it","valdaosta.it","valle-aosta.it","valle-d-aosta.it","valle-daosta.it","valleaosta.it","valled-aosta.it","valledaosta.it","vallee-aoste.it","vallée-aoste.it","vallee-d-aoste.it","vallée-d-aoste.it","valleeaoste.it","valléeaoste.it","valleedaoste.it","valléedaoste.it","vao.it","vda.it","ven.it","veneto.it","ag.it","agrigento.it","al.it","alessandria.it","alto-adige.it","altoadige.it","an.it","ancona.it","andria-barletta-trani.it","andria-trani-barletta.it","andriabarlettatrani.it","andriatranibarletta.it","ao.it","aosta.it","aoste.it","ap.it","aq.it","aquila.it","ar.it","arezzo.it","ascoli-piceno.it","ascolipiceno.it","asti.it","at.it","av.it","avellino.it","ba.it","balsan-sudtirol.it","balsan-südtirol.it","balsan-suedtirol.it","balsan.it","bari.it","barletta-trani-andria.it","barlettatraniandria.it","belluno.it","benevento.it","bergamo.it","bg.it","bi.it","biella.it","bl.it","bn.it","bo.it","bologna.it","bolzano-altoadige.it","bolzano.it","bozen-sudtirol.it","bozen-südtirol.it","bozen-suedtirol.it","bozen.it","br.it","brescia.it","brindisi.it","bs.it","bt.it","bulsan-sudtirol.it","bulsan-südtirol.it","bulsan-suedtirol.it","bulsan.it","bz.it","ca.it","cagliari.it","caltanissetta.it","campidano-medio.it","campidanomedio.it","campobasso.it","carbonia-iglesias.it","carboniaiglesias.it","carrara-massa.it","carraramassa.it","caserta.it","catania.it","catanzaro.it","cb.it","ce.it","cesena-forli.it","cesena-forlì.it","cesenaforli.it","cesenaforlì.it","ch.it","chieti.it","ci.it","cl.it","cn.it","co.it","como.it","cosenza.it","cr.it","cremona.it","crotone.it","cs.it","ct.it","cuneo.it","cz.it","dell-ogliastra.it","dellogliastra.it","en.it","enna.it","fc.it","fe.it","fermo.it","ferrara.it","fg.it","fi.it","firenze.it","florence.it","fm.it","foggia.it","forli-cesena.it","forlì-cesena.it","forlicesena.it","forlìcesena.it","fr.it","frosinone.it","ge.it","genoa.it","genova.it","go.it","gorizia.it","gr.it","grosseto.it","iglesias-carbonia.it","iglesiascarbonia.it","im.it","imperia.it","is.it","isernia.it","kr.it","la-spezia.it","laquila.it","laspezia.it","latina.it","lc.it","le.it","lecce.it","lecco.it","li.it","livorno.it","lo.it","lodi.it","lt.it","lu.it","lucca.it","macerata.it","mantova.it","massa-carrara.it","massacarrara.it","matera.it","mb.it","mc.it","me.it","medio-campidano.it","mediocampidano.it","messina.it","mi.it","milan.it","milano.it","mn.it","mo.it","modena.it","monza-brianza.it","monza-e-della-brianza.it","monza.it","monzabrianza.it","monzaebrianza.it","monzaedellabrianza.it","ms.it","mt.it","na.it","naples.it","napoli.it","no.it","novara.it","nu.it","nuoro.it","og.it","ogliastra.it","olbia-tempio.it","olbiatempio.it","or.it","oristano.it","ot.it","pa.it","padova.it","padua.it","palermo.it","parma.it","pavia.it","pc.it","pd.it","pe.it","perugia.it","pesaro-urbino.it","pesarourbino.it","pescara.it","pg.it","pi.it","piacenza.it","pisa.it","pistoia.it","pn.it","po.it","pordenone.it","potenza.it","pr.it","prato.it","pt.it","pu.it","pv.it","pz.it","ra.it","ragusa.it","ravenna.it","rc.it","re.it","reggio-calabria.it","reggio-emilia.it","reggiocalabria.it","reggioemilia.it","rg.it","ri.it","rieti.it","rimini.it","rm.it","rn.it","ro.it","roma.it","rome.it","rovigo.it","sa.it","salerno.it","sassari.it","savona.it","si.it","siena.it","siracusa.it","so.it","sondrio.it","sp.it","sr.it","ss.it","suedtirol.it","südtirol.it","sv.it","ta.it","taranto.it","te.it","tempio-olbia.it","tempioolbia.it","teramo.it","terni.it","tn.it","to.it","torino.it","tp.it","tr.it","trani-andria-barletta.it","trani-barletta-andria.it","traniandriabarletta.it","tranibarlettaandria.it","trapani.it","trento.it","treviso.it","trieste.it","ts.it","turin.it","tv.it","ud.it","udine.it","urbino-pesaro.it","urbinopesaro.it","va.it","varese.it","vb.it","vc.it","ve.it","venezia.it","venice.it","verbania.it","vercelli.it","verona.it","vi.it","vibo-valentia.it","vibovalentia.it","vicenza.it","viterbo.it","vr.it","vs.it","vt.it","vv.it","je","co.je","net.je","org.je","*.jm","jo","com.jo","org.jo","net.jo","edu.jo","sch.jo","gov.jo","mil.jo","name.jo","jobs","jp","ac.jp","ad.jp","co.jp","ed.jp","go.jp","gr.jp","lg.jp","ne.jp","or.jp","aichi.jp","akita.jp","aomori.jp","chiba.jp","ehime.jp","fukui.jp","fukuoka.jp","fukushima.jp","gifu.jp","gunma.jp","hiroshima.jp","hokkaido.jp","hyogo.jp","ibaraki.jp","ishikawa.jp","iwate.jp","kagawa.jp","kagoshima.jp","kanagawa.jp","kochi.jp","kumamoto.jp","kyoto.jp","mie.jp","miyagi.jp","miyazaki.jp","nagano.jp","nagasaki.jp","nara.jp","niigata.jp","oita.jp","okayama.jp","okinawa.jp","osaka.jp","saga.jp","saitama.jp","shiga.jp","shimane.jp","shizuoka.jp","tochigi.jp","tokushima.jp","tokyo.jp","tottori.jp","toyama.jp","wakayama.jp","yamagata.jp","yamaguchi.jp","yamanashi.jp","栃木.jp","愛知.jp","愛媛.jp","兵庫.jp","熊本.jp","茨城.jp","北海道.jp","千葉.jp","和歌山.jp","長崎.jp","長野.jp","新潟.jp","青森.jp","静岡.jp","東京.jp","石川.jp","埼玉.jp","三重.jp","京都.jp","佐賀.jp","大分.jp","大阪.jp","奈良.jp","宮城.jp","宮崎.jp","富山.jp","山口.jp","山形.jp","山梨.jp","岩手.jp","岐阜.jp","岡山.jp","島根.jp","広島.jp","徳島.jp","沖縄.jp","滋賀.jp","神奈川.jp","福井.jp","福岡.jp","福島.jp","秋田.jp","群馬.jp","香川.jp","高知.jp","鳥取.jp","鹿児島.jp","*.kawasaki.jp","*.kitakyushu.jp","*.kobe.jp","*.nagoya.jp","*.sapporo.jp","*.sendai.jp","*.yokohama.jp","!city.kawasaki.jp","!city.kitakyushu.jp","!city.kobe.jp","!city.nagoya.jp","!city.sapporo.jp","!city.sendai.jp","!city.yokohama.jp","aisai.aichi.jp","ama.aichi.jp","anjo.aichi.jp","asuke.aichi.jp","chiryu.aichi.jp","chita.aichi.jp","fuso.aichi.jp","gamagori.aichi.jp","handa.aichi.jp","hazu.aichi.jp","hekinan.aichi.jp","higashiura.aichi.jp","ichinomiya.aichi.jp","inazawa.aichi.jp","inuyama.aichi.jp","isshiki.aichi.jp","iwakura.aichi.jp","kanie.aichi.jp","kariya.aichi.jp","kasugai.aichi.jp","kira.aichi.jp","kiyosu.aichi.jp","komaki.aichi.jp","konan.aichi.jp","kota.aichi.jp","mihama.aichi.jp","miyoshi.aichi.jp","nishio.aichi.jp","nisshin.aichi.jp","obu.aichi.jp","oguchi.aichi.jp","oharu.aichi.jp","okazaki.aichi.jp","owariasahi.aichi.jp","seto.aichi.jp","shikatsu.aichi.jp","shinshiro.aichi.jp","shitara.aichi.jp","tahara.aichi.jp","takahama.aichi.jp","tobishima.aichi.jp","toei.aichi.jp","togo.aichi.jp","tokai.aichi.jp","tokoname.aichi.jp","toyoake.aichi.jp","toyohashi.aichi.jp","toyokawa.aichi.jp","toyone.aichi.jp","toyota.aichi.jp","tsushima.aichi.jp","yatomi.aichi.jp","akita.akita.jp","daisen.akita.jp","fujisato.akita.jp","gojome.akita.jp","hachirogata.akita.jp","happou.akita.jp","higashinaruse.akita.jp","honjo.akita.jp","honjyo.akita.jp","ikawa.akita.jp","kamikoani.akita.jp","kamioka.akita.jp","katagami.akita.jp","kazuno.akita.jp","kitaakita.akita.jp","kosaka.akita.jp","kyowa.akita.jp","misato.akita.jp","mitane.akita.jp","moriyoshi.akita.jp","nikaho.akita.jp","noshiro.akita.jp","odate.akita.jp","oga.akita.jp","ogata.akita.jp","semboku.akita.jp","yokote.akita.jp","yurihonjo.akita.jp","aomori.aomori.jp","gonohe.aomori.jp","hachinohe.aomori.jp","hashikami.aomori.jp","hiranai.aomori.jp","hirosaki.aomori.jp","itayanagi.aomori.jp","kuroishi.aomori.jp","misawa.aomori.jp","mutsu.aomori.jp","nakadomari.aomori.jp","noheji.aomori.jp","oirase.aomori.jp","owani.aomori.jp","rokunohe.aomori.jp","sannohe.aomori.jp","shichinohe.aomori.jp","shingo.aomori.jp","takko.aomori.jp","towada.aomori.jp","tsugaru.aomori.jp","tsuruta.aomori.jp","abiko.chiba.jp","asahi.chiba.jp","chonan.chiba.jp","chosei.chiba.jp","choshi.chiba.jp","chuo.chiba.jp","funabashi.chiba.jp","futtsu.chiba.jp","hanamigawa.chiba.jp","ichihara.chiba.jp","ichikawa.chiba.jp","ichinomiya.chiba.jp","inzai.chiba.jp","isumi.chiba.jp","kamagaya.chiba.jp","kamogawa.chiba.jp","kashiwa.chiba.jp","katori.chiba.jp","katsuura.chiba.jp","kimitsu.chiba.jp","kisarazu.chiba.jp","kozaki.chiba.jp","kujukuri.chiba.jp","kyonan.chiba.jp","matsudo.chiba.jp","midori.chiba.jp","mihama.chiba.jp","minamiboso.chiba.jp","mobara.chiba.jp","mutsuzawa.chiba.jp","nagara.chiba.jp","nagareyama.chiba.jp","narashino.chiba.jp","narita.chiba.jp","noda.chiba.jp","oamishirasato.chiba.jp","omigawa.chiba.jp","onjuku.chiba.jp","otaki.chiba.jp","sakae.chiba.jp","sakura.chiba.jp","shimofusa.chiba.jp","shirako.chiba.jp","shiroi.chiba.jp","shisui.chiba.jp","sodegaura.chiba.jp","sosa.chiba.jp","tako.chiba.jp","tateyama.chiba.jp","togane.chiba.jp","tohnosho.chiba.jp","tomisato.chiba.jp","urayasu.chiba.jp","yachimata.chiba.jp","yachiyo.chiba.jp","yokaichiba.chiba.jp","yokoshibahikari.chiba.jp","yotsukaido.chiba.jp","ainan.ehime.jp","honai.ehime.jp","ikata.ehime.jp","imabari.ehime.jp","iyo.ehime.jp","kamijima.ehime.jp","kihoku.ehime.jp","kumakogen.ehime.jp","masaki.ehime.jp","matsuno.ehime.jp","matsuyama.ehime.jp","namikata.ehime.jp","niihama.ehime.jp","ozu.ehime.jp","saijo.ehime.jp","seiyo.ehime.jp","shikokuchuo.ehime.jp","tobe.ehime.jp","toon.ehime.jp","uchiko.ehime.jp","uwajima.ehime.jp","yawatahama.ehime.jp","echizen.fukui.jp","eiheiji.fukui.jp","fukui.fukui.jp","ikeda.fukui.jp","katsuyama.fukui.jp","mihama.fukui.jp","minamiechizen.fukui.jp","obama.fukui.jp","ohi.fukui.jp","ono.fukui.jp","sabae.fukui.jp","sakai.fukui.jp","takahama.fukui.jp","tsuruga.fukui.jp","wakasa.fukui.jp","ashiya.fukuoka.jp","buzen.fukuoka.jp","chikugo.fukuoka.jp","chikuho.fukuoka.jp","chikujo.fukuoka.jp","chikushino.fukuoka.jp","chikuzen.fukuoka.jp","chuo.fukuoka.jp","dazaifu.fukuoka.jp","fukuchi.fukuoka.jp","hakata.fukuoka.jp","higashi.fukuoka.jp","hirokawa.fukuoka.jp","hisayama.fukuoka.jp","iizuka.fukuoka.jp","inatsuki.fukuoka.jp","kaho.fukuoka.jp","kasuga.fukuoka.jp","kasuya.fukuoka.jp","kawara.fukuoka.jp","keisen.fukuoka.jp","koga.fukuoka.jp","kurate.fukuoka.jp","kurogi.fukuoka.jp","kurume.fukuoka.jp","minami.fukuoka.jp","miyako.fukuoka.jp","miyama.fukuoka.jp","miyawaka.fukuoka.jp","mizumaki.fukuoka.jp","munakata.fukuoka.jp","nakagawa.fukuoka.jp","nakama.fukuoka.jp","nishi.fukuoka.jp","nogata.fukuoka.jp","ogori.fukuoka.jp","okagaki.fukuoka.jp","okawa.fukuoka.jp","oki.fukuoka.jp","omuta.fukuoka.jp","onga.fukuoka.jp","onojo.fukuoka.jp","oto.fukuoka.jp","saigawa.fukuoka.jp","sasaguri.fukuoka.jp","shingu.fukuoka.jp","shinyoshitomi.fukuoka.jp","shonai.fukuoka.jp","soeda.fukuoka.jp","sue.fukuoka.jp","tachiarai.fukuoka.jp","tagawa.fukuoka.jp","takata.fukuoka.jp","toho.fukuoka.jp","toyotsu.fukuoka.jp","tsuiki.fukuoka.jp","ukiha.fukuoka.jp","umi.fukuoka.jp","usui.fukuoka.jp","yamada.fukuoka.jp","yame.fukuoka.jp","yanagawa.fukuoka.jp","yukuhashi.fukuoka.jp","aizubange.fukushima.jp","aizumisato.fukushima.jp","aizuwakamatsu.fukushima.jp","asakawa.fukushima.jp","bandai.fukushima.jp","date.fukushima.jp","fukushima.fukushima.jp","furudono.fukushima.jp","futaba.fukushima.jp","hanawa.fukushima.jp","higashi.fukushima.jp","hirata.fukushima.jp","hirono.fukushima.jp","iitate.fukushima.jp","inawashiro.fukushima.jp","ishikawa.fukushima.jp","iwaki.fukushima.jp","izumizaki.fukushima.jp","kagamiishi.fukushima.jp","kaneyama.fukushima.jp","kawamata.fukushima.jp","kitakata.fukushima.jp","kitashiobara.fukushima.jp","koori.fukushima.jp","koriyama.fukushima.jp","kunimi.fukushima.jp","miharu.fukushima.jp","mishima.fukushima.jp","namie.fukushima.jp","nango.fukushima.jp","nishiaizu.fukushima.jp","nishigo.fukushima.jp","okuma.fukushima.jp","omotego.fukushima.jp","ono.fukushima.jp","otama.fukushima.jp","samegawa.fukushima.jp","shimogo.fukushima.jp","shirakawa.fukushima.jp","showa.fukushima.jp","soma.fukushima.jp","sukagawa.fukushima.jp","taishin.fukushima.jp","tamakawa.fukushima.jp","tanagura.fukushima.jp","tenei.fukushima.jp","yabuki.fukushima.jp","yamato.fukushima.jp","yamatsuri.fukushima.jp","yanaizu.fukushima.jp","yugawa.fukushima.jp","anpachi.gifu.jp","ena.gifu.jp","gifu.gifu.jp","ginan.gifu.jp","godo.gifu.jp","gujo.gifu.jp","hashima.gifu.jp","hichiso.gifu.jp","hida.gifu.jp","higashishirakawa.gifu.jp","ibigawa.gifu.jp","ikeda.gifu.jp","kakamigahara.gifu.jp","kani.gifu.jp","kasahara.gifu.jp","kasamatsu.gifu.jp","kawaue.gifu.jp","kitagata.gifu.jp","mino.gifu.jp","minokamo.gifu.jp","mitake.gifu.jp","mizunami.gifu.jp","motosu.gifu.jp","nakatsugawa.gifu.jp","ogaki.gifu.jp","sakahogi.gifu.jp","seki.gifu.jp","sekigahara.gifu.jp","shirakawa.gifu.jp","tajimi.gifu.jp","takayama.gifu.jp","tarui.gifu.jp","toki.gifu.jp","tomika.gifu.jp","wanouchi.gifu.jp","yamagata.gifu.jp","yaotsu.gifu.jp","yoro.gifu.jp","annaka.gunma.jp","chiyoda.gunma.jp","fujioka.gunma.jp","higashiagatsuma.gunma.jp","isesaki.gunma.jp","itakura.gunma.jp","kanna.gunma.jp","kanra.gunma.jp","katashina.gunma.jp","kawaba.gunma.jp","kiryu.gunma.jp","kusatsu.gunma.jp","maebashi.gunma.jp","meiwa.gunma.jp","midori.gunma.jp","minakami.gunma.jp","naganohara.gunma.jp","nakanojo.gunma.jp","nanmoku.gunma.jp","numata.gunma.jp","oizumi.gunma.jp","ora.gunma.jp","ota.gunma.jp","shibukawa.gunma.jp","shimonita.gunma.jp","shinto.gunma.jp","showa.gunma.jp","takasaki.gunma.jp","takayama.gunma.jp","tamamura.gunma.jp","tatebayashi.gunma.jp","tomioka.gunma.jp","tsukiyono.gunma.jp","tsumagoi.gunma.jp","ueno.gunma.jp","yoshioka.gunma.jp","asaminami.hiroshima.jp","daiwa.hiroshima.jp","etajima.hiroshima.jp","fuchu.hiroshima.jp","fukuyama.hiroshima.jp","hatsukaichi.hiroshima.jp","higashihiroshima.hiroshima.jp","hongo.hiroshima.jp","jinsekikogen.hiroshima.jp","kaita.hiroshima.jp","kui.hiroshima.jp","kumano.hiroshima.jp","kure.hiroshima.jp","mihara.hiroshima.jp","miyoshi.hiroshima.jp","naka.hiroshima.jp","onomichi.hiroshima.jp","osakikamijima.hiroshima.jp","otake.hiroshima.jp","saka.hiroshima.jp","sera.hiroshima.jp","seranishi.hiroshima.jp","shinichi.hiroshima.jp","shobara.hiroshima.jp","takehara.hiroshima.jp","abashiri.hokkaido.jp","abira.hokkaido.jp","aibetsu.hokkaido.jp","akabira.hokkaido.jp","akkeshi.hokkaido.jp","asahikawa.hokkaido.jp","ashibetsu.hokkaido.jp","ashoro.hokkaido.jp","assabu.hokkaido.jp","atsuma.hokkaido.jp","bibai.hokkaido.jp","biei.hokkaido.jp","bifuka.hokkaido.jp","bihoro.hokkaido.jp","biratori.hokkaido.jp","chippubetsu.hokkaido.jp","chitose.hokkaido.jp","date.hokkaido.jp","ebetsu.hokkaido.jp","embetsu.hokkaido.jp","eniwa.hokkaido.jp","erimo.hokkaido.jp","esan.hokkaido.jp","esashi.hokkaido.jp","fukagawa.hokkaido.jp","fukushima.hokkaido.jp","furano.hokkaido.jp","furubira.hokkaido.jp","haboro.hokkaido.jp","hakodate.hokkaido.jp","hamatonbetsu.hokkaido.jp","hidaka.hokkaido.jp","higashikagura.hokkaido.jp","higashikawa.hokkaido.jp","hiroo.hokkaido.jp","hokuryu.hokkaido.jp","hokuto.hokkaido.jp","honbetsu.hokkaido.jp","horokanai.hokkaido.jp","horonobe.hokkaido.jp","ikeda.hokkaido.jp","imakane.hokkaido.jp","ishikari.hokkaido.jp","iwamizawa.hokkaido.jp","iwanai.hokkaido.jp","kamifurano.hokkaido.jp","kamikawa.hokkaido.jp","kamishihoro.hokkaido.jp","kamisunagawa.hokkaido.jp","kamoenai.hokkaido.jp","kayabe.hokkaido.jp","kembuchi.hokkaido.jp","kikonai.hokkaido.jp","kimobetsu.hokkaido.jp","kitahiroshima.hokkaido.jp","kitami.hokkaido.jp","kiyosato.hokkaido.jp","koshimizu.hokkaido.jp","kunneppu.hokkaido.jp","kuriyama.hokkaido.jp","kuromatsunai.hokkaido.jp","kushiro.hokkaido.jp","kutchan.hokkaido.jp","kyowa.hokkaido.jp","mashike.hokkaido.jp","matsumae.hokkaido.jp","mikasa.hokkaido.jp","minamifurano.hokkaido.jp","mombetsu.hokkaido.jp","moseushi.hokkaido.jp","mukawa.hokkaido.jp","muroran.hokkaido.jp","naie.hokkaido.jp","nakagawa.hokkaido.jp","nakasatsunai.hokkaido.jp","nakatombetsu.hokkaido.jp","nanae.hokkaido.jp","nanporo.hokkaido.jp","nayoro.hokkaido.jp","nemuro.hokkaido.jp","niikappu.hokkaido.jp","niki.hokkaido.jp","nishiokoppe.hokkaido.jp","noboribetsu.hokkaido.jp","numata.hokkaido.jp","obihiro.hokkaido.jp","obira.hokkaido.jp","oketo.hokkaido.jp","okoppe.hokkaido.jp","otaru.hokkaido.jp","otobe.hokkaido.jp","otofuke.hokkaido.jp","otoineppu.hokkaido.jp","oumu.hokkaido.jp","ozora.hokkaido.jp","pippu.hokkaido.jp","rankoshi.hokkaido.jp","rebun.hokkaido.jp","rikubetsu.hokkaido.jp","rishiri.hokkaido.jp","rishirifuji.hokkaido.jp","saroma.hokkaido.jp","sarufutsu.hokkaido.jp","shakotan.hokkaido.jp","shari.hokkaido.jp","shibecha.hokkaido.jp","shibetsu.hokkaido.jp","shikabe.hokkaido.jp","shikaoi.hokkaido.jp","shimamaki.hokkaido.jp","shimizu.hokkaido.jp","shimokawa.hokkaido.jp","shinshinotsu.hokkaido.jp","shintoku.hokkaido.jp","shiranuka.hokkaido.jp","shiraoi.hokkaido.jp","shiriuchi.hokkaido.jp","sobetsu.hokkaido.jp","sunagawa.hokkaido.jp","taiki.hokkaido.jp","takasu.hokkaido.jp","takikawa.hokkaido.jp","takinoue.hokkaido.jp","teshikaga.hokkaido.jp","tobetsu.hokkaido.jp","tohma.hokkaido.jp","tomakomai.hokkaido.jp","tomari.hokkaido.jp","toya.hokkaido.jp","toyako.hokkaido.jp","toyotomi.hokkaido.jp","toyoura.hokkaido.jp","tsubetsu.hokkaido.jp","tsukigata.hokkaido.jp","urakawa.hokkaido.jp","urausu.hokkaido.jp","uryu.hokkaido.jp","utashinai.hokkaido.jp","wakkanai.hokkaido.jp","wassamu.hokkaido.jp","yakumo.hokkaido.jp","yoichi.hokkaido.jp","aioi.hyogo.jp","akashi.hyogo.jp","ako.hyogo.jp","amagasaki.hyogo.jp","aogaki.hyogo.jp","asago.hyogo.jp","ashiya.hyogo.jp","awaji.hyogo.jp","fukusaki.hyogo.jp","goshiki.hyogo.jp","harima.hyogo.jp","himeji.hyogo.jp","ichikawa.hyogo.jp","inagawa.hyogo.jp","itami.hyogo.jp","kakogawa.hyogo.jp","kamigori.hyogo.jp","kamikawa.hyogo.jp","kasai.hyogo.jp","kasuga.hyogo.jp","kawanishi.hyogo.jp","miki.hyogo.jp","minamiawaji.hyogo.jp","nishinomiya.hyogo.jp","nishiwaki.hyogo.jp","ono.hyogo.jp","sanda.hyogo.jp","sannan.hyogo.jp","sasayama.hyogo.jp","sayo.hyogo.jp","shingu.hyogo.jp","shinonsen.hyogo.jp","shiso.hyogo.jp","sumoto.hyogo.jp","taishi.hyogo.jp","taka.hyogo.jp","takarazuka.hyogo.jp","takasago.hyogo.jp","takino.hyogo.jp","tamba.hyogo.jp","tatsuno.hyogo.jp","toyooka.hyogo.jp","yabu.hyogo.jp","yashiro.hyogo.jp","yoka.hyogo.jp","yokawa.hyogo.jp","ami.ibaraki.jp","asahi.ibaraki.jp","bando.ibaraki.jp","chikusei.ibaraki.jp","daigo.ibaraki.jp","fujishiro.ibaraki.jp","hitachi.ibaraki.jp","hitachinaka.ibaraki.jp","hitachiomiya.ibaraki.jp","hitachiota.ibaraki.jp","ibaraki.ibaraki.jp","ina.ibaraki.jp","inashiki.ibaraki.jp","itako.ibaraki.jp","iwama.ibaraki.jp","joso.ibaraki.jp","kamisu.ibaraki.jp","kasama.ibaraki.jp","kashima.ibaraki.jp","kasumigaura.ibaraki.jp","koga.ibaraki.jp","miho.ibaraki.jp","mito.ibaraki.jp","moriya.ibaraki.jp","naka.ibaraki.jp","namegata.ibaraki.jp","oarai.ibaraki.jp","ogawa.ibaraki.jp","omitama.ibaraki.jp","ryugasaki.ibaraki.jp","sakai.ibaraki.jp","sakuragawa.ibaraki.jp","shimodate.ibaraki.jp","shimotsuma.ibaraki.jp","shirosato.ibaraki.jp","sowa.ibaraki.jp","suifu.ibaraki.jp","takahagi.ibaraki.jp","tamatsukuri.ibaraki.jp","tokai.ibaraki.jp","tomobe.ibaraki.jp","tone.ibaraki.jp","toride.ibaraki.jp","tsuchiura.ibaraki.jp","tsukuba.ibaraki.jp","uchihara.ibaraki.jp","ushiku.ibaraki.jp","yachiyo.ibaraki.jp","yamagata.ibaraki.jp","yawara.ibaraki.jp","yuki.ibaraki.jp","anamizu.ishikawa.jp","hakui.ishikawa.jp","hakusan.ishikawa.jp","kaga.ishikawa.jp","kahoku.ishikawa.jp","kanazawa.ishikawa.jp","kawakita.ishikawa.jp","komatsu.ishikawa.jp","nakanoto.ishikawa.jp","nanao.ishikawa.jp","nomi.ishikawa.jp","nonoichi.ishikawa.jp","noto.ishikawa.jp","shika.ishikawa.jp","suzu.ishikawa.jp","tsubata.ishikawa.jp","tsurugi.ishikawa.jp","uchinada.ishikawa.jp","wajima.ishikawa.jp","fudai.iwate.jp","fujisawa.iwate.jp","hanamaki.iwate.jp","hiraizumi.iwate.jp","hirono.iwate.jp","ichinohe.iwate.jp","ichinoseki.iwate.jp","iwaizumi.iwate.jp","iwate.iwate.jp","joboji.iwate.jp","kamaishi.iwate.jp","kanegasaki.iwate.jp","karumai.iwate.jp","kawai.iwate.jp","kitakami.iwate.jp","kuji.iwate.jp","kunohe.iwate.jp","kuzumaki.iwate.jp","miyako.iwate.jp","mizusawa.iwate.jp","morioka.iwate.jp","ninohe.iwate.jp","noda.iwate.jp","ofunato.iwate.jp","oshu.iwate.jp","otsuchi.iwate.jp","rikuzentakata.iwate.jp","shiwa.iwate.jp","shizukuishi.iwate.jp","sumita.iwate.jp","tanohata.iwate.jp","tono.iwate.jp","yahaba.iwate.jp","yamada.iwate.jp","ayagawa.kagawa.jp","higashikagawa.kagawa.jp","kanonji.kagawa.jp","kotohira.kagawa.jp","manno.kagawa.jp","marugame.kagawa.jp","mitoyo.kagawa.jp","naoshima.kagawa.jp","sanuki.kagawa.jp","tadotsu.kagawa.jp","takamatsu.kagawa.jp","tonosho.kagawa.jp","uchinomi.kagawa.jp","utazu.kagawa.jp","zentsuji.kagawa.jp","akune.kagoshima.jp","amami.kagoshima.jp","hioki.kagoshima.jp","isa.kagoshima.jp","isen.kagoshima.jp","izumi.kagoshima.jp","kagoshima.kagoshima.jp","kanoya.kagoshima.jp","kawanabe.kagoshima.jp","kinko.kagoshima.jp","kouyama.kagoshima.jp","makurazaki.kagoshima.jp","matsumoto.kagoshima.jp","minamitane.kagoshima.jp","nakatane.kagoshima.jp","nishinoomote.kagoshima.jp","satsumasendai.kagoshima.jp","soo.kagoshima.jp","tarumizu.kagoshima.jp","yusui.kagoshima.jp","aikawa.kanagawa.jp","atsugi.kanagawa.jp","ayase.kanagawa.jp","chigasaki.kanagawa.jp","ebina.kanagawa.jp","fujisawa.kanagawa.jp","hadano.kanagawa.jp","hakone.kanagawa.jp","hiratsuka.kanagawa.jp","isehara.kanagawa.jp","kaisei.kanagawa.jp","kamakura.kanagawa.jp","kiyokawa.kanagawa.jp","matsuda.kanagawa.jp","minamiashigara.kanagawa.jp","miura.kanagawa.jp","nakai.kanagawa.jp","ninomiya.kanagawa.jp","odawara.kanagawa.jp","oi.kanagawa.jp","oiso.kanagawa.jp","sagamihara.kanagawa.jp","samukawa.kanagawa.jp","tsukui.kanagawa.jp","yamakita.kanagawa.jp","yamato.kanagawa.jp","yokosuka.kanagawa.jp","yugawara.kanagawa.jp","zama.kanagawa.jp","zushi.kanagawa.jp","aki.kochi.jp","geisei.kochi.jp","hidaka.kochi.jp","higashitsuno.kochi.jp","ino.kochi.jp","kagami.kochi.jp","kami.kochi.jp","kitagawa.kochi.jp","kochi.kochi.jp","mihara.kochi.jp","motoyama.kochi.jp","muroto.kochi.jp","nahari.kochi.jp","nakamura.kochi.jp","nankoku.kochi.jp","nishitosa.kochi.jp","niyodogawa.kochi.jp","ochi.kochi.jp","okawa.kochi.jp","otoyo.kochi.jp","otsuki.kochi.jp","sakawa.kochi.jp","sukumo.kochi.jp","susaki.kochi.jp","tosa.kochi.jp","tosashimizu.kochi.jp","toyo.kochi.jp","tsuno.kochi.jp","umaji.kochi.jp","yasuda.kochi.jp","yusuhara.kochi.jp","amakusa.kumamoto.jp","arao.kumamoto.jp","aso.kumamoto.jp","choyo.kumamoto.jp","gyokuto.kumamoto.jp","kamiamakusa.kumamoto.jp","kikuchi.kumamoto.jp","kumamoto.kumamoto.jp","mashiki.kumamoto.jp","mifune.kumamoto.jp","minamata.kumamoto.jp","minamioguni.kumamoto.jp","nagasu.kumamoto.jp","nishihara.kumamoto.jp","oguni.kumamoto.jp","ozu.kumamoto.jp","sumoto.kumamoto.jp","takamori.kumamoto.jp","uki.kumamoto.jp","uto.kumamoto.jp","yamaga.kumamoto.jp","yamato.kumamoto.jp","yatsushiro.kumamoto.jp","ayabe.kyoto.jp","fukuchiyama.kyoto.jp","higashiyama.kyoto.jp","ide.kyoto.jp","ine.kyoto.jp","joyo.kyoto.jp","kameoka.kyoto.jp","kamo.kyoto.jp","kita.kyoto.jp","kizu.kyoto.jp","kumiyama.kyoto.jp","kyotamba.kyoto.jp","kyotanabe.kyoto.jp","kyotango.kyoto.jp","maizuru.kyoto.jp","minami.kyoto.jp","minamiyamashiro.kyoto.jp","miyazu.kyoto.jp","muko.kyoto.jp","nagaokakyo.kyoto.jp","nakagyo.kyoto.jp","nantan.kyoto.jp","oyamazaki.kyoto.jp","sakyo.kyoto.jp","seika.kyoto.jp","tanabe.kyoto.jp","uji.kyoto.jp","ujitawara.kyoto.jp","wazuka.kyoto.jp","yamashina.kyoto.jp","yawata.kyoto.jp","asahi.mie.jp","inabe.mie.jp","ise.mie.jp","kameyama.mie.jp","kawagoe.mie.jp","kiho.mie.jp","kisosaki.mie.jp","kiwa.mie.jp","komono.mie.jp","kumano.mie.jp","kuwana.mie.jp","matsusaka.mie.jp","meiwa.mie.jp","mihama.mie.jp","minamiise.mie.jp","misugi.mie.jp","miyama.mie.jp","nabari.mie.jp","shima.mie.jp","suzuka.mie.jp","tado.mie.jp","taiki.mie.jp","taki.mie.jp","tamaki.mie.jp","toba.mie.jp","tsu.mie.jp","udono.mie.jp","ureshino.mie.jp","watarai.mie.jp","yokkaichi.mie.jp","furukawa.miyagi.jp","higashimatsushima.miyagi.jp","ishinomaki.miyagi.jp","iwanuma.miyagi.jp","kakuda.miyagi.jp","kami.miyagi.jp","kawasaki.miyagi.jp","marumori.miyagi.jp","matsushima.miyagi.jp","minamisanriku.miyagi.jp","misato.miyagi.jp","murata.miyagi.jp","natori.miyagi.jp","ogawara.miyagi.jp","ohira.miyagi.jp","onagawa.miyagi.jp","osaki.miyagi.jp","rifu.miyagi.jp","semine.miyagi.jp","shibata.miyagi.jp","shichikashuku.miyagi.jp","shikama.miyagi.jp","shiogama.miyagi.jp","shiroishi.miyagi.jp","tagajo.miyagi.jp","taiwa.miyagi.jp","tome.miyagi.jp","tomiya.miyagi.jp","wakuya.miyagi.jp","watari.miyagi.jp","yamamoto.miyagi.jp","zao.miyagi.jp","aya.miyazaki.jp","ebino.miyazaki.jp","gokase.miyazaki.jp","hyuga.miyazaki.jp","kadogawa.miyazaki.jp","kawaminami.miyazaki.jp","kijo.miyazaki.jp","kitagawa.miyazaki.jp","kitakata.miyazaki.jp","kitaura.miyazaki.jp","kobayashi.miyazaki.jp","kunitomi.miyazaki.jp","kushima.miyazaki.jp","mimata.miyazaki.jp","miyakonojo.miyazaki.jp","miyazaki.miyazaki.jp","morotsuka.miyazaki.jp","nichinan.miyazaki.jp","nishimera.miyazaki.jp","nobeoka.miyazaki.jp","saito.miyazaki.jp","shiiba.miyazaki.jp","shintomi.miyazaki.jp","takaharu.miyazaki.jp","takanabe.miyazaki.jp","takazaki.miyazaki.jp","tsuno.miyazaki.jp","achi.nagano.jp","agematsu.nagano.jp","anan.nagano.jp","aoki.nagano.jp","asahi.nagano.jp","azumino.nagano.jp","chikuhoku.nagano.jp","chikuma.nagano.jp","chino.nagano.jp","fujimi.nagano.jp","hakuba.nagano.jp","hara.nagano.jp","hiraya.nagano.jp","iida.nagano.jp","iijima.nagano.jp","iiyama.nagano.jp","iizuna.nagano.jp","ikeda.nagano.jp","ikusaka.nagano.jp","ina.nagano.jp","karuizawa.nagano.jp","kawakami.nagano.jp","kiso.nagano.jp","kisofukushima.nagano.jp","kitaaiki.nagano.jp","komagane.nagano.jp","komoro.nagano.jp","matsukawa.nagano.jp","matsumoto.nagano.jp","miasa.nagano.jp","minamiaiki.nagano.jp","minamimaki.nagano.jp","minamiminowa.nagano.jp","minowa.nagano.jp","miyada.nagano.jp","miyota.nagano.jp","mochizuki.nagano.jp","nagano.nagano.jp","nagawa.nagano.jp","nagiso.nagano.jp","nakagawa.nagano.jp","nakano.nagano.jp","nozawaonsen.nagano.jp","obuse.nagano.jp","ogawa.nagano.jp","okaya.nagano.jp","omachi.nagano.jp","omi.nagano.jp","ookuwa.nagano.jp","ooshika.nagano.jp","otaki.nagano.jp","otari.nagano.jp","sakae.nagano.jp","sakaki.nagano.jp","saku.nagano.jp","sakuho.nagano.jp","shimosuwa.nagano.jp","shinanomachi.nagano.jp","shiojiri.nagano.jp","suwa.nagano.jp","suzaka.nagano.jp","takagi.nagano.jp","takamori.nagano.jp","takayama.nagano.jp","tateshina.nagano.jp","tatsuno.nagano.jp","togakushi.nagano.jp","togura.nagano.jp","tomi.nagano.jp","ueda.nagano.jp","wada.nagano.jp","yamagata.nagano.jp","yamanouchi.nagano.jp","yasaka.nagano.jp","yasuoka.nagano.jp","chijiwa.nagasaki.jp","futsu.nagasaki.jp","goto.nagasaki.jp","hasami.nagasaki.jp","hirado.nagasaki.jp","iki.nagasaki.jp","isahaya.nagasaki.jp","kawatana.nagasaki.jp","kuchinotsu.nagasaki.jp","matsuura.nagasaki.jp","nagasaki.nagasaki.jp","obama.nagasaki.jp","omura.nagasaki.jp","oseto.nagasaki.jp","saikai.nagasaki.jp","sasebo.nagasaki.jp","seihi.nagasaki.jp","shimabara.nagasaki.jp","shinkamigoto.nagasaki.jp","togitsu.nagasaki.jp","tsushima.nagasaki.jp","unzen.nagasaki.jp","ando.nara.jp","gose.nara.jp","heguri.nara.jp","higashiyoshino.nara.jp","ikaruga.nara.jp","ikoma.nara.jp","kamikitayama.nara.jp","kanmaki.nara.jp","kashiba.nara.jp","kashihara.nara.jp","katsuragi.nara.jp","kawai.nara.jp","kawakami.nara.jp","kawanishi.nara.jp","koryo.nara.jp","kurotaki.nara.jp","mitsue.nara.jp","miyake.nara.jp","nara.nara.jp","nosegawa.nara.jp","oji.nara.jp","ouda.nara.jp","oyodo.nara.jp","sakurai.nara.jp","sango.nara.jp","shimoichi.nara.jp","shimokitayama.nara.jp","shinjo.nara.jp","soni.nara.jp","takatori.nara.jp","tawaramoto.nara.jp","tenkawa.nara.jp","tenri.nara.jp","uda.nara.jp","yamatokoriyama.nara.jp","yamatotakada.nara.jp","yamazoe.nara.jp","yoshino.nara.jp","aga.niigata.jp","agano.niigata.jp","gosen.niigata.jp","itoigawa.niigata.jp","izumozaki.niigata.jp","joetsu.niigata.jp","kamo.niigata.jp","kariwa.niigata.jp","kashiwazaki.niigata.jp","minamiuonuma.niigata.jp","mitsuke.niigata.jp","muika.niigata.jp","murakami.niigata.jp","myoko.niigata.jp","nagaoka.niigata.jp","niigata.niigata.jp","ojiya.niigata.jp","omi.niigata.jp","sado.niigata.jp","sanjo.niigata.jp","seiro.niigata.jp","seirou.niigata.jp","sekikawa.niigata.jp","shibata.niigata.jp","tagami.niigata.jp","tainai.niigata.jp","tochio.niigata.jp","tokamachi.niigata.jp","tsubame.niigata.jp","tsunan.niigata.jp","uonuma.niigata.jp","yahiko.niigata.jp","yoita.niigata.jp","yuzawa.niigata.jp","beppu.oita.jp","bungoono.oita.jp","bungotakada.oita.jp","hasama.oita.jp","hiji.oita.jp","himeshima.oita.jp","hita.oita.jp","kamitsue.oita.jp","kokonoe.oita.jp","kuju.oita.jp","kunisaki.oita.jp","kusu.oita.jp","oita.oita.jp","saiki.oita.jp","taketa.oita.jp","tsukumi.oita.jp","usa.oita.jp","usuki.oita.jp","yufu.oita.jp","akaiwa.okayama.jp","asakuchi.okayama.jp","bizen.okayama.jp","hayashima.okayama.jp","ibara.okayama.jp","kagamino.okayama.jp","kasaoka.okayama.jp","kibichuo.okayama.jp","kumenan.okayama.jp","kurashiki.okayama.jp","maniwa.okayama.jp","misaki.okayama.jp","nagi.okayama.jp","niimi.okayama.jp","nishiawakura.okayama.jp","okayama.okayama.jp","satosho.okayama.jp","setouchi.okayama.jp","shinjo.okayama.jp","shoo.okayama.jp","soja.okayama.jp","takahashi.okayama.jp","tamano.okayama.jp","tsuyama.okayama.jp","wake.okayama.jp","yakage.okayama.jp","aguni.okinawa.jp","ginowan.okinawa.jp","ginoza.okinawa.jp","gushikami.okinawa.jp","haebaru.okinawa.jp","higashi.okinawa.jp","hirara.okinawa.jp","iheya.okinawa.jp","ishigaki.okinawa.jp","ishikawa.okinawa.jp","itoman.okinawa.jp","izena.okinawa.jp","kadena.okinawa.jp","kin.okinawa.jp","kitadaito.okinawa.jp","kitanakagusuku.okinawa.jp","kumejima.okinawa.jp","kunigami.okinawa.jp","minamidaito.okinawa.jp","motobu.okinawa.jp","nago.okinawa.jp","naha.okinawa.jp","nakagusuku.okinawa.jp","nakijin.okinawa.jp","nanjo.okinawa.jp","nishihara.okinawa.jp","ogimi.okinawa.jp","okinawa.okinawa.jp","onna.okinawa.jp","shimoji.okinawa.jp","taketomi.okinawa.jp","tarama.okinawa.jp","tokashiki.okinawa.jp","tomigusuku.okinawa.jp","tonaki.okinawa.jp","urasoe.okinawa.jp","uruma.okinawa.jp","yaese.okinawa.jp","yomitan.okinawa.jp","yonabaru.okinawa.jp","yonaguni.okinawa.jp","zamami.okinawa.jp","abeno.osaka.jp","chihayaakasaka.osaka.jp","chuo.osaka.jp","daito.osaka.jp","fujiidera.osaka.jp","habikino.osaka.jp","hannan.osaka.jp","higashiosaka.osaka.jp","higashisumiyoshi.osaka.jp","higashiyodogawa.osaka.jp","hirakata.osaka.jp","ibaraki.osaka.jp","ikeda.osaka.jp","izumi.osaka.jp","izumiotsu.osaka.jp","izumisano.osaka.jp","kadoma.osaka.jp","kaizuka.osaka.jp","kanan.osaka.jp","kashiwara.osaka.jp","katano.osaka.jp","kawachinagano.osaka.jp","kishiwada.osaka.jp","kita.osaka.jp","kumatori.osaka.jp","matsubara.osaka.jp","minato.osaka.jp","minoh.osaka.jp","misaki.osaka.jp","moriguchi.osaka.jp","neyagawa.osaka.jp","nishi.osaka.jp","nose.osaka.jp","osakasayama.osaka.jp","sakai.osaka.jp","sayama.osaka.jp","sennan.osaka.jp","settsu.osaka.jp","shijonawate.osaka.jp","shimamoto.osaka.jp","suita.osaka.jp","tadaoka.osaka.jp","taishi.osaka.jp","tajiri.osaka.jp","takaishi.osaka.jp","takatsuki.osaka.jp","tondabayashi.osaka.jp","toyonaka.osaka.jp","toyono.osaka.jp","yao.osaka.jp","ariake.saga.jp","arita.saga.jp","fukudomi.saga.jp","genkai.saga.jp","hamatama.saga.jp","hizen.saga.jp","imari.saga.jp","kamimine.saga.jp","kanzaki.saga.jp","karatsu.saga.jp","kashima.saga.jp","kitagata.saga.jp","kitahata.saga.jp","kiyama.saga.jp","kouhoku.saga.jp","kyuragi.saga.jp","nishiarita.saga.jp","ogi.saga.jp","omachi.saga.jp","ouchi.saga.jp","saga.saga.jp","shiroishi.saga.jp","taku.saga.jp","tara.saga.jp","tosu.saga.jp","yoshinogari.saga.jp","arakawa.saitama.jp","asaka.saitama.jp","chichibu.saitama.jp","fujimi.saitama.jp","fujimino.saitama.jp","fukaya.saitama.jp","hanno.saitama.jp","hanyu.saitama.jp","hasuda.saitama.jp","hatogaya.saitama.jp","hatoyama.saitama.jp","hidaka.saitama.jp","higashichichibu.saitama.jp","higashimatsuyama.saitama.jp","honjo.saitama.jp","ina.saitama.jp","iruma.saitama.jp","iwatsuki.saitama.jp","kamiizumi.saitama.jp","kamikawa.saitama.jp","kamisato.saitama.jp","kasukabe.saitama.jp","kawagoe.saitama.jp","kawaguchi.saitama.jp","kawajima.saitama.jp","kazo.saitama.jp","kitamoto.saitama.jp","koshigaya.saitama.jp","kounosu.saitama.jp","kuki.saitama.jp","kumagaya.saitama.jp","matsubushi.saitama.jp","minano.saitama.jp","misato.saitama.jp","miyashiro.saitama.jp","miyoshi.saitama.jp","moroyama.saitama.jp","nagatoro.saitama.jp","namegawa.saitama.jp","niiza.saitama.jp","ogano.saitama.jp","ogawa.saitama.jp","ogose.saitama.jp","okegawa.saitama.jp","omiya.saitama.jp","otaki.saitama.jp","ranzan.saitama.jp","ryokami.saitama.jp","saitama.saitama.jp","sakado.saitama.jp","satte.saitama.jp","sayama.saitama.jp","shiki.saitama.jp","shiraoka.saitama.jp","soka.saitama.jp","sugito.saitama.jp","toda.saitama.jp","tokigawa.saitama.jp","tokorozawa.saitama.jp","tsurugashima.saitama.jp","urawa.saitama.jp","warabi.saitama.jp","yashio.saitama.jp","yokoze.saitama.jp","yono.saitama.jp","yorii.saitama.jp","yoshida.saitama.jp","yoshikawa.saitama.jp","yoshimi.saitama.jp","aisho.shiga.jp","gamo.shiga.jp","higashiomi.shiga.jp","hikone.shiga.jp","koka.shiga.jp","konan.shiga.jp","kosei.shiga.jp","koto.shiga.jp","kusatsu.shiga.jp","maibara.shiga.jp","moriyama.shiga.jp","nagahama.shiga.jp","nishiazai.shiga.jp","notogawa.shiga.jp","omihachiman.shiga.jp","otsu.shiga.jp","ritto.shiga.jp","ryuoh.shiga.jp","takashima.shiga.jp","takatsuki.shiga.jp","torahime.shiga.jp","toyosato.shiga.jp","yasu.shiga.jp","akagi.shimane.jp","ama.shimane.jp","gotsu.shimane.jp","hamada.shimane.jp","higashiizumo.shimane.jp","hikawa.shimane.jp","hikimi.shimane.jp","izumo.shimane.jp","kakinoki.shimane.jp","masuda.shimane.jp","matsue.shimane.jp","misato.shimane.jp","nishinoshima.shimane.jp","ohda.shimane.jp","okinoshima.shimane.jp","okuizumo.shimane.jp","shimane.shimane.jp","tamayu.shimane.jp","tsuwano.shimane.jp","unnan.shimane.jp","yakumo.shimane.jp","yasugi.shimane.jp","yatsuka.shimane.jp","arai.shizuoka.jp","atami.shizuoka.jp","fuji.shizuoka.jp","fujieda.shizuoka.jp","fujikawa.shizuoka.jp","fujinomiya.shizuoka.jp","fukuroi.shizuoka.jp","gotemba.shizuoka.jp","haibara.shizuoka.jp","hamamatsu.shizuoka.jp","higashiizu.shizuoka.jp","ito.shizuoka.jp","iwata.shizuoka.jp","izu.shizuoka.jp","izunokuni.shizuoka.jp","kakegawa.shizuoka.jp","kannami.shizuoka.jp","kawanehon.shizuoka.jp","kawazu.shizuoka.jp","kikugawa.shizuoka.jp","kosai.shizuoka.jp","makinohara.shizuoka.jp","matsuzaki.shizuoka.jp","minamiizu.shizuoka.jp","mishima.shizuoka.jp","morimachi.shizuoka.jp","nishiizu.shizuoka.jp","numazu.shizuoka.jp","omaezaki.shizuoka.jp","shimada.shizuoka.jp","shimizu.shizuoka.jp","shimoda.shizuoka.jp","shizuoka.shizuoka.jp","susono.shizuoka.jp","yaizu.shizuoka.jp","yoshida.shizuoka.jp","ashikaga.tochigi.jp","bato.tochigi.jp","haga.tochigi.jp","ichikai.tochigi.jp","iwafune.tochigi.jp","kaminokawa.tochigi.jp","kanuma.tochigi.jp","karasuyama.tochigi.jp","kuroiso.tochigi.jp","mashiko.tochigi.jp","mibu.tochigi.jp","moka.tochigi.jp","motegi.tochigi.jp","nasu.tochigi.jp","nasushiobara.tochigi.jp","nikko.tochigi.jp","nishikata.tochigi.jp","nogi.tochigi.jp","ohira.tochigi.jp","ohtawara.tochigi.jp","oyama.tochigi.jp","sakura.tochigi.jp","sano.tochigi.jp","shimotsuke.tochigi.jp","shioya.tochigi.jp","takanezawa.tochigi.jp","tochigi.tochigi.jp","tsuga.tochigi.jp","ujiie.tochigi.jp","utsunomiya.tochigi.jp","yaita.tochigi.jp","aizumi.tokushima.jp","anan.tokushima.jp","ichiba.tokushima.jp","itano.tokushima.jp","kainan.tokushima.jp","komatsushima.tokushima.jp","matsushige.tokushima.jp","mima.tokushima.jp","minami.tokushima.jp","miyoshi.tokushima.jp","mugi.tokushima.jp","nakagawa.tokushima.jp","naruto.tokushima.jp","sanagochi.tokushima.jp","shishikui.tokushima.jp","tokushima.tokushima.jp","wajiki.tokushima.jp","adachi.tokyo.jp","akiruno.tokyo.jp","akishima.tokyo.jp","aogashima.tokyo.jp","arakawa.tokyo.jp","bunkyo.tokyo.jp","chiyoda.tokyo.jp","chofu.tokyo.jp","chuo.tokyo.jp","edogawa.tokyo.jp","fuchu.tokyo.jp","fussa.tokyo.jp","hachijo.tokyo.jp","hachioji.tokyo.jp","hamura.tokyo.jp","higashikurume.tokyo.jp","higashimurayama.tokyo.jp","higashiyamato.tokyo.jp","hino.tokyo.jp","hinode.tokyo.jp","hinohara.tokyo.jp","inagi.tokyo.jp","itabashi.tokyo.jp","katsushika.tokyo.jp","kita.tokyo.jp","kiyose.tokyo.jp","kodaira.tokyo.jp","koganei.tokyo.jp","kokubunji.tokyo.jp","komae.tokyo.jp","koto.tokyo.jp","kouzushima.tokyo.jp","kunitachi.tokyo.jp","machida.tokyo.jp","meguro.tokyo.jp","minato.tokyo.jp","mitaka.tokyo.jp","mizuho.tokyo.jp","musashimurayama.tokyo.jp","musashino.tokyo.jp","nakano.tokyo.jp","nerima.tokyo.jp","ogasawara.tokyo.jp","okutama.tokyo.jp","ome.tokyo.jp","oshima.tokyo.jp","ota.tokyo.jp","setagaya.tokyo.jp","shibuya.tokyo.jp","shinagawa.tokyo.jp","shinjuku.tokyo.jp","suginami.tokyo.jp","sumida.tokyo.jp","tachikawa.tokyo.jp","taito.tokyo.jp","tama.tokyo.jp","toshima.tokyo.jp","chizu.tottori.jp","hino.tottori.jp","kawahara.tottori.jp","koge.tottori.jp","kotoura.tottori.jp","misasa.tottori.jp","nanbu.tottori.jp","nichinan.tottori.jp","sakaiminato.tottori.jp","tottori.tottori.jp","wakasa.tottori.jp","yazu.tottori.jp","yonago.tottori.jp","asahi.toyama.jp","fuchu.toyama.jp","fukumitsu.toyama.jp","funahashi.toyama.jp","himi.toyama.jp","imizu.toyama.jp","inami.toyama.jp","johana.toyama.jp","kamiichi.toyama.jp","kurobe.toyama.jp","nakaniikawa.toyama.jp","namerikawa.toyama.jp","nanto.toyama.jp","nyuzen.toyama.jp","oyabe.toyama.jp","taira.toyama.jp","takaoka.toyama.jp","tateyama.toyama.jp","toga.toyama.jp","tonami.toyama.jp","toyama.toyama.jp","unazuki.toyama.jp","uozu.toyama.jp","yamada.toyama.jp","arida.wakayama.jp","aridagawa.wakayama.jp","gobo.wakayama.jp","hashimoto.wakayama.jp","hidaka.wakayama.jp","hirogawa.wakayama.jp","inami.wakayama.jp","iwade.wakayama.jp","kainan.wakayama.jp","kamitonda.wakayama.jp","katsuragi.wakayama.jp","kimino.wakayama.jp","kinokawa.wakayama.jp","kitayama.wakayama.jp","koya.wakayama.jp","koza.wakayama.jp","kozagawa.wakayama.jp","kudoyama.wakayama.jp","kushimoto.wakayama.jp","mihama.wakayama.jp","misato.wakayama.jp","nachikatsuura.wakayama.jp","shingu.wakayama.jp","shirahama.wakayama.jp","taiji.wakayama.jp","tanabe.wakayama.jp","wakayama.wakayama.jp","yuasa.wakayama.jp","yura.wakayama.jp","asahi.yamagata.jp","funagata.yamagata.jp","higashine.yamagata.jp","iide.yamagata.jp","kahoku.yamagata.jp","kaminoyama.yamagata.jp","kaneyama.yamagata.jp","kawanishi.yamagata.jp","mamurogawa.yamagata.jp","mikawa.yamagata.jp","murayama.yamagata.jp","nagai.yamagata.jp","nakayama.yamagata.jp","nanyo.yamagata.jp","nishikawa.yamagata.jp","obanazawa.yamagata.jp","oe.yamagata.jp","oguni.yamagata.jp","ohkura.yamagata.jp","oishida.yamagata.jp","sagae.yamagata.jp","sakata.yamagata.jp","sakegawa.yamagata.jp","shinjo.yamagata.jp","shirataka.yamagata.jp","shonai.yamagata.jp","takahata.yamagata.jp","tendo.yamagata.jp","tozawa.yamagata.jp","tsuruoka.yamagata.jp","yamagata.yamagata.jp","yamanobe.yamagata.jp","yonezawa.yamagata.jp","yuza.yamagata.jp","abu.yamaguchi.jp","hagi.yamaguchi.jp","hikari.yamaguchi.jp","hofu.yamaguchi.jp","iwakuni.yamaguchi.jp","kudamatsu.yamaguchi.jp","mitou.yamaguchi.jp","nagato.yamaguchi.jp","oshima.yamaguchi.jp","shimonoseki.yamaguchi.jp","shunan.yamaguchi.jp","tabuse.yamaguchi.jp","tokuyama.yamaguchi.jp","toyota.yamaguchi.jp","ube.yamaguchi.jp","yuu.yamaguchi.jp","chuo.yamanashi.jp","doshi.yamanashi.jp","fuefuki.yamanashi.jp","fujikawa.yamanashi.jp","fujikawaguchiko.yamanashi.jp","fujiyoshida.yamanashi.jp","hayakawa.yamanashi.jp","hokuto.yamanashi.jp","ichikawamisato.yamanashi.jp","kai.yamanashi.jp","kofu.yamanashi.jp","koshu.yamanashi.jp","kosuge.yamanashi.jp","minami-alps.yamanashi.jp","minobu.yamanashi.jp","nakamichi.yamanashi.jp","nanbu.yamanashi.jp","narusawa.yamanashi.jp","nirasaki.yamanashi.jp","nishikatsura.yamanashi.jp","oshino.yamanashi.jp","otsuki.yamanashi.jp","showa.yamanashi.jp","tabayama.yamanashi.jp","tsuru.yamanashi.jp","uenohara.yamanashi.jp","yamanakako.yamanashi.jp","yamanashi.yamanashi.jp","ke","ac.ke","co.ke","go.ke","info.ke","me.ke","mobi.ke","ne.ke","or.ke","sc.ke","kg","org.kg","net.kg","com.kg","edu.kg","gov.kg","mil.kg","*.kh","ki","edu.ki","biz.ki","net.ki","org.ki","gov.ki","info.ki","com.ki","km","org.km","nom.km","gov.km","prd.km","tm.km","edu.km","mil.km","ass.km","com.km","coop.km","asso.km","presse.km","medecin.km","notaires.km","pharmaciens.km","veterinaire.km","gouv.km","kn","net.kn","org.kn","edu.kn","gov.kn","kp","com.kp","edu.kp","gov.kp","org.kp","rep.kp","tra.kp","kr","ac.kr","co.kr","es.kr","go.kr","hs.kr","kg.kr","mil.kr","ms.kr","ne.kr","or.kr","pe.kr","re.kr","sc.kr","busan.kr","chungbuk.kr","chungnam.kr","daegu.kr","daejeon.kr","gangwon.kr","gwangju.kr","gyeongbuk.kr","gyeonggi.kr","gyeongnam.kr","incheon.kr","jeju.kr","jeonbuk.kr","jeonnam.kr","seoul.kr","ulsan.kr","kw","com.kw","edu.kw","emb.kw","gov.kw","ind.kw","net.kw","org.kw","ky","com.ky","edu.ky","net.ky","org.ky","kz","org.kz","edu.kz","net.kz","gov.kz","mil.kz","com.kz","la","int.la","net.la","info.la","edu.la","gov.la","per.la","com.la","org.la","lb","com.lb","edu.lb","gov.lb","net.lb","org.lb","lc","com.lc","net.lc","co.lc","org.lc","edu.lc","gov.lc","li","lk","gov.lk","sch.lk","net.lk","int.lk","com.lk","org.lk","edu.lk","ngo.lk","soc.lk","web.lk","ltd.lk","assn.lk","grp.lk","hotel.lk","ac.lk","lr","com.lr","edu.lr","gov.lr","org.lr","net.lr","ls","ac.ls","biz.ls","co.ls","edu.ls","gov.ls","info.ls","net.ls","org.ls","sc.ls","lt","gov.lt","lu","lv","com.lv","edu.lv","gov.lv","org.lv","mil.lv","id.lv","net.lv","asn.lv","conf.lv","ly","com.ly","net.ly","gov.ly","plc.ly","edu.ly","sch.ly","med.ly","org.ly","id.ly","ma","co.ma","net.ma","gov.ma","org.ma","ac.ma","press.ma","mc","tm.mc","asso.mc","md","me","co.me","net.me","org.me","edu.me","ac.me","gov.me","its.me","priv.me","mg","org.mg","nom.mg","gov.mg","prd.mg","tm.mg","edu.mg","mil.mg","com.mg","co.mg","mh","mil","mk","com.mk","org.mk","net.mk","edu.mk","gov.mk","inf.mk","name.mk","ml","com.ml","edu.ml","gouv.ml","gov.ml","net.ml","org.ml","presse.ml","*.mm","mn","gov.mn","edu.mn","org.mn","mo","com.mo","net.mo","org.mo","edu.mo","gov.mo","mobi","mp","mq","mr","gov.mr","ms","com.ms","edu.ms","gov.ms","net.ms","org.ms","mt","com.mt","edu.mt","net.mt","org.mt","mu","com.mu","net.mu","org.mu","gov.mu","ac.mu","co.mu","or.mu","museum","academy.museum","agriculture.museum","air.museum","airguard.museum","alabama.museum","alaska.museum","amber.museum","ambulance.museum","american.museum","americana.museum","americanantiques.museum","americanart.museum","amsterdam.museum","and.museum","annefrank.museum","anthro.museum","anthropology.museum","antiques.museum","aquarium.museum","arboretum.museum","archaeological.museum","archaeology.museum","architecture.museum","art.museum","artanddesign.museum","artcenter.museum","artdeco.museum","arteducation.museum","artgallery.museum","arts.museum","artsandcrafts.museum","asmatart.museum","assassination.museum","assisi.museum","association.museum","astronomy.museum","atlanta.museum","austin.museum","australia.museum","automotive.museum","aviation.museum","axis.museum","badajoz.museum","baghdad.museum","bahn.museum","bale.museum","baltimore.museum","barcelona.museum","baseball.museum","basel.museum","baths.museum","bauern.museum","beauxarts.museum","beeldengeluid.museum","bellevue.museum","bergbau.museum","berkeley.museum","berlin.museum","bern.museum","bible.museum","bilbao.museum","bill.museum","birdart.museum","birthplace.museum","bonn.museum","boston.museum","botanical.museum","botanicalgarden.museum","botanicgarden.museum","botany.museum","brandywinevalley.museum","brasil.museum","bristol.museum","british.museum","britishcolumbia.museum","broadcast.museum","brunel.museum","brussel.museum","brussels.museum","bruxelles.museum","building.museum","burghof.museum","bus.museum","bushey.museum","cadaques.museum","california.museum","cambridge.museum","can.museum","canada.museum","capebreton.museum","carrier.museum","cartoonart.museum","casadelamoneda.museum","castle.museum","castres.museum","celtic.museum","center.museum","chattanooga.museum","cheltenham.museum","chesapeakebay.museum","chicago.museum","children.museum","childrens.museum","childrensgarden.museum","chiropractic.museum","chocolate.museum","christiansburg.museum","cincinnati.museum","cinema.museum","circus.museum","civilisation.museum","civilization.museum","civilwar.museum","clinton.museum","clock.museum","coal.museum","coastaldefence.museum","cody.museum","coldwar.museum","collection.museum","colonialwilliamsburg.museum","coloradoplateau.museum","columbia.museum","columbus.museum","communication.museum","communications.museum","community.museum","computer.museum","computerhistory.museum","comunicações.museum","contemporary.museum","contemporaryart.museum","convent.museum","copenhagen.museum","corporation.museum","correios-e-telecomunicações.museum","corvette.museum","costume.museum","countryestate.museum","county.museum","crafts.museum","cranbrook.museum","creation.museum","cultural.museum","culturalcenter.museum","culture.museum","cyber.museum","cymru.museum","dali.museum","dallas.museum","database.museum","ddr.museum","decorativearts.museum","delaware.museum","delmenhorst.museum","denmark.museum","depot.museum","design.museum","detroit.museum","dinosaur.museum","discovery.museum","dolls.museum","donostia.museum","durham.museum","eastafrica.museum","eastcoast.museum","education.museum","educational.museum","egyptian.museum","eisenbahn.museum","elburg.museum","elvendrell.museum","embroidery.museum","encyclopedic.museum","england.museum","entomology.museum","environment.museum","environmentalconservation.museum","epilepsy.museum","essex.museum","estate.museum","ethnology.museum","exeter.museum","exhibition.museum","family.museum","farm.museum","farmequipment.museum","farmers.museum","farmstead.museum","field.museum","figueres.museum","filatelia.museum","film.museum","fineart.museum","finearts.museum","finland.museum","flanders.museum","florida.museum","force.museum","fortmissoula.museum","fortworth.museum","foundation.museum","francaise.museum","frankfurt.museum","franziskaner.museum","freemasonry.museum","freiburg.museum","fribourg.museum","frog.museum","fundacio.museum","furniture.museum","gallery.museum","garden.museum","gateway.museum","geelvinck.museum","gemological.museum","geology.museum","georgia.museum","giessen.museum","glas.museum","glass.museum","gorge.museum","grandrapids.museum","graz.museum","guernsey.museum","halloffame.museum","hamburg.museum","handson.museum","harvestcelebration.museum","hawaii.museum","health.museum","heimatunduhren.museum","hellas.museum","helsinki.museum","hembygdsforbund.museum","heritage.museum","histoire.museum","historical.museum","historicalsociety.museum","historichouses.museum","historisch.museum","historisches.museum","history.museum","historyofscience.museum","horology.museum","house.museum","humanities.museum","illustration.museum","imageandsound.museum","indian.museum","indiana.museum","indianapolis.museum","indianmarket.museum","intelligence.museum","interactive.museum","iraq.museum","iron.museum","isleofman.museum","jamison.museum","jefferson.museum","jerusalem.museum","jewelry.museum","jewish.museum","jewishart.museum","jfk.museum","journalism.museum","judaica.museum","judygarland.museum","juedisches.museum","juif.museum","karate.museum","karikatur.museum","kids.museum","koebenhavn.museum","koeln.museum","kunst.museum","kunstsammlung.museum","kunstunddesign.museum","labor.museum","labour.museum","lajolla.museum","lancashire.museum","landes.museum","lans.museum","läns.museum","larsson.museum","lewismiller.museum","lincoln.museum","linz.museum","living.museum","livinghistory.museum","localhistory.museum","london.museum","losangeles.museum","louvre.museum","loyalist.museum","lucerne.museum","luxembourg.museum","luzern.museum","mad.museum","madrid.museum","mallorca.museum","manchester.museum","mansion.museum","mansions.museum","manx.museum","marburg.museum","maritime.museum","maritimo.museum","maryland.museum","marylhurst.museum","media.museum","medical.museum","medizinhistorisches.museum","meeres.museum","memorial.museum","mesaverde.museum","michigan.museum","midatlantic.museum","military.museum","mill.museum","miners.museum","mining.museum","minnesota.museum","missile.museum","missoula.museum","modern.museum","moma.museum","money.museum","monmouth.museum","monticello.museum","montreal.museum","moscow.museum","motorcycle.museum","muenchen.museum","muenster.museum","mulhouse.museum","muncie.museum","museet.museum","museumcenter.museum","museumvereniging.museum","music.museum","national.museum","nationalfirearms.museum","nationalheritage.museum","nativeamerican.museum","naturalhistory.museum","naturalhistorymuseum.museum","naturalsciences.museum","nature.museum","naturhistorisches.museum","natuurwetenschappen.museum","naumburg.museum","naval.museum","nebraska.museum","neues.museum","newhampshire.museum","newjersey.museum","newmexico.museum","newport.museum","newspaper.museum","newyork.museum","niepce.museum","norfolk.museum","north.museum","nrw.museum","nyc.museum","nyny.museum","oceanographic.museum","oceanographique.museum","omaha.museum","online.museum","ontario.museum","openair.museum","oregon.museum","oregontrail.museum","otago.museum","oxford.museum","pacific.museum","paderborn.museum","palace.museum","paleo.museum","palmsprings.museum","panama.museum","paris.museum","pasadena.museum","pharmacy.museum","philadelphia.museum","philadelphiaarea.museum","philately.museum","phoenix.museum","photography.museum","pilots.museum","pittsburgh.museum","planetarium.museum","plantation.museum","plants.museum","plaza.museum","portal.museum","portland.museum","portlligat.museum","posts-and-telecommunications.museum","preservation.museum","presidio.museum","press.museum","project.museum","public.museum","pubol.museum","quebec.museum","railroad.museum","railway.museum","research.museum","resistance.museum","riodejaneiro.museum","rochester.museum","rockart.museum","roma.museum","russia.museum","saintlouis.museum","salem.museum","salvadordali.museum","salzburg.museum","sandiego.museum","sanfrancisco.museum","santabarbara.museum","santacruz.museum","santafe.museum","saskatchewan.museum","satx.museum","savannahga.museum","schlesisches.museum","schoenbrunn.museum","schokoladen.museum","school.museum","schweiz.museum","science.museum","scienceandhistory.museum","scienceandindustry.museum","sciencecenter.museum","sciencecenters.museum","science-fiction.museum","sciencehistory.museum","sciences.museum","sciencesnaturelles.museum","scotland.museum","seaport.museum","settlement.museum","settlers.museum","shell.museum","sherbrooke.museum","sibenik.museum","silk.museum","ski.museum","skole.museum","society.museum","sologne.museum","soundandvision.museum","southcarolina.museum","southwest.museum","space.museum","spy.museum","square.museum","stadt.museum","stalbans.museum","starnberg.museum","state.museum","stateofdelaware.museum","station.museum","steam.museum","steiermark.museum","stjohn.museum","stockholm.museum","stpetersburg.museum","stuttgart.museum","suisse.museum","surgeonshall.museum","surrey.museum","svizzera.museum","sweden.museum","sydney.museum","tank.museum","tcm.museum","technology.museum","telekommunikation.museum","television.museum","texas.museum","textile.museum","theater.museum","time.museum","timekeeping.museum","topology.museum","torino.museum","touch.museum","town.museum","transport.museum","tree.museum","trolley.museum","trust.museum","trustee.museum","uhren.museum","ulm.museum","undersea.museum","university.museum","usa.museum","usantiques.museum","usarts.museum","uscountryestate.museum","usculture.museum","usdecorativearts.museum","usgarden.museum","ushistory.museum","ushuaia.museum","uslivinghistory.museum","utah.museum","uvic.museum","valley.museum","vantaa.museum","versailles.museum","viking.museum","village.museum","virginia.museum","virtual.museum","virtuel.museum","vlaanderen.museum","volkenkunde.museum","wales.museum","wallonie.museum","war.museum","washingtondc.museum","watchandclock.museum","watch-and-clock.museum","western.museum","westfalen.museum","whaling.museum","wildlife.museum","williamsburg.museum","windmill.museum","workshop.museum","york.museum","yorkshire.museum","yosemite.museum","youth.museum","zoological.museum","zoology.museum","ירושלים.museum","иком.museum","mv","aero.mv","biz.mv","com.mv","coop.mv","edu.mv","gov.mv","info.mv","int.mv","mil.mv","museum.mv","name.mv","net.mv","org.mv","pro.mv","mw","ac.mw","biz.mw","co.mw","com.mw","coop.mw","edu.mw","gov.mw","int.mw","museum.mw","net.mw","org.mw","mx","com.mx","org.mx","gob.mx","edu.mx","net.mx","my","biz.my","com.my","edu.my","gov.my","mil.my","name.my","net.my","org.my","mz","ac.mz","adv.mz","co.mz","edu.mz","gov.mz","mil.mz","net.mz","org.mz","na","info.na","pro.na","name.na","school.na","or.na","dr.na","us.na","mx.na","ca.na","in.na","cc.na","tv.na","ws.na","mobi.na","co.na","com.na","org.na","name","nc","asso.nc","nom.nc","ne","net","nf","com.nf","net.nf","per.nf","rec.nf","web.nf","arts.nf","firm.nf","info.nf","other.nf","store.nf","ng","com.ng","edu.ng","gov.ng","i.ng","mil.ng","mobi.ng","name.ng","net.ng","org.ng","sch.ng","ni","ac.ni","biz.ni","co.ni","com.ni","edu.ni","gob.ni","in.ni","info.ni","int.ni","mil.ni","net.ni","nom.ni","org.ni","web.ni","nl","no","fhs.no","vgs.no","fylkesbibl.no","folkebibl.no","museum.no","idrett.no","priv.no","mil.no","stat.no","dep.no","kommune.no","herad.no","aa.no","ah.no","bu.no","fm.no","hl.no","hm.no","jan-mayen.no","mr.no","nl.no","nt.no","of.no","ol.no","oslo.no","rl.no","sf.no","st.no","svalbard.no","tm.no","tr.no","va.no","vf.no","gs.aa.no","gs.ah.no","gs.bu.no","gs.fm.no","gs.hl.no","gs.hm.no","gs.jan-mayen.no","gs.mr.no","gs.nl.no","gs.nt.no","gs.of.no","gs.ol.no","gs.oslo.no","gs.rl.no","gs.sf.no","gs.st.no","gs.svalbard.no","gs.tm.no","gs.tr.no","gs.va.no","gs.vf.no","akrehamn.no","åkrehamn.no","algard.no","ålgård.no","arna.no","brumunddal.no","bryne.no","bronnoysund.no","brønnøysund.no","drobak.no","drøbak.no","egersund.no","fetsund.no","floro.no","florø.no","fredrikstad.no","hokksund.no","honefoss.no","hønefoss.no","jessheim.no","jorpeland.no","jørpeland.no","kirkenes.no","kopervik.no","krokstadelva.no","langevag.no","langevåg.no","leirvik.no","mjondalen.no","mjøndalen.no","mo-i-rana.no","mosjoen.no","mosjøen.no","nesoddtangen.no","orkanger.no","osoyro.no","osøyro.no","raholt.no","råholt.no","sandnessjoen.no","sandnessjøen.no","skedsmokorset.no","slattum.no","spjelkavik.no","stathelle.no","stavern.no","stjordalshalsen.no","stjørdalshalsen.no","tananger.no","tranby.no","vossevangen.no","afjord.no","åfjord.no","agdenes.no","al.no","ål.no","alesund.no","ålesund.no","alstahaug.no","alta.no","áltá.no","alaheadju.no","álaheadju.no","alvdal.no","amli.no","åmli.no","amot.no","åmot.no","andebu.no","andoy.no","andøy.no","andasuolo.no","ardal.no","årdal.no","aremark.no","arendal.no","ås.no","aseral.no","åseral.no","asker.no","askim.no","askvoll.no","askoy.no","askøy.no","asnes.no","åsnes.no","audnedaln.no","aukra.no","aure.no","aurland.no","aurskog-holand.no","aurskog-høland.no","austevoll.no","austrheim.no","averoy.no","averøy.no","balestrand.no","ballangen.no","balat.no","bálát.no","balsfjord.no","bahccavuotna.no","báhccavuotna.no","bamble.no","bardu.no","beardu.no","beiarn.no","bajddar.no","bájddar.no","baidar.no","báidár.no","berg.no","bergen.no","berlevag.no","berlevåg.no","bearalvahki.no","bearalváhki.no","bindal.no","birkenes.no","bjarkoy.no","bjarkøy.no","bjerkreim.no","bjugn.no","bodo.no","bodø.no","badaddja.no","bådåddjå.no","budejju.no","bokn.no","bremanger.no","bronnoy.no","brønnøy.no","bygland.no","bykle.no","barum.no","bærum.no","bo.telemark.no","bø.telemark.no","bo.nordland.no","bø.nordland.no","bievat.no","bievát.no","bomlo.no","bømlo.no","batsfjord.no","båtsfjord.no","bahcavuotna.no","báhcavuotna.no","dovre.no","drammen.no","drangedal.no","dyroy.no","dyrøy.no","donna.no","dønna.no","eid.no","eidfjord.no","eidsberg.no","eidskog.no","eidsvoll.no","eigersund.no","elverum.no","enebakk.no","engerdal.no","etne.no","etnedal.no","evenes.no","evenassi.no","evenášši.no","evje-og-hornnes.no","farsund.no","fauske.no","fuossko.no","fuoisku.no","fedje.no","fet.no","finnoy.no","finnøy.no","fitjar.no","fjaler.no","fjell.no","flakstad.no","flatanger.no","flekkefjord.no","flesberg.no","flora.no","fla.no","flå.no","folldal.no","forsand.no","fosnes.no","frei.no","frogn.no","froland.no","frosta.no","frana.no","fræna.no","froya.no","frøya.no","fusa.no","fyresdal.no","forde.no","førde.no","gamvik.no","gangaviika.no","gáŋgaviika.no","gaular.no","gausdal.no","gildeskal.no","gildeskål.no","giske.no","gjemnes.no","gjerdrum.no","gjerstad.no","gjesdal.no","gjovik.no","gjøvik.no","gloppen.no","gol.no","gran.no","grane.no","granvin.no","gratangen.no","grimstad.no","grong.no","kraanghke.no","kråanghke.no","grue.no","gulen.no","hadsel.no","halden.no","halsa.no","hamar.no","hamaroy.no","habmer.no","hábmer.no","hapmir.no","hápmir.no","hammerfest.no","hammarfeasta.no","hámmárfeasta.no","haram.no","hareid.no","harstad.no","hasvik.no","aknoluokta.no","ákŋoluokta.no","hattfjelldal.no","aarborte.no","haugesund.no","hemne.no","hemnes.no","hemsedal.no","heroy.more-og-romsdal.no","herøy.møre-og-romsdal.no","heroy.nordland.no","herøy.nordland.no","hitra.no","hjartdal.no","hjelmeland.no","hobol.no","hobøl.no","hof.no","hol.no","hole.no","holmestrand.no","holtalen.no","holtålen.no","hornindal.no","horten.no","hurdal.no","hurum.no","hvaler.no","hyllestad.no","hagebostad.no","hægebostad.no","hoyanger.no","høyanger.no","hoylandet.no","høylandet.no","ha.no","hå.no","ibestad.no","inderoy.no","inderøy.no","iveland.no","jevnaker.no","jondal.no","jolster.no","jølster.no","karasjok.no","karasjohka.no","kárášjohka.no","karlsoy.no","galsa.no","gálsá.no","karmoy.no","karmøy.no","kautokeino.no","guovdageaidnu.no","klepp.no","klabu.no","klæbu.no","kongsberg.no","kongsvinger.no","kragero.no","kragerø.no","kristiansand.no","kristiansund.no","krodsherad.no","krødsherad.no","kvalsund.no","rahkkeravju.no","ráhkkerávju.no","kvam.no","kvinesdal.no","kvinnherad.no","kviteseid.no","kvitsoy.no","kvitsøy.no","kvafjord.no","kvæfjord.no","giehtavuoatna.no","kvanangen.no","kvænangen.no","navuotna.no","návuotna.no","kafjord.no","kåfjord.no","gaivuotna.no","gáivuotna.no","larvik.no","lavangen.no","lavagis.no","loabat.no","loabát.no","lebesby.no","davvesiida.no","leikanger.no","leirfjord.no","leka.no","leksvik.no","lenvik.no","leangaviika.no","leaŋgaviika.no","lesja.no","levanger.no","lier.no","lierne.no","lillehammer.no","lillesand.no","lindesnes.no","lindas.no","lindås.no","lom.no","loppa.no","lahppi.no","láhppi.no","lund.no","lunner.no","luroy.no","lurøy.no","luster.no","lyngdal.no","lyngen.no","ivgu.no","lardal.no","lerdal.no","lærdal.no","lodingen.no","lødingen.no","lorenskog.no","lørenskog.no","loten.no","løten.no","malvik.no","masoy.no","måsøy.no","muosat.no","muosát.no","mandal.no","marker.no","marnardal.no","masfjorden.no","meland.no","meldal.no","melhus.no","meloy.no","meløy.no","meraker.no","meråker.no","moareke.no","moåreke.no","midsund.no","midtre-gauldal.no","modalen.no","modum.no","molde.no","moskenes.no","moss.no","mosvik.no","malselv.no","målselv.no","malatvuopmi.no","málatvuopmi.no","namdalseid.no","aejrie.no","namsos.no","namsskogan.no","naamesjevuemie.no","nååmesjevuemie.no","laakesvuemie.no","nannestad.no","narvik.no","narviika.no","naustdal.no","nedre-eiker.no","nes.akershus.no","nes.buskerud.no","nesna.no","nesodden.no","nesseby.no","unjarga.no","unjárga.no","nesset.no","nissedal.no","nittedal.no","nord-aurdal.no","nord-fron.no","nord-odal.no","norddal.no","nordkapp.no","davvenjarga.no","davvenjárga.no","nordre-land.no","nordreisa.no","raisa.no","ráisa.no","nore-og-uvdal.no","notodden.no","naroy.no","nærøy.no","notteroy.no","nøtterøy.no","odda.no","oksnes.no","øksnes.no","oppdal.no","oppegard.no","oppegård.no","orkdal.no","orland.no","ørland.no","orskog.no","ørskog.no","orsta.no","ørsta.no","os.hedmark.no","os.hordaland.no","osen.no","osteroy.no","osterøy.no","ostre-toten.no","østre-toten.no","overhalla.no","ovre-eiker.no","øvre-eiker.no","oyer.no","øyer.no","oygarden.no","øygarden.no","oystre-slidre.no","øystre-slidre.no","porsanger.no","porsangu.no","porsáŋgu.no","porsgrunn.no","radoy.no","radøy.no","rakkestad.no","rana.no","ruovat.no","randaberg.no","rauma.no","rendalen.no","rennebu.no","rennesoy.no","rennesøy.no","rindal.no","ringebu.no","ringerike.no","ringsaker.no","rissa.no","risor.no","risør.no","roan.no","rollag.no","rygge.no","ralingen.no","rælingen.no","rodoy.no","rødøy.no","romskog.no","rømskog.no","roros.no","røros.no","rost.no","røst.no","royken.no","røyken.no","royrvik.no","røyrvik.no","rade.no","råde.no","salangen.no","siellak.no","saltdal.no","salat.no","sálát.no","sálat.no","samnanger.no","sande.more-og-romsdal.no","sande.møre-og-romsdal.no","sande.vestfold.no","sandefjord.no","sandnes.no","sandoy.no","sandøy.no","sarpsborg.no","sauda.no","sauherad.no","sel.no","selbu.no","selje.no","seljord.no","sigdal.no","siljan.no","sirdal.no","skaun.no","skedsmo.no","ski.no","skien.no","skiptvet.no","skjervoy.no","skjervøy.no","skierva.no","skiervá.no","skjak.no","skjåk.no","skodje.no","skanland.no","skånland.no","skanit.no","skánit.no","smola.no","smøla.no","snillfjord.no","snasa.no","snåsa.no","snoasa.no","snaase.no","snåase.no","sogndal.no","sokndal.no","sola.no","solund.no","songdalen.no","sortland.no","spydeberg.no","stange.no","stavanger.no","steigen.no","steinkjer.no","stjordal.no","stjørdal.no","stokke.no","stor-elvdal.no","stord.no","stordal.no","storfjord.no","omasvuotna.no","strand.no","stranda.no","stryn.no","sula.no","suldal.no","sund.no","sunndal.no","surnadal.no","sveio.no","svelvik.no","sykkylven.no","sogne.no","søgne.no","somna.no","sømna.no","sondre-land.no","søndre-land.no","sor-aurdal.no","sør-aurdal.no","sor-fron.no","sør-fron.no","sor-odal.no","sør-odal.no","sor-varanger.no","sør-varanger.no","matta-varjjat.no","mátta-várjjat.no","sorfold.no","sørfold.no","sorreisa.no","sørreisa.no","sorum.no","sørum.no","tana.no","deatnu.no","time.no","tingvoll.no","tinn.no","tjeldsund.no","dielddanuorri.no","tjome.no","tjøme.no","tokke.no","tolga.no","torsken.no","tranoy.no","tranøy.no","tromso.no","tromsø.no","tromsa.no","romsa.no","trondheim.no","troandin.no","trysil.no","trana.no","træna.no","trogstad.no","trøgstad.no","tvedestrand.no","tydal.no","tynset.no","tysfjord.no","divtasvuodna.no","divttasvuotna.no","tysnes.no","tysvar.no","tysvær.no","tonsberg.no","tønsberg.no","ullensaker.no","ullensvang.no","ulvik.no","utsira.no","vadso.no","vadsø.no","cahcesuolo.no","čáhcesuolo.no","vaksdal.no","valle.no","vang.no","vanylven.no","vardo.no","vardø.no","varggat.no","várggát.no","vefsn.no","vaapste.no","vega.no","vegarshei.no","vegårshei.no","vennesla.no","verdal.no","verran.no","vestby.no","vestnes.no","vestre-slidre.no","vestre-toten.no","vestvagoy.no","vestvågøy.no","vevelstad.no","vik.no","vikna.no","vindafjord.no","volda.no","voss.no","varoy.no","værøy.no","vagan.no","vågan.no","voagat.no","vagsoy.no","vågsøy.no","vaga.no","vågå.no","valer.ostfold.no","våler.østfold.no","valer.hedmark.no","våler.hedmark.no","*.np","nr","biz.nr","info.nr","gov.nr","edu.nr","org.nr","net.nr","com.nr","nu","nz","ac.nz","co.nz","cri.nz","geek.nz","gen.nz","govt.nz","health.nz","iwi.nz","kiwi.nz","maori.nz","mil.nz","māori.nz","net.nz","org.nz","parliament.nz","school.nz","om","co.om","com.om","edu.om","gov.om","med.om","museum.om","net.om","org.om","pro.om","onion","org","pa","ac.pa","gob.pa","com.pa","org.pa","sld.pa","edu.pa","net.pa","ing.pa","abo.pa","med.pa","nom.pa","pe","edu.pe","gob.pe","nom.pe","mil.pe","org.pe","com.pe","net.pe","pf","com.pf","org.pf","edu.pf","*.pg","ph","com.ph","net.ph","org.ph","gov.ph","edu.ph","ngo.ph","mil.ph","i.ph","pk","com.pk","net.pk","edu.pk","org.pk","fam.pk","biz.pk","web.pk","gov.pk","gob.pk","gok.pk","gon.pk","gop.pk","gos.pk","info.pk","pl","com.pl","net.pl","org.pl","aid.pl","agro.pl","atm.pl","auto.pl","biz.pl","edu.pl","gmina.pl","gsm.pl","info.pl","mail.pl","miasta.pl","media.pl","mil.pl","nieruchomosci.pl","nom.pl","pc.pl","powiat.pl","priv.pl","realestate.pl","rel.pl","sex.pl","shop.pl","sklep.pl","sos.pl","szkola.pl","targi.pl","tm.pl","tourism.pl","travel.pl","turystyka.pl","gov.pl","ap.gov.pl","ic.gov.pl","is.gov.pl","us.gov.pl","kmpsp.gov.pl","kppsp.gov.pl","kwpsp.gov.pl","psp.gov.pl","wskr.gov.pl","kwp.gov.pl","mw.gov.pl","ug.gov.pl","um.gov.pl","umig.gov.pl","ugim.gov.pl","upow.gov.pl","uw.gov.pl","starostwo.gov.pl","pa.gov.pl","po.gov.pl","psse.gov.pl","pup.gov.pl","rzgw.gov.pl","sa.gov.pl","so.gov.pl","sr.gov.pl","wsa.gov.pl","sko.gov.pl","uzs.gov.pl","wiih.gov.pl","winb.gov.pl","pinb.gov.pl","wios.gov.pl","witd.gov.pl","wzmiuw.gov.pl","piw.gov.pl","wiw.gov.pl","griw.gov.pl","wif.gov.pl","oum.gov.pl","sdn.gov.pl","zp.gov.pl","uppo.gov.pl","mup.gov.pl","wuoz.gov.pl","konsulat.gov.pl","oirm.gov.pl","augustow.pl","babia-gora.pl","bedzin.pl","beskidy.pl","bialowieza.pl","bialystok.pl","bielawa.pl","bieszczady.pl","boleslawiec.pl","bydgoszcz.pl","bytom.pl","cieszyn.pl","czeladz.pl","czest.pl","dlugoleka.pl","elblag.pl","elk.pl","glogow.pl","gniezno.pl","gorlice.pl","grajewo.pl","ilawa.pl","jaworzno.pl","jelenia-gora.pl","jgora.pl","kalisz.pl","kazimierz-dolny.pl","karpacz.pl","kartuzy.pl","kaszuby.pl","katowice.pl","kepno.pl","ketrzyn.pl","klodzko.pl","kobierzyce.pl","kolobrzeg.pl","konin.pl","konskowola.pl","kutno.pl","lapy.pl","lebork.pl","legnica.pl","lezajsk.pl","limanowa.pl","lomza.pl","lowicz.pl","lubin.pl","lukow.pl","malbork.pl","malopolska.pl","mazowsze.pl","mazury.pl","mielec.pl","mielno.pl","mragowo.pl","naklo.pl","nowaruda.pl","nysa.pl","olawa.pl","olecko.pl","olkusz.pl","olsztyn.pl","opoczno.pl","opole.pl","ostroda.pl","ostroleka.pl","ostrowiec.pl","ostrowwlkp.pl","pila.pl","pisz.pl","podhale.pl","podlasie.pl","polkowice.pl","pomorze.pl","pomorskie.pl","prochowice.pl","pruszkow.pl","przeworsk.pl","pulawy.pl","radom.pl","rawa-maz.pl","rybnik.pl","rzeszow.pl","sanok.pl","sejny.pl","slask.pl","slupsk.pl","sosnowiec.pl","stalowa-wola.pl","skoczow.pl","starachowice.pl","stargard.pl","suwalki.pl","swidnica.pl","swiebodzin.pl","swinoujscie.pl","szczecin.pl","szczytno.pl","tarnobrzeg.pl","tgory.pl","turek.pl","tychy.pl","ustka.pl","walbrzych.pl","warmia.pl","warszawa.pl","waw.pl","wegrow.pl","wielun.pl","wlocl.pl","wloclawek.pl","wodzislaw.pl","wolomin.pl","wroclaw.pl","zachpomor.pl","zagan.pl","zarow.pl","zgora.pl","zgorzelec.pl","pm","pn","gov.pn","co.pn","org.pn","edu.pn","net.pn","post","pr","com.pr","net.pr","org.pr","gov.pr","edu.pr","isla.pr","pro.pr","biz.pr","info.pr","name.pr","est.pr","prof.pr","ac.pr","pro","aaa.pro","aca.pro","acct.pro","avocat.pro","bar.pro","cpa.pro","eng.pro","jur.pro","law.pro","med.pro","recht.pro","ps","edu.ps","gov.ps","sec.ps","plo.ps","com.ps","org.ps","net.ps","pt","net.pt","gov.pt","org.pt","edu.pt","int.pt","publ.pt","com.pt","nome.pt","pw","co.pw","ne.pw","or.pw","ed.pw","go.pw","belau.pw","py","com.py","coop.py","edu.py","gov.py","mil.py","net.py","org.py","qa","com.qa","edu.qa","gov.qa","mil.qa","name.qa","net.qa","org.qa","sch.qa","re","asso.re","com.re","nom.re","ro","arts.ro","com.ro","firm.ro","info.ro","nom.ro","nt.ro","org.ro","rec.ro","store.ro","tm.ro","www.ro","rs","ac.rs","co.rs","edu.rs","gov.rs","in.rs","org.rs","ru","rw","ac.rw","co.rw","coop.rw","gov.rw","mil.rw","net.rw","org.rw","sa","com.sa","net.sa","org.sa","gov.sa","med.sa","pub.sa","edu.sa","sch.sa","sb","com.sb","edu.sb","gov.sb","net.sb","org.sb","sc","com.sc","gov.sc","net.sc","org.sc","edu.sc","sd","com.sd","net.sd","org.sd","edu.sd","med.sd","tv.sd","gov.sd","info.sd","se","a.se","ac.se","b.se","bd.se","brand.se","c.se","d.se","e.se","f.se","fh.se","fhsk.se","fhv.se","g.se","h.se","i.se","k.se","komforb.se","kommunalforbund.se","komvux.se","l.se","lanbib.se","m.se","n.se","naturbruksgymn.se","o.se","org.se","p.se","parti.se","pp.se","press.se","r.se","s.se","t.se","tm.se","u.se","w.se","x.se","y.se","z.se","sg","com.sg","net.sg","org.sg","gov.sg","edu.sg","per.sg","sh","com.sh","net.sh","gov.sh","org.sh","mil.sh","si","sj","sk","sl","com.sl","net.sl","edu.sl","gov.sl","org.sl","sm","sn","art.sn","com.sn","edu.sn","gouv.sn","org.sn","perso.sn","univ.sn","so","com.so","edu.so","gov.so","me.so","net.so","org.so","sr","ss","biz.ss","com.ss","edu.ss","gov.ss","me.ss","net.ss","org.ss","sch.ss","st","co.st","com.st","consulado.st","edu.st","embaixada.st","mil.st","net.st","org.st","principe.st","saotome.st","store.st","su","sv","com.sv","edu.sv","gob.sv","org.sv","red.sv","sx","gov.sx","sy","edu.sy","gov.sy","net.sy","mil.sy","com.sy","org.sy","sz","co.sz","ac.sz","org.sz","tc","td","tel","tf","tg","th","ac.th","co.th","go.th","in.th","mi.th","net.th","or.th","tj","ac.tj","biz.tj","co.tj","com.tj","edu.tj","go.tj","gov.tj","int.tj","mil.tj","name.tj","net.tj","nic.tj","org.tj","test.tj","web.tj","tk","tl","gov.tl","tm","com.tm","co.tm","org.tm","net.tm","nom.tm","gov.tm","mil.tm","edu.tm","tn","com.tn","ens.tn","fin.tn","gov.tn","ind.tn","info.tn","intl.tn","mincom.tn","nat.tn","net.tn","org.tn","perso.tn","tourism.tn","to","com.to","gov.to","net.to","org.to","edu.to","mil.to","tr","av.tr","bbs.tr","bel.tr","biz.tr","com.tr","dr.tr","edu.tr","gen.tr","gov.tr","info.tr","mil.tr","k12.tr","kep.tr","name.tr","net.tr","org.tr","pol.tr","tel.tr","tsk.tr","tv.tr","web.tr","nc.tr","gov.nc.tr","tt","co.tt","com.tt","org.tt","net.tt","biz.tt","info.tt","pro.tt","int.tt","coop.tt","jobs.tt","mobi.tt","travel.tt","museum.tt","aero.tt","name.tt","gov.tt","edu.tt","tv","tw","edu.tw","gov.tw","mil.tw","com.tw","net.tw","org.tw","idv.tw","game.tw","ebiz.tw","club.tw","網路.tw","組織.tw","商業.tw","tz","ac.tz","co.tz","go.tz","hotel.tz","info.tz","me.tz","mil.tz","mobi.tz","ne.tz","or.tz","sc.tz","tv.tz","ua","com.ua","edu.ua","gov.ua","in.ua","net.ua","org.ua","cherkassy.ua","cherkasy.ua","chernigov.ua","chernihiv.ua","chernivtsi.ua","chernovtsy.ua","ck.ua","cn.ua","cr.ua","crimea.ua","cv.ua","dn.ua","dnepropetrovsk.ua","dnipropetrovsk.ua","donetsk.ua","dp.ua","if.ua","ivano-frankivsk.ua","kh.ua","kharkiv.ua","kharkov.ua","kherson.ua","khmelnitskiy.ua","khmelnytskyi.ua","kiev.ua","kirovograd.ua","km.ua","kr.ua","krym.ua","ks.ua","kv.ua","kyiv.ua","lg.ua","lt.ua","lugansk.ua","lutsk.ua","lv.ua","lviv.ua","mk.ua","mykolaiv.ua","nikolaev.ua","od.ua","odesa.ua","odessa.ua","pl.ua","poltava.ua","rivne.ua","rovno.ua","rv.ua","sb.ua","sebastopol.ua","sevastopol.ua","sm.ua","sumy.ua","te.ua","ternopil.ua","uz.ua","uzhgorod.ua","vinnica.ua","vinnytsia.ua","vn.ua","volyn.ua","yalta.ua","zaporizhzhe.ua","zaporizhzhia.ua","zhitomir.ua","zhytomyr.ua","zp.ua","zt.ua","ug","co.ug","or.ug","ac.ug","sc.ug","go.ug","ne.ug","com.ug","org.ug","uk","ac.uk","co.uk","gov.uk","ltd.uk","me.uk","net.uk","nhs.uk","org.uk","plc.uk","police.uk","*.sch.uk","us","dni.us","fed.us","isa.us","kids.us","nsn.us","ak.us","al.us","ar.us","as.us","az.us","ca.us","co.us","ct.us","dc.us","de.us","fl.us","ga.us","gu.us","hi.us","ia.us","id.us","il.us","in.us","ks.us","ky.us","la.us","ma.us","md.us","me.us","mi.us","mn.us","mo.us","ms.us","mt.us","nc.us","nd.us","ne.us","nh.us","nj.us","nm.us","nv.us","ny.us","oh.us","ok.us","or.us","pa.us","pr.us","ri.us","sc.us","sd.us","tn.us","tx.us","ut.us","vi.us","vt.us","va.us","wa.us","wi.us","wv.us","wy.us","k12.ak.us","k12.al.us","k12.ar.us","k12.as.us","k12.az.us","k12.ca.us","k12.co.us","k12.ct.us","k12.dc.us","k12.de.us","k12.fl.us","k12.ga.us","k12.gu.us","k12.ia.us","k12.id.us","k12.il.us","k12.in.us","k12.ks.us","k12.ky.us","k12.la.us","k12.ma.us","k12.md.us","k12.me.us","k12.mi.us","k12.mn.us","k12.mo.us","k12.ms.us","k12.mt.us","k12.nc.us","k12.ne.us","k12.nh.us","k12.nj.us","k12.nm.us","k12.nv.us","k12.ny.us","k12.oh.us","k12.ok.us","k12.or.us","k12.pa.us","k12.pr.us","k12.sc.us","k12.tn.us","k12.tx.us","k12.ut.us","k12.vi.us","k12.vt.us","k12.va.us","k12.wa.us","k12.wi.us","k12.wy.us","cc.ak.us","cc.al.us","cc.ar.us","cc.as.us","cc.az.us","cc.ca.us","cc.co.us","cc.ct.us","cc.dc.us","cc.de.us","cc.fl.us","cc.ga.us","cc.gu.us","cc.hi.us","cc.ia.us","cc.id.us","cc.il.us","cc.in.us","cc.ks.us","cc.ky.us","cc.la.us","cc.ma.us","cc.md.us","cc.me.us","cc.mi.us","cc.mn.us","cc.mo.us","cc.ms.us","cc.mt.us","cc.nc.us","cc.nd.us","cc.ne.us","cc.nh.us","cc.nj.us","cc.nm.us","cc.nv.us","cc.ny.us","cc.oh.us","cc.ok.us","cc.or.us","cc.pa.us","cc.pr.us","cc.ri.us","cc.sc.us","cc.sd.us","cc.tn.us","cc.tx.us","cc.ut.us","cc.vi.us","cc.vt.us","cc.va.us","cc.wa.us","cc.wi.us","cc.wv.us","cc.wy.us","lib.ak.us","lib.al.us","lib.ar.us","lib.as.us","lib.az.us","lib.ca.us","lib.co.us","lib.ct.us","lib.dc.us","lib.fl.us","lib.ga.us","lib.gu.us","lib.hi.us","lib.ia.us","lib.id.us","lib.il.us","lib.in.us","lib.ks.us","lib.ky.us","lib.la.us","lib.ma.us","lib.md.us","lib.me.us","lib.mi.us","lib.mn.us","lib.mo.us","lib.ms.us","lib.mt.us","lib.nc.us","lib.nd.us","lib.ne.us","lib.nh.us","lib.nj.us","lib.nm.us","lib.nv.us","lib.ny.us","lib.oh.us","lib.ok.us","lib.or.us","lib.pa.us","lib.pr.us","lib.ri.us","lib.sc.us","lib.sd.us","lib.tn.us","lib.tx.us","lib.ut.us","lib.vi.us","lib.vt.us","lib.va.us","lib.wa.us","lib.wi.us","lib.wy.us","pvt.k12.ma.us","chtr.k12.ma.us","paroch.k12.ma.us","ann-arbor.mi.us","cog.mi.us","dst.mi.us","eaton.mi.us","gen.mi.us","mus.mi.us","tec.mi.us","washtenaw.mi.us","uy","com.uy","edu.uy","gub.uy","mil.uy","net.uy","org.uy","uz","co.uz","com.uz","net.uz","org.uz","va","vc","com.vc","net.vc","org.vc","gov.vc","mil.vc","edu.vc","ve","arts.ve","bib.ve","co.ve","com.ve","e12.ve","edu.ve","firm.ve","gob.ve","gov.ve","info.ve","int.ve","mil.ve","net.ve","nom.ve","org.ve","rar.ve","rec.ve","store.ve","tec.ve","web.ve","vg","vi","co.vi","com.vi","k12.vi","net.vi","org.vi","vn","com.vn","net.vn","org.vn","edu.vn","gov.vn","int.vn","ac.vn","biz.vn","info.vn","name.vn","pro.vn","health.vn","vu","com.vu","edu.vu","net.vu","org.vu","wf","ws","com.ws","net.ws","org.ws","gov.ws","edu.ws","yt","امارات","հայ","বাংলা","бг","البحرين","бел","中国","中國","الجزائر","مصر","ею","ευ","موريتانيا","გე","ελ","香港","公司.香港","教育.香港","政府.香港","個人.香港","網絡.香港","組織.香港","ಭಾರತ","ଭାରତ","ভাৰত","भारतम्","भारोत","ڀارت","ഭാരതം","भारत","بارت","بھارت","భారత్","ભારત","ਭਾਰਤ","ভারত","இந்தியா","ایران","ايران","عراق","الاردن","한국","қаз","ລາວ","ලංකා","இலங்கை","المغرب","мкд","мон","澳門","澳门","مليسيا","عمان","پاکستان","پاكستان","فلسطين","срб","пр.срб","орг.срб","обр.срб","од.срб","упр.срб","ак.срб","рф","قطر","السعودية","السعودیة","السعودیۃ","السعوديه","سودان","新加坡","சிங்கப்பூர்","سورية","سوريا","ไทย","ศึกษา.ไทย","ธุรกิจ.ไทย","รัฐบาล.ไทย","ทหาร.ไทย","เน็ต.ไทย","องค์กร.ไทย","تونس","台灣","台湾","臺灣","укр","اليمن","xxx","ye","com.ye","edu.ye","gov.ye","net.ye","mil.ye","org.ye","ac.za","agric.za","alt.za","co.za","edu.za","gov.za","grondar.za","law.za","mil.za","net.za","ngo.za","nic.za","nis.za","nom.za","org.za","school.za","tm.za","web.za","zm","ac.zm","biz.zm","co.zm","com.zm","edu.zm","gov.zm","info.zm","mil.zm","net.zm","org.zm","sch.zm","zw","ac.zw","co.zw","gov.zw","mil.zw","org.zw","aaa","aarp","abarth","abb","abbott","abbvie","abc","able","abogado","abudhabi","academy","accenture","accountant","accountants","aco","actor","adac","ads","adult","aeg","aetna","afl","africa","agakhan","agency","aig","airbus","airforce","airtel","akdn","alfaromeo","alibaba","alipay","allfinanz","allstate","ally","alsace","alstom","amazon","americanexpress","americanfamily","amex","amfam","amica","amsterdam","analytics","android","anquan","anz","aol","apartments","app","apple","aquarelle","arab","aramco","archi","army","art","arte","asda","associates","athleta","attorney","auction","audi","audible","audio","auspost","author","auto","autos","avianca","aws","axa","azure","baby","baidu","banamex","bananarepublic","band","bank","bar","barcelona","barclaycard","barclays","barefoot","bargains","baseball","basketball","bauhaus","bayern","bbc","bbt","bbva","bcg","bcn","beats","beauty","beer","bentley","berlin","best","bestbuy","bet","bharti","bible","bid","bike","bing","bingo","bio","black","blackfriday","blockbuster","blog","bloomberg","blue","bms","bmw","bnpparibas","boats","boehringer","bofa","bom","bond","boo","book","booking","bosch","bostik","boston","bot","boutique","box","bradesco","bridgestone","broadway","broker","brother","brussels","bugatti","build","builders","business","buy","buzz","bzh","cab","cafe","cal","call","calvinklein","cam","camera","camp","cancerresearch","canon","capetown","capital","capitalone","car","caravan","cards","care","career","careers","cars","casa","case","cash","casino","catering","catholic","cba","cbn","cbre","cbs","center","ceo","cern","cfa","cfd","chanel","channel","charity","chase","chat","cheap","chintai","christmas","chrome","church","cipriani","circle","cisco","citadel","citi","citic","city","cityeats","claims","cleaning","click","clinic","clinique","clothing","cloud","club","clubmed","coach","codes","coffee","college","cologne","comcast","commbank","community","company","compare","computer","comsec","condos","construction","consulting","contact","contractors","cooking","cookingchannel","cool","corsica","country","coupon","coupons","courses","cpa","credit","creditcard","creditunion","cricket","crown","crs","cruise","cruises","cuisinella","cymru","cyou","dabur","dad","dance","data","date","dating","datsun","day","dclk","dds","deal","dealer","deals","degree","delivery","dell","deloitte","delta","democrat","dental","dentist","desi","design","dev","dhl","diamonds","diet","digital","direct","directory","discount","discover","dish","diy","dnp","docs","doctor","dog","domains","dot","download","drive","dtv","dubai","dunlop","dupont","durban","dvag","dvr","earth","eat","eco","edeka","education","email","emerck","energy","engineer","engineering","enterprises","epson","equipment","ericsson","erni","esq","estate","etisalat","eurovision","eus","events","exchange","expert","exposed","express","extraspace","fage","fail","fairwinds","faith","family","fan","fans","farm","farmers","fashion","fast","fedex","feedback","ferrari","ferrero","fiat","fidelity","fido","film","final","finance","financial","fire","firestone","firmdale","fish","fishing","fit","fitness","flickr","flights","flir","florist","flowers","fly","foo","food","foodnetwork","football","ford","forex","forsale","forum","foundation","fox","free","fresenius","frl","frogans","frontdoor","frontier","ftr","fujitsu","fun","fund","furniture","futbol","fyi","gal","gallery","gallo","gallup","game","games","gap","garden","gay","gbiz","gdn","gea","gent","genting","george","ggee","gift","gifts","gives","giving","glass","gle","global","globo","gmail","gmbh","gmo","gmx","godaddy","gold","goldpoint","golf","goo","goodyear","goog","google","gop","got","grainger","graphics","gratis","green","gripe","grocery","group","guardian","gucci","guge","guide","guitars","guru","hair","hamburg","hangout","haus","hbo","hdfc","hdfcbank","health","healthcare","help","helsinki","here","hermes","hgtv","hiphop","hisamitsu","hitachi","hiv","hkt","hockey","holdings","holiday","homedepot","homegoods","homes","homesense","honda","horse","hospital","host","hosting","hot","hoteles","hotels","hotmail","house","how","hsbc","hughes","hyatt","hyundai","ibm","icbc","ice","icu","ieee","ifm","ikano","imamat","imdb","immo","immobilien","inc","industries","infiniti","ing","ink","institute","insurance","insure","international","intuit","investments","ipiranga","irish","ismaili","ist","istanbul","itau","itv","jaguar","java","jcb","jeep","jetzt","jewelry","jio","jll","jmp","jnj","joburg","jot","joy","jpmorgan","jprs","juegos","juniper","kaufen","kddi","kerryhotels","kerrylogistics","kerryproperties","kfh","kia","kids","kim","kinder","kindle","kitchen","kiwi","koeln","komatsu","kosher","kpmg","kpn","krd","kred","kuokgroup","kyoto","lacaixa","lamborghini","lamer","lancaster","lancia","land","landrover","lanxess","lasalle","lat","latino","latrobe","law","lawyer","lds","lease","leclerc","lefrak","legal","lego","lexus","lgbt","lidl","life","lifeinsurance","lifestyle","lighting","like","lilly","limited","limo","lincoln","linde","link","lipsy","live","living","llc","llp","loan","loans","locker","locus","loft","lol","london","lotte","lotto","love","lpl","lplfinancial","ltd","ltda","lundbeck","luxe","luxury","macys","madrid","maif","maison","makeup","man","management","mango","map","market","marketing","markets","marriott","marshalls","maserati","mattel","mba","mckinsey","med","media","meet","melbourne","meme","memorial","men","menu","merckmsd","miami","microsoft","mini","mint","mit","mitsubishi","mlb","mls","mma","mobile","moda","moe","moi","mom","monash","money","monster","mormon","mortgage","moscow","moto","motorcycles","mov","movie","msd","mtn","mtr","music","mutual","nab","nagoya","natura","navy","nba","nec","netbank","netflix","network","neustar","new","news","next","nextdirect","nexus","nfl","ngo","nhk","nico","nike","nikon","ninja","nissan","nissay","nokia","northwesternmutual","norton","now","nowruz","nowtv","nra","nrw","ntt","nyc","obi","observer","office","okinawa","olayan","olayangroup","oldnavy","ollo","omega","one","ong","onl","online","ooo","open","oracle","orange","organic","origins","osaka","otsuka","ott","ovh","page","panasonic","paris","pars","partners","parts","party","passagens","pay","pccw","pet","pfizer","pharmacy","phd","philips","phone","photo","photography","photos","physio","pics","pictet","pictures","pid","pin","ping","pink","pioneer","pizza","place","play","playstation","plumbing","plus","pnc","pohl","poker","politie","porn","pramerica","praxi","press","prime","prod","productions","prof","progressive","promo","properties","property","protection","pru","prudential","pub","pwc","qpon","quebec","quest","racing","radio","read","realestate","realtor","realty","recipes","red","redstone","redumbrella","rehab","reise","reisen","reit","reliance","ren","rent","rentals","repair","report","republican","rest","restaurant","review","reviews","rexroth","rich","richardli","ricoh","ril","rio","rip","rocher","rocks","rodeo","rogers","room","rsvp","rugby","ruhr","run","rwe","ryukyu","saarland","safe","safety","sakura","sale","salon","samsclub","samsung","sandvik","sandvikcoromant","sanofi","sap","sarl","sas","save","saxo","sbi","sbs","sca","scb","schaeffler","schmidt","scholarships","school","schule","schwarz","science","scot","search","seat","secure","security","seek","select","sener","services","ses","seven","sew","sex","sexy","sfr","shangrila","sharp","shaw","shell","shia","shiksha","shoes","shop","shopping","shouji","show","showtime","silk","sina","singles","site","ski","skin","sky","skype","sling","smart","smile","sncf","soccer","social","softbank","software","sohu","solar","solutions","song","sony","soy","spa","space","sport","spot","srl","stada","staples","star","statebank","statefarm","stc","stcgroup","stockholm","storage","store","stream","studio","study","style","sucks","supplies","supply","support","surf","surgery","suzuki","swatch","swiss","sydney","systems","tab","taipei","talk","taobao","target","tatamotors","tatar","tattoo","tax","taxi","tci","tdk","team","tech","technology","temasek","tennis","teva","thd","theater","theatre","tiaa","tickets","tienda","tiffany","tips","tires","tirol","tjmaxx","tjx","tkmaxx","tmall","today","tokyo","tools","top","toray","toshiba","total","tours","town","toyota","toys","trade","trading","training","travel","travelchannel","travelers","travelersinsurance","trust","trv","tube","tui","tunes","tushu","tvs","ubank","ubs","unicom","university","uno","uol","ups","vacations","vana","vanguard","vegas","ventures","verisign","versicherung","vet","viajes","video","vig","viking","villas","vin","vip","virgin","visa","vision","viva","vivo","vlaanderen","vodka","volkswagen","volvo","vote","voting","voto","voyage","vuelos","wales","walmart","walter","wang","wanggou","watch","watches","weather","weatherchannel","webcam","weber","website","wedding","weibo","weir","whoswho","wien","wiki","williamhill","win","windows","wine","winners","wme","wolterskluwer","woodside","work","works","world","wow","wtc","wtf","xbox","xerox","xfinity","xihuan","xin","कॉम","セール","佛山","慈善","集团","在线","点看","คอม","八卦","موقع","公益","公司","香格里拉","网站","移动","我爱你","москва","католик","онлайн","сайт","联通","קום","时尚","微博","淡马锡","ファッション","орг","नेट","ストア","アマゾン","삼성","商标","商店","商城","дети","ポイント","新闻","家電","كوم","中文网","中信","娱乐","谷歌","電訊盈科","购物","クラウド","通販","网店","संगठन","餐厅","网络","ком","亚马逊","诺基亚","食品","飞利浦","手机","ارامكو","العليان","اتصالات","بازار","ابوظبي","كاثوليك","همراه","닷컴","政府","شبكة","بيتك","عرب","机构","组织机构","健康","招聘","рус","大拿","みんな","グーグル","世界","書籍","网址","닷넷","コム","天主教","游戏","vermögensberater","vermögensberatung","企业","信息","嘉里大酒店","嘉里","广东","政务","xyz","yachts","yahoo","yamaxun","yandex","yodobashi","yoga","yokohama","you","youtube","yun","zappos","zara","zero","zip","zone","zuerich","cc.ua","inf.ua","ltd.ua","611.to","graphox.us","*.devcdnaccesso.com","adobeaemcloud.com","*.dev.adobeaemcloud.com","hlx.live","adobeaemcloud.net","hlx.page","hlx3.page","beep.pl","airkitapps.com","airkitapps-au.com","airkitapps.eu","aivencloud.com","barsy.ca","*.compute.estate","*.alces.network","kasserver.com","altervista.org","alwaysdata.net","cloudfront.net","*.compute.amazonaws.com","*.compute-1.amazonaws.com","*.compute.amazonaws.com.cn","us-east-1.amazonaws.com","cn-north-1.eb.amazonaws.com.cn","cn-northwest-1.eb.amazonaws.com.cn","elasticbeanstalk.com","ap-northeast-1.elasticbeanstalk.com","ap-northeast-2.elasticbeanstalk.com","ap-northeast-3.elasticbeanstalk.com","ap-south-1.elasticbeanstalk.com","ap-southeast-1.elasticbeanstalk.com","ap-southeast-2.elasticbeanstalk.com","ca-central-1.elasticbeanstalk.com","eu-central-1.elasticbeanstalk.com","eu-west-1.elasticbeanstalk.com","eu-west-2.elasticbeanstalk.com","eu-west-3.elasticbeanstalk.com","sa-east-1.elasticbeanstalk.com","us-east-1.elasticbeanstalk.com","us-east-2.elasticbeanstalk.com","us-gov-west-1.elasticbeanstalk.com","us-west-1.elasticbeanstalk.com","us-west-2.elasticbeanstalk.com","*.elb.amazonaws.com","*.elb.amazonaws.com.cn","awsglobalaccelerator.com","s3.amazonaws.com","s3-ap-northeast-1.amazonaws.com","s3-ap-northeast-2.amazonaws.com","s3-ap-south-1.amazonaws.com","s3-ap-southeast-1.amazonaws.com","s3-ap-southeast-2.amazonaws.com","s3-ca-central-1.amazonaws.com","s3-eu-central-1.amazonaws.com","s3-eu-west-1.amazonaws.com","s3-eu-west-2.amazonaws.com","s3-eu-west-3.amazonaws.com","s3-external-1.amazonaws.com","s3-fips-us-gov-west-1.amazonaws.com","s3-sa-east-1.amazonaws.com","s3-us-gov-west-1.amazonaws.com","s3-us-east-2.amazonaws.com","s3-us-west-1.amazonaws.com","s3-us-west-2.amazonaws.com","s3.ap-northeast-2.amazonaws.com","s3.ap-south-1.amazonaws.com","s3.cn-north-1.amazonaws.com.cn","s3.ca-central-1.amazonaws.com","s3.eu-central-1.amazonaws.com","s3.eu-west-2.amazonaws.com","s3.eu-west-3.amazonaws.com","s3.us-east-2.amazonaws.com","s3.dualstack.ap-northeast-1.amazonaws.com","s3.dualstack.ap-northeast-2.amazonaws.com","s3.dualstack.ap-south-1.amazonaws.com","s3.dualstack.ap-southeast-1.amazonaws.com","s3.dualstack.ap-southeast-2.amazonaws.com","s3.dualstack.ca-central-1.amazonaws.com","s3.dualstack.eu-central-1.amazonaws.com","s3.dualstack.eu-west-1.amazonaws.com","s3.dualstack.eu-west-2.amazonaws.com","s3.dualstack.eu-west-3.amazonaws.com","s3.dualstack.sa-east-1.amazonaws.com","s3.dualstack.us-east-1.amazonaws.com","s3.dualstack.us-east-2.amazonaws.com","s3-website-us-east-1.amazonaws.com","s3-website-us-west-1.amazonaws.com","s3-website-us-west-2.amazonaws.com","s3-website-ap-northeast-1.amazonaws.com","s3-website-ap-southeast-1.amazonaws.com","s3-website-ap-southeast-2.amazonaws.com","s3-website-eu-west-1.amazonaws.com","s3-website-sa-east-1.amazonaws.com","s3-website.ap-northeast-2.amazonaws.com","s3-website.ap-south-1.amazonaws.com","s3-website.ca-central-1.amazonaws.com","s3-website.eu-central-1.amazonaws.com","s3-website.eu-west-2.amazonaws.com","s3-website.eu-west-3.amazonaws.com","s3-website.us-east-2.amazonaws.com","t3l3p0rt.net","tele.amune.org","apigee.io","siiites.com","appspacehosted.com","appspaceusercontent.com","appudo.net","on-aptible.com","user.aseinet.ne.jp","gv.vc","d.gv.vc","user.party.eus","pimienta.org","poivron.org","potager.org","sweetpepper.org","myasustor.com","cdn.prod.atlassian-dev.net","translated.page","myfritz.net","onavstack.net","*.awdev.ca","*.advisor.ws","ecommerce-shop.pl","b-data.io","backplaneapp.io","balena-devices.com","rs.ba","*.banzai.cloud","app.banzaicloud.io","*.backyards.banzaicloud.io","base.ec","official.ec","buyshop.jp","fashionstore.jp","handcrafted.jp","kawaiishop.jp","supersale.jp","theshop.jp","shopselect.net","base.shop","*.beget.app","betainabox.com","bnr.la","bitbucket.io","blackbaudcdn.net","of.je","bluebite.io","boomla.net","boutir.com","boxfuse.io","square7.ch","bplaced.com","bplaced.de","square7.de","bplaced.net","square7.net","shop.brendly.rs","browsersafetymark.io","uk0.bigv.io","dh.bytemark.co.uk","vm.bytemark.co.uk","cafjs.com","mycd.eu","drr.ac","uwu.ai","carrd.co","crd.co","ju.mp","ae.org","br.com","cn.com","com.de","com.se","de.com","eu.com","gb.net","hu.net","jp.net","jpn.com","mex.com","ru.com","sa.com","se.net","uk.com","uk.net","us.com","za.bz","za.com","ar.com","hu.com","kr.com","no.com","qc.com","uy.com","africa.com","gr.com","in.net","web.in","us.org","co.com","aus.basketball","nz.basketball","radio.am","radio.fm","c.la","certmgr.org","cx.ua","discourse.group","discourse.team","cleverapps.io","clerk.app","clerkstage.app","*.lcl.dev","*.lclstage.dev","*.stg.dev","*.stgstage.dev","clickrising.net","c66.me","cloud66.ws","cloud66.zone","jdevcloud.com","wpdevcloud.com","cloudaccess.host","freesite.host","cloudaccess.net","cloudcontrolled.com","cloudcontrolapp.com","*.cloudera.site","pages.dev","trycloudflare.com","workers.dev","wnext.app","co.ca","*.otap.co","co.cz","c.cdn77.org","cdn77-ssl.net","r.cdn77.net","rsc.cdn77.org","ssl.origin.cdn77-secure.org","cloudns.asia","cloudns.biz","cloudns.club","cloudns.cc","cloudns.eu","cloudns.in","cloudns.info","cloudns.org","cloudns.pro","cloudns.pw","cloudns.us","cnpy.gdn","codeberg.page","co.nl","co.no","webhosting.be","hosting-cluster.nl","ac.ru","edu.ru","gov.ru","int.ru","mil.ru","test.ru","dyn.cosidns.de","dynamisches-dns.de","dnsupdater.de","internet-dns.de","l-o-g-i-n.de","dynamic-dns.info","feste-ip.net","knx-server.net","static-access.net","realm.cz","*.cryptonomic.net","cupcake.is","curv.dev","*.customer-oci.com","*.oci.customer-oci.com","*.ocp.customer-oci.com","*.ocs.customer-oci.com","cyon.link","cyon.site","fnwk.site","folionetwork.site","platform0.app","daplie.me","localhost.daplie.me","dattolocal.com","dattorelay.com","dattoweb.com","mydatto.com","dattolocal.net","mydatto.net","biz.dk","co.dk","firm.dk","reg.dk","store.dk","dyndns.dappnode.io","*.dapps.earth","*.bzz.dapps.earth","builtwithdark.com","demo.datadetect.com","instance.datadetect.com","edgestack.me","ddns5.com","debian.net","deno.dev","deno-staging.dev","dedyn.io","deta.app","deta.dev","*.rss.my.id","*.diher.solutions","discordsays.com","discordsez.com","jozi.biz","dnshome.de","online.th","shop.th","drayddns.com","shoparena.pl","dreamhosters.com","mydrobo.com","drud.io","drud.us","duckdns.org","bip.sh","bitbridge.net","dy.fi","tunk.org","dyndns-at-home.com","dyndns-at-work.com","dyndns-blog.com","dyndns-free.com","dyndns-home.com","dyndns-ip.com","dyndns-mail.com","dyndns-office.com","dyndns-pics.com","dyndns-remote.com","dyndns-server.com","dyndns-web.com","dyndns-wiki.com","dyndns-work.com","dyndns.biz","dyndns.info","dyndns.org","dyndns.tv","at-band-camp.net","ath.cx","barrel-of-knowledge.info","barrell-of-knowledge.info","better-than.tv","blogdns.com","blogdns.net","blogdns.org","blogsite.org","boldlygoingnowhere.org","broke-it.net","buyshouses.net","cechire.com","dnsalias.com","dnsalias.net","dnsalias.org","dnsdojo.com","dnsdojo.net","dnsdojo.org","does-it.net","doesntexist.com","doesntexist.org","dontexist.com","dontexist.net","dontexist.org","doomdns.com","doomdns.org","dvrdns.org","dyn-o-saur.com","dynalias.com","dynalias.net","dynalias.org","dynathome.net","dyndns.ws","endofinternet.net","endofinternet.org","endoftheinternet.org","est-a-la-maison.com","est-a-la-masion.com","est-le-patron.com","est-mon-blogueur.com","for-better.biz","for-more.biz","for-our.info","for-some.biz","for-the.biz","forgot.her.name","forgot.his.name","from-ak.com","from-al.com","from-ar.com","from-az.net","from-ca.com","from-co.net","from-ct.com","from-dc.com","from-de.com","from-fl.com","from-ga.com","from-hi.com","from-ia.com","from-id.com","from-il.com","from-in.com","from-ks.com","from-ky.com","from-la.net","from-ma.com","from-md.com","from-me.org","from-mi.com","from-mn.com","from-mo.com","from-ms.com","from-mt.com","from-nc.com","from-nd.com","from-ne.com","from-nh.com","from-nj.com","from-nm.com","from-nv.com","from-ny.net","from-oh.com","from-ok.com","from-or.com","from-pa.com","from-pr.com","from-ri.com","from-sc.com","from-sd.com","from-tn.com","from-tx.com","from-ut.com","from-va.com","from-vt.com","from-wa.com","from-wi.com","from-wv.com","from-wy.com","ftpaccess.cc","fuettertdasnetz.de","game-host.org","game-server.cc","getmyip.com","gets-it.net","go.dyndns.org","gotdns.com","gotdns.org","groks-the.info","groks-this.info","ham-radio-op.net","here-for-more.info","hobby-site.com","hobby-site.org","home.dyndns.org","homedns.org","homeftp.net","homeftp.org","homeip.net","homelinux.com","homelinux.net","homelinux.org","homeunix.com","homeunix.net","homeunix.org","iamallama.com","in-the-band.net","is-a-anarchist.com","is-a-blogger.com","is-a-bookkeeper.com","is-a-bruinsfan.org","is-a-bulls-fan.com","is-a-candidate.org","is-a-caterer.com","is-a-celticsfan.org","is-a-chef.com","is-a-chef.net","is-a-chef.org","is-a-conservative.com","is-a-cpa.com","is-a-cubicle-slave.com","is-a-democrat.com","is-a-designer.com","is-a-doctor.com","is-a-financialadvisor.com","is-a-geek.com","is-a-geek.net","is-a-geek.org","is-a-green.com","is-a-guru.com","is-a-hard-worker.com","is-a-hunter.com","is-a-knight.org","is-a-landscaper.com","is-a-lawyer.com","is-a-liberal.com","is-a-libertarian.com","is-a-linux-user.org","is-a-llama.com","is-a-musician.com","is-a-nascarfan.com","is-a-nurse.com","is-a-painter.com","is-a-patsfan.org","is-a-personaltrainer.com","is-a-photographer.com","is-a-player.com","is-a-republican.com","is-a-rockstar.com","is-a-socialist.com","is-a-soxfan.org","is-a-student.com","is-a-teacher.com","is-a-techie.com","is-a-therapist.com","is-an-accountant.com","is-an-actor.com","is-an-actress.com","is-an-anarchist.com","is-an-artist.com","is-an-engineer.com","is-an-entertainer.com","is-by.us","is-certified.com","is-found.org","is-gone.com","is-into-anime.com","is-into-cars.com","is-into-cartoons.com","is-into-games.com","is-leet.com","is-lost.org","is-not-certified.com","is-saved.org","is-slick.com","is-uberleet.com","is-very-bad.org","is-very-evil.org","is-very-good.org","is-very-nice.org","is-very-sweet.org","is-with-theband.com","isa-geek.com","isa-geek.net","isa-geek.org","isa-hockeynut.com","issmarterthanyou.com","isteingeek.de","istmein.de","kicks-ass.net","kicks-ass.org","knowsitall.info","land-4-sale.us","lebtimnetz.de","leitungsen.de","likes-pie.com","likescandy.com","merseine.nu","mine.nu","misconfused.org","mypets.ws","myphotos.cc","neat-url.com","office-on-the.net","on-the-web.tv","podzone.net","podzone.org","readmyblog.org","saves-the-whales.com","scrapper-site.net","scrapping.cc","selfip.biz","selfip.com","selfip.info","selfip.net","selfip.org","sells-for-less.com","sells-for-u.com","sells-it.net","sellsyourhome.org","servebbs.com","servebbs.net","servebbs.org","serveftp.net","serveftp.org","servegame.org","shacknet.nu","simple-url.com","space-to-rent.com","stuff-4-sale.org","stuff-4-sale.us","teaches-yoga.com","thruhere.net","traeumtgerade.de","webhop.biz","webhop.info","webhop.net","webhop.org","worse-than.tv","writesthisblog.com","ddnss.de","dyn.ddnss.de","dyndns.ddnss.de","dyndns1.de","dyn-ip24.de","home-webserver.de","dyn.home-webserver.de","myhome-server.de","ddnss.org","definima.net","definima.io","ondigitalocean.app","*.digitaloceanspaces.com","bci.dnstrace.pro","ddnsfree.com","ddnsgeek.com","giize.com","gleeze.com","kozow.com","loseyourip.com","ooguy.com","theworkpc.com","casacam.net","dynu.net","accesscam.org","camdvr.org","freeddns.org","mywire.org","webredirect.org","myddns.rocks","blogsite.xyz","dynv6.net","e4.cz","eero.online","eero-stage.online","elementor.cloud","elementor.cool","en-root.fr","mytuleap.com","tuleap-partners.com","encr.app","encoreapi.com","onred.one","staging.onred.one","eu.encoway.cloud","eu.org","al.eu.org","asso.eu.org","at.eu.org","au.eu.org","be.eu.org","bg.eu.org","ca.eu.org","cd.eu.org","ch.eu.org","cn.eu.org","cy.eu.org","cz.eu.org","de.eu.org","dk.eu.org","edu.eu.org","ee.eu.org","es.eu.org","fi.eu.org","fr.eu.org","gr.eu.org","hr.eu.org","hu.eu.org","ie.eu.org","il.eu.org","in.eu.org","int.eu.org","is.eu.org","it.eu.org","jp.eu.org","kr.eu.org","lt.eu.org","lu.eu.org","lv.eu.org","mc.eu.org","me.eu.org","mk.eu.org","mt.eu.org","my.eu.org","net.eu.org","ng.eu.org","nl.eu.org","no.eu.org","nz.eu.org","paris.eu.org","pl.eu.org","pt.eu.org","q-a.eu.org","ro.eu.org","ru.eu.org","se.eu.org","si.eu.org","sk.eu.org","tr.eu.org","uk.eu.org","us.eu.org","eurodir.ru","eu-1.evennode.com","eu-2.evennode.com","eu-3.evennode.com","eu-4.evennode.com","us-1.evennode.com","us-2.evennode.com","us-3.evennode.com","us-4.evennode.com","twmail.cc","twmail.net","twmail.org","mymailer.com.tw","url.tw","onfabrica.com","apps.fbsbx.com","ru.net","adygeya.ru","bashkiria.ru","bir.ru","cbg.ru","com.ru","dagestan.ru","grozny.ru","kalmykia.ru","kustanai.ru","marine.ru","mordovia.ru","msk.ru","mytis.ru","nalchik.ru","nov.ru","pyatigorsk.ru","spb.ru","vladikavkaz.ru","vladimir.ru","abkhazia.su","adygeya.su","aktyubinsk.su","arkhangelsk.su","armenia.su","ashgabad.su","azerbaijan.su","balashov.su","bashkiria.su","bryansk.su","bukhara.su","chimkent.su","dagestan.su","east-kazakhstan.su","exnet.su","georgia.su","grozny.su","ivanovo.su","jambyl.su","kalmykia.su","kaluga.su","karacol.su","karaganda.su","karelia.su","khakassia.su","krasnodar.su","kurgan.su","kustanai.su","lenug.su","mangyshlak.su","mordovia.su","msk.su","murmansk.su","nalchik.su","navoi.su","north-kazakhstan.su","nov.su","obninsk.su","penza.su","pokrovsk.su","sochi.su","spb.su","tashkent.su","termez.su","togliatti.su","troitsk.su","tselinograd.su","tula.su","tuva.su","vladikavkaz.su","vladimir.su","vologda.su","channelsdvr.net","u.channelsdvr.net","edgecompute.app","fastly-terrarium.com","fastlylb.net","map.fastlylb.net","freetls.fastly.net","map.fastly.net","a.prod.fastly.net","global.prod.fastly.net","a.ssl.fastly.net","b.ssl.fastly.net","global.ssl.fastly.net","fastvps-server.com","fastvps.host","myfast.host","fastvps.site","myfast.space","fedorainfracloud.org","fedorapeople.org","cloud.fedoraproject.org","app.os.fedoraproject.org","app.os.stg.fedoraproject.org","conn.uk","copro.uk","hosp.uk","mydobiss.com","fh-muenster.io","filegear.me","filegear-au.me","filegear-de.me","filegear-gb.me","filegear-ie.me","filegear-jp.me","filegear-sg.me","firebaseapp.com","fireweb.app","flap.id","onflashdrive.app","fldrv.com","fly.dev","edgeapp.net","shw.io","flynnhosting.net","forgeblocks.com","id.forgerock.io","framer.app","framercanvas.com","*.frusky.de","ravpage.co.il","0e.vc","freebox-os.com","freeboxos.com","fbx-os.fr","fbxos.fr","freebox-os.fr","freeboxos.fr","freedesktop.org","freemyip.com","wien.funkfeuer.at","*.futurecms.at","*.ex.futurecms.at","*.in.futurecms.at","futurehosting.at","futuremailing.at","*.ex.ortsinfo.at","*.kunden.ortsinfo.at","*.statics.cloud","independent-commission.uk","independent-inquest.uk","independent-inquiry.uk","independent-panel.uk","independent-review.uk","public-inquiry.uk","royal-commission.uk","campaign.gov.uk","service.gov.uk","api.gov.uk","gehirn.ne.jp","usercontent.jp","gentapps.com","gentlentapis.com","lab.ms","cdn-edges.net","ghost.io","gsj.bz","githubusercontent.com","githubpreview.dev","github.io","gitlab.io","gitapp.si","gitpage.si","glitch.me","nog.community","co.ro","shop.ro","lolipop.io","angry.jp","babyblue.jp","babymilk.jp","backdrop.jp","bambina.jp","bitter.jp","blush.jp","boo.jp","boy.jp","boyfriend.jp","but.jp","candypop.jp","capoo.jp","catfood.jp","cheap.jp","chicappa.jp","chillout.jp","chips.jp","chowder.jp","chu.jp","ciao.jp","cocotte.jp","coolblog.jp","cranky.jp","cutegirl.jp","daa.jp","deca.jp","deci.jp","digick.jp","egoism.jp","fakefur.jp","fem.jp","flier.jp","floppy.jp","fool.jp","frenchkiss.jp","girlfriend.jp","girly.jp","gloomy.jp","gonna.jp","greater.jp","hacca.jp","heavy.jp","her.jp","hiho.jp","hippy.jp","holy.jp","hungry.jp","icurus.jp","itigo.jp","jellybean.jp","kikirara.jp","kill.jp","kilo.jp","kuron.jp","littlestar.jp","lolipopmc.jp","lolitapunk.jp","lomo.jp","lovepop.jp","lovesick.jp","main.jp","mods.jp","mond.jp","mongolian.jp","moo.jp","namaste.jp","nikita.jp","nobushi.jp","noor.jp","oops.jp","parallel.jp","parasite.jp","pecori.jp","peewee.jp","penne.jp","pepper.jp","perma.jp","pigboat.jp","pinoko.jp","punyu.jp","pupu.jp","pussycat.jp","pya.jp","raindrop.jp","readymade.jp","sadist.jp","schoolbus.jp","secret.jp","staba.jp","stripper.jp","sub.jp","sunnyday.jp","thick.jp","tonkotsu.jp","under.jp","upper.jp","velvet.jp","verse.jp","versus.jp","vivian.jp","watson.jp","weblike.jp","whitesnow.jp","zombie.jp","heteml.net","cloudapps.digital","london.cloudapps.digital","pymnt.uk","homeoffice.gov.uk","ro.im","goip.de","run.app","a.run.app","web.app","*.0emm.com","appspot.com","*.r.appspot.com","codespot.com","googleapis.com","googlecode.com","pagespeedmobilizer.com","publishproxy.com","withgoogle.com","withyoutube.com","*.gateway.dev","cloud.goog","translate.goog","*.usercontent.goog","cloudfunctions.net","blogspot.ae","blogspot.al","blogspot.am","blogspot.ba","blogspot.be","blogspot.bg","blogspot.bj","blogspot.ca","blogspot.cf","blogspot.ch","blogspot.cl","blogspot.co.at","blogspot.co.id","blogspot.co.il","blogspot.co.ke","blogspot.co.nz","blogspot.co.uk","blogspot.co.za","blogspot.com","blogspot.com.ar","blogspot.com.au","blogspot.com.br","blogspot.com.by","blogspot.com.co","blogspot.com.cy","blogspot.com.ee","blogspot.com.eg","blogspot.com.es","blogspot.com.mt","blogspot.com.ng","blogspot.com.tr","blogspot.com.uy","blogspot.cv","blogspot.cz","blogspot.de","blogspot.dk","blogspot.fi","blogspot.fr","blogspot.gr","blogspot.hk","blogspot.hr","blogspot.hu","blogspot.ie","blogspot.in","blogspot.is","blogspot.it","blogspot.jp","blogspot.kr","blogspot.li","blogspot.lt","blogspot.lu","blogspot.md","blogspot.mk","blogspot.mr","blogspot.mx","blogspot.my","blogspot.nl","blogspot.no","blogspot.pe","blogspot.pt","blogspot.qa","blogspot.re","blogspot.ro","blogspot.rs","blogspot.ru","blogspot.se","blogspot.sg","blogspot.si","blogspot.sk","blogspot.sn","blogspot.td","blogspot.tw","blogspot.ug","blogspot.vn","goupile.fr","gov.nl","awsmppl.com","günstigbestellen.de","günstigliefern.de","fin.ci","free.hr","caa.li","ua.rs","conf.se","hs.zone","hs.run","hashbang.sh","hasura.app","hasura-app.io","pages.it.hs-heilbronn.de","hepforge.org","herokuapp.com","herokussl.com","ravendb.cloud","myravendb.com","ravendb.community","ravendb.me","development.run","ravendb.run","homesklep.pl","secaas.hk","hoplix.shop","orx.biz","biz.gl","col.ng","firm.ng","gen.ng","ltd.ng","ngo.ng","edu.scot","sch.so","hostyhosting.io","häkkinen.fi","*.moonscale.io","moonscale.net","iki.fi","ibxos.it","iliadboxos.it","impertrixcdn.com","impertrix.com","smushcdn.com","wphostedmail.com","wpmucdn.com","tempurl.host","wpmudev.host","dyn-berlin.de","in-berlin.de","in-brb.de","in-butter.de","in-dsl.de","in-dsl.net","in-dsl.org","in-vpn.de","in-vpn.net","in-vpn.org","biz.at","info.at","info.cx","ac.leg.br","al.leg.br","am.leg.br","ap.leg.br","ba.leg.br","ce.leg.br","df.leg.br","es.leg.br","go.leg.br","ma.leg.br","mg.leg.br","ms.leg.br","mt.leg.br","pa.leg.br","pb.leg.br","pe.leg.br","pi.leg.br","pr.leg.br","rj.leg.br","rn.leg.br","ro.leg.br","rr.leg.br","rs.leg.br","sc.leg.br","se.leg.br","sp.leg.br","to.leg.br","pixolino.com","na4u.ru","iopsys.se","ipifony.net","iservschule.de","mein-iserv.de","schulplattform.de","schulserver.de","test-iserv.de","iserv.dev","iobb.net","mel.cloudlets.com.au","cloud.interhostsolutions.be","users.scale.virtualcloud.com.br","mycloud.by","alp1.ae.flow.ch","appengine.flow.ch","es-1.axarnet.cloud","diadem.cloud","vip.jelastic.cloud","jele.cloud","it1.eur.aruba.jenv-aruba.cloud","it1.jenv-aruba.cloud","keliweb.cloud","cs.keliweb.cloud","oxa.cloud","tn.oxa.cloud","uk.oxa.cloud","primetel.cloud","uk.primetel.cloud","ca.reclaim.cloud","uk.reclaim.cloud","us.reclaim.cloud","ch.trendhosting.cloud","de.trendhosting.cloud","jele.club","amscompute.com","clicketcloud.com","dopaas.com","hidora.com","paas.hosted-by-previder.com","rag-cloud.hosteur.com","rag-cloud-ch.hosteur.com","jcloud.ik-server.com","jcloud-ver-jpc.ik-server.com","demo.jelastic.com","kilatiron.com","paas.massivegrid.com","jed.wafaicloud.com","lon.wafaicloud.com","ryd.wafaicloud.com","j.scaleforce.com.cy","jelastic.dogado.eu","fi.cloudplatform.fi","demo.datacenter.fi","paas.datacenter.fi","jele.host","mircloud.host","paas.beebyte.io","sekd1.beebyteapp.io","jele.io","cloud-fr1.unispace.io","jc.neen.it","cloud.jelastic.open.tim.it","jcloud.kz","upaas.kazteleport.kz","cloudjiffy.net","fra1-de.cloudjiffy.net","west1-us.cloudjiffy.net","jls-sto1.elastx.net","jls-sto2.elastx.net","jls-sto3.elastx.net","faststacks.net","fr-1.paas.massivegrid.net","lon-1.paas.massivegrid.net","lon-2.paas.massivegrid.net","ny-1.paas.massivegrid.net","ny-2.paas.massivegrid.net","sg-1.paas.massivegrid.net","jelastic.saveincloud.net","nordeste-idc.saveincloud.net","j.scaleforce.net","jelastic.tsukaeru.net","sdscloud.pl","unicloud.pl","mircloud.ru","jelastic.regruhosting.ru","enscaled.sg","jele.site","jelastic.team","orangecloud.tn","j.layershift.co.uk","phx.enscaled.us","mircloud.us","myjino.ru","*.hosting.myjino.ru","*.landing.myjino.ru","*.spectrum.myjino.ru","*.vps.myjino.ru","jotelulu.cloud","*.triton.zone","*.cns.joyent.com","js.org","kaas.gg","khplay.nl","ktistory.com","kapsi.fi","keymachine.de","kinghost.net","uni5.net","knightpoint.systems","koobin.events","oya.to","kuleuven.cloud","ezproxy.kuleuven.be","co.krd","edu.krd","krellian.net","webthings.io","git-repos.de","lcube-server.de","svn-repos.de","leadpages.co","lpages.co","lpusercontent.com","lelux.site","co.business","co.education","co.events","co.financial","co.network","co.place","co.technology","app.lmpm.com","linkyard.cloud","linkyard-cloud.ch","members.linode.com","*.nodebalancer.linode.com","*.linodeobjects.com","ip.linodeusercontent.com","we.bs","*.user.localcert.dev","localzone.xyz","loginline.app","loginline.dev","loginline.io","loginline.services","loginline.site","servers.run","lohmus.me","krasnik.pl","leczna.pl","lubartow.pl","lublin.pl","poniatowa.pl","swidnik.pl","glug.org.uk","lug.org.uk","lugs.org.uk","barsy.bg","barsy.co.uk","barsyonline.co.uk","barsycenter.com","barsyonline.com","barsy.club","barsy.de","barsy.eu","barsy.in","barsy.info","barsy.io","barsy.me","barsy.menu","barsy.mobi","barsy.net","barsy.online","barsy.org","barsy.pro","barsy.pub","barsy.ro","barsy.shop","barsy.site","barsy.support","barsy.uk","*.magentosite.cloud","mayfirst.info","mayfirst.org","hb.cldmail.ru","cn.vu","mazeplay.com","mcpe.me","mcdir.me","mcdir.ru","mcpre.ru","vps.mcdir.ru","mediatech.by","mediatech.dev","hra.health","miniserver.com","memset.net","messerli.app","*.cloud.metacentrum.cz","custom.metacentrum.cz","flt.cloud.muni.cz","usr.cloud.muni.cz","meteorapp.com","eu.meteorapp.com","co.pl","*.azurecontainer.io","azurewebsites.net","azure-mobile.net","cloudapp.net","azurestaticapps.net","1.azurestaticapps.net","centralus.azurestaticapps.net","eastasia.azurestaticapps.net","eastus2.azurestaticapps.net","westeurope.azurestaticapps.net","westus2.azurestaticapps.net","csx.cc","mintere.site","forte.id","mozilla-iot.org","bmoattachments.org","net.ru","org.ru","pp.ru","hostedpi.com","customer.mythic-beasts.com","caracal.mythic-beasts.com","fentiger.mythic-beasts.com","lynx.mythic-beasts.com","ocelot.mythic-beasts.com","oncilla.mythic-beasts.com","onza.mythic-beasts.com","sphinx.mythic-beasts.com","vs.mythic-beasts.com","x.mythic-beasts.com","yali.mythic-beasts.com","cust.retrosnub.co.uk","ui.nabu.casa","pony.club","of.fashion","in.london","of.london","from.marketing","with.marketing","for.men","repair.men","and.mom","for.mom","for.one","under.one","for.sale","that.win","from.work","to.work","cloud.nospamproxy.com","netlify.app","4u.com","ngrok.io","nh-serv.co.uk","nfshost.com","*.developer.app","noop.app","*.northflank.app","*.build.run","*.code.run","*.database.run","*.migration.run","noticeable.news","dnsking.ch","mypi.co","n4t.co","001www.com","ddnslive.com","myiphost.com","forumz.info","16-b.it","32-b.it","64-b.it","soundcast.me","tcp4.me","dnsup.net","hicam.net","now-dns.net","ownip.net","vpndns.net","dynserv.org","now-dns.org","x443.pw","now-dns.top","ntdll.top","freeddns.us","crafting.xyz","zapto.xyz","nsupdate.info","nerdpol.ovh","blogsyte.com","brasilia.me","cable-modem.org","ciscofreak.com","collegefan.org","couchpotatofries.org","damnserver.com","ddns.me","ditchyourip.com","dnsfor.me","dnsiskinky.com","dvrcam.info","dynns.com","eating-organic.net","fantasyleague.cc","geekgalaxy.com","golffan.us","health-carereform.com","homesecuritymac.com","homesecuritypc.com","hopto.me","ilovecollege.info","loginto.me","mlbfan.org","mmafan.biz","myactivedirectory.com","mydissent.net","myeffect.net","mymediapc.net","mypsx.net","mysecuritycamera.com","mysecuritycamera.net","mysecuritycamera.org","net-freaks.com","nflfan.org","nhlfan.net","no-ip.ca","no-ip.co.uk","no-ip.net","noip.us","onthewifi.com","pgafan.net","point2this.com","pointto.us","privatizehealthinsurance.net","quicksytes.com","read-books.org","securitytactics.com","serveexchange.com","servehumour.com","servep2p.com","servesarcasm.com","stufftoread.com","ufcfan.org","unusualperson.com","workisboring.com","3utilities.com","bounceme.net","ddns.net","ddnsking.com","gotdns.ch","hopto.org","myftp.biz","myftp.org","myvnc.com","no-ip.biz","no-ip.info","no-ip.org","noip.me","redirectme.net","servebeer.com","serveblog.net","servecounterstrike.com","serveftp.com","servegame.com","servehalflife.com","servehttp.com","serveirc.com","serveminecraft.net","servemp3.com","servepics.com","servequake.com","sytes.net","webhop.me","zapto.org","stage.nodeart.io","pcloud.host","nyc.mn","static.observableusercontent.com","cya.gg","omg.lol","cloudycluster.net","omniwe.site","service.one","nid.io","opensocial.site","opencraft.hosting","orsites.com","operaunite.com","tech.orange","authgear-staging.com","authgearapps.com","skygearapp.com","outsystemscloud.com","*.webpaas.ovh.net","*.hosting.ovh.net","ownprovider.com","own.pm","*.owo.codes","ox.rs","oy.lc","pgfog.com","pagefrontapp.com","pagexl.com","*.paywhirl.com","bar0.net","bar1.net","bar2.net","rdv.to","art.pl","gliwice.pl","krakow.pl","poznan.pl","wroc.pl","zakopane.pl","pantheonsite.io","gotpantheon.com","mypep.link","perspecta.cloud","lk3.ru","on-web.fr","bc.platform.sh","ent.platform.sh","eu.platform.sh","us.platform.sh","*.platformsh.site","*.tst.site","platter-app.com","platter-app.dev","platterp.us","pdns.page","plesk.page","pleskns.com","dyn53.io","onporter.run","co.bn","postman-echo.com","pstmn.io","mock.pstmn.io","httpbin.org","prequalifyme.today","xen.prgmr.com","priv.at","prvcy.page","*.dweb.link","protonet.io","chirurgiens-dentistes-en-france.fr","byen.site","pubtls.org","pythonanywhere.com","eu.pythonanywhere.com","qoto.io","qualifioapp.com","qbuser.com","cloudsite.builders","instances.spawn.cc","instantcloud.cn","ras.ru","qa2.com","qcx.io","*.sys.qcx.io","dev-myqnapcloud.com","alpha-myqnapcloud.com","myqnapcloud.com","*.quipelements.com","vapor.cloud","vaporcloud.io","rackmaze.com","rackmaze.net","g.vbrplsbx.io","*.on-k3s.io","*.on-rancher.cloud","*.on-rio.io","readthedocs.io","rhcloud.com","app.render.com","onrender.com","repl.co","id.repl.co","repl.run","resindevice.io","devices.resinstaging.io","hzc.io","wellbeingzone.eu","wellbeingzone.co.uk","adimo.co.uk","itcouldbewor.se","git-pages.rit.edu","rocky.page","биз.рус","ком.рус","крым.рус","мир.рус","мск.рус","орг.рус","самара.рус","сочи.рус","спб.рус","я.рус","*.builder.code.com","*.dev-builder.code.com","*.stg-builder.code.com","sandcats.io","logoip.de","logoip.com","fr-par-1.baremetal.scw.cloud","fr-par-2.baremetal.scw.cloud","nl-ams-1.baremetal.scw.cloud","fnc.fr-par.scw.cloud","functions.fnc.fr-par.scw.cloud","k8s.fr-par.scw.cloud","nodes.k8s.fr-par.scw.cloud","s3.fr-par.scw.cloud","s3-website.fr-par.scw.cloud","whm.fr-par.scw.cloud","priv.instances.scw.cloud","pub.instances.scw.cloud","k8s.scw.cloud","k8s.nl-ams.scw.cloud","nodes.k8s.nl-ams.scw.cloud","s3.nl-ams.scw.cloud","s3-website.nl-ams.scw.cloud","whm.nl-ams.scw.cloud","k8s.pl-waw.scw.cloud","nodes.k8s.pl-waw.scw.cloud","s3.pl-waw.scw.cloud","s3-website.pl-waw.scw.cloud","scalebook.scw.cloud","smartlabeling.scw.cloud","dedibox.fr","schokokeks.net","gov.scot","service.gov.scot","scrysec.com","firewall-gateway.com","firewall-gateway.de","my-gateway.de","my-router.de","spdns.de","spdns.eu","firewall-gateway.net","my-firewall.org","myfirewall.org","spdns.org","seidat.net","sellfy.store","senseering.net","minisite.ms","magnet.page","biz.ua","co.ua","pp.ua","shiftcrypto.dev","shiftcrypto.io","shiftedit.io","myshopblocks.com","myshopify.com","shopitsite.com","shopware.store","mo-siemens.io","1kapp.com","appchizi.com","applinzi.com","sinaapp.com","vipsinaapp.com","siteleaf.net","bounty-full.com","alpha.bounty-full.com","beta.bounty-full.com","small-web.org","vp4.me","try-snowplow.com","srht.site","stackhero-network.com","musician.io","novecore.site","static.land","dev.static.land","sites.static.land","storebase.store","vps-host.net","atl.jelastic.vps-host.net","njs.jelastic.vps-host.net","ric.jelastic.vps-host.net","playstation-cloud.com","apps.lair.io","*.stolos.io","spacekit.io","customer.speedpartner.de","myspreadshop.at","myspreadshop.com.au","myspreadshop.be","myspreadshop.ca","myspreadshop.ch","myspreadshop.com","myspreadshop.de","myspreadshop.dk","myspreadshop.es","myspreadshop.fi","myspreadshop.fr","myspreadshop.ie","myspreadshop.it","myspreadshop.net","myspreadshop.nl","myspreadshop.no","myspreadshop.pl","myspreadshop.se","myspreadshop.co.uk","api.stdlib.com","storj.farm","utwente.io","soc.srcf.net","user.srcf.net","temp-dns.com","supabase.co","supabase.in","supabase.net","su.paba.se","*.s5y.io","*.sensiosite.cloud","syncloud.it","dscloud.biz","direct.quickconnect.cn","dsmynas.com","familyds.com","diskstation.me","dscloud.me","i234.me","myds.me","synology.me","dscloud.mobi","dsmynas.net","familyds.net","dsmynas.org","familyds.org","vpnplus.to","direct.quickconnect.to","tabitorder.co.il","taifun-dns.de","beta.tailscale.net","ts.net","gda.pl","gdansk.pl","gdynia.pl","med.pl","sopot.pl","site.tb-hosting.com","edugit.io","s3.teckids.org","telebit.app","telebit.io","*.telebit.xyz","gwiddle.co.uk","*.firenet.ch","*.svc.firenet.ch","reservd.com","thingdustdata.com","cust.dev.thingdust.io","cust.disrec.thingdust.io","cust.prod.thingdust.io","cust.testing.thingdust.io","reservd.dev.thingdust.io","reservd.disrec.thingdust.io","reservd.testing.thingdust.io","tickets.io","arvo.network","azimuth.network","tlon.network","torproject.net","pages.torproject.net","bloxcms.com","townnews-staging.com","tbits.me","12hp.at","2ix.at","4lima.at","lima-city.at","12hp.ch","2ix.ch","4lima.ch","lima-city.ch","trafficplex.cloud","de.cool","12hp.de","2ix.de","4lima.de","lima-city.de","1337.pictures","clan.rip","lima-city.rocks","webspace.rocks","lima.zone","*.transurl.be","*.transurl.eu","*.transurl.nl","site.transip.me","tuxfamily.org","dd-dns.de","diskstation.eu","diskstation.org","dray-dns.de","draydns.de","dyn-vpn.de","dynvpn.de","mein-vigor.de","my-vigor.de","my-wan.de","syno-ds.de","synology-diskstation.de","synology-ds.de","typedream.app","pro.typeform.com","uber.space","*.uberspace.de","hk.com","hk.org","ltd.hk","inc.hk","name.pm","sch.tf","biz.wf","sch.wf","org.yt","virtualuser.de","virtual-user.de","upli.io","urown.cloud","dnsupdate.info","lib.de.us","2038.io","vercel.app","vercel.dev","now.sh","router.management","v-info.info","voorloper.cloud","neko.am","nyaa.am","be.ax","cat.ax","es.ax","eu.ax","gg.ax","mc.ax","us.ax","xy.ax","nl.ci","xx.gl","app.gp","blog.gt","de.gt","to.gt","be.gy","cc.hn","blog.kg","io.kg","jp.kg","tv.kg","uk.kg","us.kg","de.ls","at.md","de.md","jp.md","to.md","indie.porn","vxl.sh","ch.tc","me.tc","we.tc","nyan.to","at.vg","blog.vu","dev.vu","me.vu","v.ua","*.vultrobjects.com","wafflecell.com","*.webhare.dev","reserve-online.net","reserve-online.com","bookonline.app","hotelwithflight.com","wedeploy.io","wedeploy.me","wedeploy.sh","remotewd.com","pages.wiardweb.com","wmflabs.org","toolforge.org","wmcloud.org","panel.gg","daemon.panel.gg","messwithdns.com","woltlab-demo.com","myforum.community","community-pro.de","diskussionsbereich.de","community-pro.net","meinforum.net","affinitylottery.org.uk","raffleentry.org.uk","weeklylottery.org.uk","wpenginepowered.com","js.wpenginepowered.com","wixsite.com","editorx.io","half.host","xnbay.com","u2.xnbay.com","u2-local.xnbay.com","cistron.nl","demon.nl","xs4all.space","yandexcloud.net","storage.yandexcloud.net","website.yandexcloud.net","official.academy","yolasite.com","ybo.faith","yombo.me","homelink.one","ybo.party","ybo.review","ybo.science","ybo.trade","ynh.fr","nohost.me","noho.st","za.net","za.org","bss.design","basicserver.io","virtualserver.io","enterprisecloud.nu"]}}),Zt=ae({"node_modules/psl/index.js"(e){var a=_o(),o={};o.rules=Qt().map(function(t){return{rule:t,suffix:t.replace(/^(\*\.|\!)/,""),punySuffix:-1,wildcard:t.charAt(0)==="*",exception:t.charAt(0)==="!"}}),o.endsWith=function(t,i){return t.indexOf(i,t.length-i.length)!==-1},o.findRule=function(t){var i=a.toASCII(t);return o.rules.reduce(function(s,n){return n.punySuffix===-1&&(n.punySuffix=a.toASCII(n.suffix)),!o.endsWith(i,"."+n.punySuffix)&&i!==n.punySuffix?s:n},null)},e.errorCodes={DOMAIN_TOO_SHORT:"Domain name too short.",DOMAIN_TOO_LONG:"Domain name too long. It should be no more than 255 chars.",LABEL_STARTS_WITH_DASH:"Domain name label can not start with a dash.",LABEL_ENDS_WITH_DASH:"Domain name label can not end with a dash.",LABEL_TOO_LONG:"Domain name label should be at most 63 chars long.",LABEL_TOO_SHORT:"Domain name label should be at least 1 character long.",LABEL_INVALID_CHARS:"Domain name label can only contain alphanumeric characters or dashes."},o.validate=function(t){var i=a.toASCII(t);if(i.length<1)return"DOMAIN_TOO_SHORT";if(i.length>255)return"DOMAIN_TOO_LONG";for(var s=i.split("."),n,r=0;r<s.length;++r){if(n=s[r],!n.length)return"LABEL_TOO_SHORT";if(n.length>63)return"LABEL_TOO_LONG";if(n.charAt(0)==="-")return"LABEL_STARTS_WITH_DASH";if(n.charAt(n.length-1)==="-")return"LABEL_ENDS_WITH_DASH";if(!/^[a-z0-9\-]+$/.test(n))return"LABEL_INVALID_CHARS"}},e.parse=function(t){if(typeof t!="string")throw new TypeError("Domain name must be a string.");var i=t.slice(0).toLowerCase();i.charAt(i.length-1)==="."&&(i=i.slice(0,i.length-1));var s=o.validate(i);if(s)return{input:t,error:{message:e.errorCodes[s],code:s}};var n={input:t,tld:null,sld:null,domain:null,subdomain:null,listed:!1},r=i.split(".");if(r[r.length-1]==="local")return n;var c=function(){return/xn--/.test(i)&&(n.domain&&(n.domain=a.toASCII(n.domain)),n.subdomain&&(n.subdomain=a.toASCII(n.subdomain))),n},h=o.findRule(i);if(!h)return r.length<2?n:(n.tld=r.pop(),n.sld=r.pop(),n.domain=[n.sld,n.tld].join("."),r.length&&(n.subdomain=r.pop()),c());n.listed=!0;var p=h.suffix.split("."),m=r.slice(0,r.length-p.length);return h.exception&&m.push(p.shift()),n.tld=p.join("."),!m.length||(h.wildcard&&(p.unshift(m.pop()),n.tld=p.join(".")),!m.length)||(n.sld=m.pop(),n.domain=[n.sld,n.tld].join("."),m.length&&(n.subdomain=m.join("."))),c()},e.get=function(t){return t&&e.parse(t).domain||null},e.isValid=function(t){var i=e.parse(t);return!!(i.domain&&i.listed)}}}),Io=ae({"node_modules/tough-cookie/lib/pubsuffix-psl.js"(e){var a=Zt(),o=["local","example","invalid","localhost","test"],t=["localhost","invalid"];function i(s,n={}){const r=s.split("."),c=r[r.length-1],h=!!n.allowSpecialUseDomain,p=!!n.ignoreError;if(h&&o.includes(c)){if(r.length>1)return`${r[r.length-2]}.${c}`;if(t.includes(c))return`${c}`}if(!p&&o.includes(c))throw new Error(`Cookie has domain set to the public suffix "${c}" which is a special use domain. To allow this, configure your CookieJar with {allowSpecialUseDomain:true, rejectPublicSuffixes: false}.`);return a.get(s)}e.getPublicSuffix=i}}),To=ae({"node_modules/tough-cookie/lib/store.js"(e){var a=class{constructor(){this.synchronous=!1}findCookie(o,t,i,s){throw new Error("findCookie is not implemented")}findCookies(o,t,i,s){throw new Error("findCookies is not implemented")}putCookie(o,t){throw new Error("putCookie is not implemented")}updateCookie(o,t,i){throw new Error("updateCookie is not implemented")}removeCookie(o,t,i,s){throw new Error("removeCookie is not implemented")}removeCookies(o,t,i){throw new Error("removeCookies is not implemented")}removeAllCookies(o){throw new Error("removeAllCookies is not implemented")}getAllCookies(o){throw new Error("getAllCookies is not implemented (therefore jar cannot be serialized)")}};e.Store=a}}),Po=ae({"node_modules/universalify/index.js"(e){e.fromCallback=function(a){return Object.defineProperty(function(){if(typeof arguments[arguments.length-1]=="function")a.apply(this,arguments);else return new Promise((o,t)=>{arguments[arguments.length]=(i,s)=>{if(i)return t(i);o(s)},arguments.length++,a.apply(this,arguments)})},"name",{value:a.name})},e.fromPromise=function(a){return Object.defineProperty(function(){const o=arguments[arguments.length-1];if(typeof o!="function")return a.apply(this,arguments);delete arguments[arguments.length-1],arguments.length--,a.apply(this,arguments).then(t=>o(null,t),o)},"name",{value:a.name})}}}),Do=ae({"node_modules/tough-cookie/lib/permuteDomain.js"(e){var a=Io();function o(t,i){const s=a.getPublicSuffix(t,{allowSpecialUseDomain:i});if(!s)return null;if(s==t)return[t];t.slice(-1)=="."&&(t=t.slice(0,-1));const r=t.slice(0,-(s.length+1)).split(".").reverse();let c=s;const h=[c];for(;r.length;)c=`${r.shift()}.${c}`,h.push(c);return h}e.permuteDomain=o}}),Oo=ae({"node_modules/tough-cookie/lib/pathMatch.js"(e){function a(o,t){return t===o||o.indexOf(t)===0&&(t.substr(-1)==="/"||o.substr(t.length,1)==="/")}e.pathMatch=a}}),qo=ae({"node_modules/tough-cookie/lib/utilHelper.js"(e){function a(){try{return Gt("util")}catch{return null}}function o(){return Symbol.for("nodejs.util.inspect.custom")}function t(i){const n=(i.requireUtil||a)();return n?n.inspect.custom:null}e.getUtilInspect=function(s,n={}){const c=(n.requireUtil||a)();return function(p,m,v){return c?c.inspect(p,m,v):s(p)}},e.getCustomInspectSymbol=function(s={}){return(s.lookupCustomInspectSymbol||o)()||t(s)}}}),ei=ae({"node_modules/tough-cookie/lib/memstore.js"(e){var{fromCallback:a}=Po(),o=To().Store,t=Do().permuteDomain,i=Oo().pathMatch,{getCustomInspectSymbol:s,getUtilInspect:n}=qo(),r=class extends o{constructor(){super(),this.synchronous=!0,this.idx=Object.create(null);const m=s();m&&(this[m]=this.inspect)}inspect(){return`{ idx: ${{inspect:n(c)}.inspect(this.idx,!1,2)} }`}findCookie(m,v,b,y){return!this.idx[m]||!this.idx[m][v]?y(null,void 0):y(null,this.idx[m][v][b]||null)}findCookies(m,v,b,y){const z=[];if(typeof b=="function"&&(y=b,b=!0),!m)return y(null,[]);let C;v?C=function(g){Object.keys(g).forEach(j=>{if(i(v,j)){const E=g[j];for(const w in E)z.push(E[w])}})}:C=function(g){for(const j in g){const E=g[j];for(const w in E)z.push(E[w])}};const A=t(m,b)||[m],q=this.idx;A.forEach(R=>{const g=q[R];g&&C(g)}),y(null,z)}putCookie(m,v){this.idx[m.domain]||(this.idx[m.domain]=Object.create(null)),this.idx[m.domain][m.path]||(this.idx[m.domain][m.path]=Object.create(null)),this.idx[m.domain][m.path][m.key]=m,v(null)}updateCookie(m,v,b){this.putCookie(v,b)}removeCookie(m,v,b,y){this.idx[m]&&this.idx[m][v]&&this.idx[m][v][b]&&delete this.idx[m][v][b],y(null)}removeCookies(m,v,b){return this.idx[m]&&(v?delete this.idx[m][v]:delete this.idx[m]),b(null)}removeAllCookies(m){return this.idx=Object.create(null),m(null)}getAllCookies(m){const v=[],b=this.idx;Object.keys(b).forEach(z=>{Object.keys(b[z]).forEach(A=>{Object.keys(b[z][A]).forEach(R=>{R!==null&&v.push(b[z][A][R])})})}),v.sort((z,C)=>(z.creationIndex||0)-(C.creationIndex||0)),m(null,v)}};["findCookie","findCookies","putCookie","updateCookie","removeCookie","removeCookies","removeAllCookies","getAllCookies"].forEach(m=>{r.prototype[m]=a(r.prototype[m])}),e.MemoryCookieStore=r;function c(m){const v=Object.keys(m);if(v.length===0)return"[Object: null prototype] {}";let b=`[Object: null prototype] {
`;return Object.keys(m).forEach((y,z)=>{b+=h(y,m[y]),z<v.length-1&&(b+=","),b+=`
`}),b+="}",b}function h(m,v){const b="  ";let y=`${b}'${m}': [Object: null prototype] {
`;return Object.keys(v).forEach((z,C,A)=>{y+=p(z,v[z]),C<A.length-1&&(y+=","),y+=`
`}),y+=`${b}}`,y}function p(m,v){const b="    ";let y=`${b}'${m}': [Object: null prototype] {
`;return Object.keys(v).forEach((z,C,A)=>{const q=v[z];y+=`      ${z}: ${q.inspect()}`,C<A.length-1&&(y+=","),y+=`
`}),y+=`${b}}`,y}e.inspectFallback=c}}),ai=ae({"node_modules/tough-cookie/lib/validators.js"(e){var a=Object.prototype.toString;function o(b){return typeof b=="function"}function t(b){return n(b)&&b!==""}function i(b){return c(b,Date)&&p(b.getTime())}function s(b){return b===""||b instanceof String&&b.toString()===""}function n(b){return typeof b=="string"||b instanceof String}function r(b){return a.call(b)==="[object Object]"}function c(b,y){try{return b instanceof y}catch{return!1}}function h(b){return t(b)||r(b)&&"hostname"in b&&"pathname"in b&&"protocol"in b||c(b,URL)}function p(b){return typeof b=="number"&&b%1===0}function m(b,y,z){if(o(y)||(z=y,y=null),r(z)||(z={Error:"Failed Check"}),!b)if(y)y(new v(z));else throw new v(z)}var v=class extends Error{constructor(...b){super(...b)}};e.ParameterError=v,e.isFunction=o,e.isNonEmptyString=t,e.isDate=i,e.isEmptyString=s,e.isString=n,e.isObject=r,e.isUrlStringOrObject=h,e.validate=m}}),oi=ae({"node_modules/tough-cookie/lib/version.js"(e,a){a.exports="4.1.4"}}),ti=ae({"node_modules/tough-cookie/lib/cookie.js"(e){var a=_o(),o=Yt(),t=Io(),i=To().Store,s=ei().MemoryCookieStore,n=Oo().pathMatch,r=ai(),c=oi(),{fromCallback:h}=Po(),{getCustomInspectSymbol:p}=qo(),m=/^[\x21\x23-\x2B\x2D-\x3A\x3C-\x5B\x5D-\x7E]+$/,v=/[\x00-\x1F]/,b=[`
`,"\r","\0"],y=/[\x20-\x3A\x3C-\x7E]+/,z=/[\x09\x20-\x2F\x3B-\x40\x5B-\x60\x7B-\x7E]/,C={jan:0,feb:1,mar:2,apr:3,may:4,jun:5,jul:6,aug:7,sep:8,oct:9,nov:10,dec:11},A=2147483647e3,q=0,R='Invalid sameSiteContext option for getCookies(); expected one of "strict", "lax", or "none"';function g(d){r.validate(r.isNonEmptyString(d),d);const u=String(d).toLowerCase();return u==="none"||u==="lax"||u==="strict"?u:null}var j=Object.freeze({SILENT:"silent",STRICT:"strict",DISABLED:"unsafe-disabled"}),E=/(?:^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$)|(?:^(?:(?:[a-f\d]{1,4}:){7}(?:[a-f\d]{1,4}|:)|(?:[a-f\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|:[a-f\d]{1,4}|:)|(?:[a-f\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,2}|:)|(?:[a-f\d]{1,4}:){4}(?:(?::[a-f\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,3}|:)|(?:[a-f\d]{1,4}:){3}(?:(?::[a-f\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,4}|:)|(?:[a-f\d]{1,4}:){2}(?:(?::[a-f\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,5}|:)|(?:[a-f\d]{1,4}:){1}(?:(?::[a-f\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,6}|:)|(?::(?:(?::[a-f\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}|(?::[a-f\d]{1,4}){1,7}|:)))$)/,w=`
\\[?(?:
(?:[a-fA-F\\d]{1,4}:){7}(?:[a-fA-F\\d]{1,4}|:)|
(?:[a-fA-F\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|:[a-fA-F\\d]{1,4}|:)|
(?:[a-fA-F\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,2}|:)|
(?:[a-fA-F\\d]{1,4}:){4}(?:(?::[a-fA-F\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,3}|:)|
(?:[a-fA-F\\d]{1,4}:){3}(?:(?::[a-fA-F\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,4}|:)|
(?:[a-fA-F\\d]{1,4}:){2}(?:(?::[a-fA-F\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,5}|:)|
(?:[a-fA-F\\d]{1,4}:){1}(?:(?::[a-fA-F\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,6}|:)|
(?::(?:(?::[a-fA-F\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-fA-F\\d]{1,4}){1,7}|:))
)(?:%[0-9a-zA-Z]{1,})?\\]?
`.replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),T=new RegExp(`^${w}$`);function S(d,u,l,x){let k=0;for(;k<d.length;){const L=d.charCodeAt(k);if(L<=47||L>=58)break;k++}return k<u||k>l||!x&&k!=d.length?null:parseInt(d.substr(0,k),10)}function _(d){const u=d.split(":"),l=[0,0,0];if(u.length!==3)return null;for(let x=0;x<3;x++){const k=x==2,L=S(u[x],1,2,k);if(L===null)return null;l[x]=L}return l}function I(d){d=String(d).substr(0,3).toLowerCase();const u=C[d];return u>=0?u:null}function W(d){if(!d)return;const u=d.split(z);if(!u)return;let l=null,x=null,k=null,L=null,D=null,$=null;for(let J=0;J<u.length;J++){const X=u[J].trim();if(!X.length)continue;let O;if(k===null&&(O=_(X),O)){l=O[0],x=O[1],k=O[2];continue}if(L===null&&(O=S(X,1,2,!0),O!==null)){L=O;continue}if(D===null&&(O=I(X),O!==null)){D=O;continue}$===null&&(O=S(X,2,4,!0),O!==null&&($=O,$>=70&&$<=99?$+=1900:$>=0&&$<=69&&($+=2e3)))}if(!(L===null||D===null||$===null||k===null||L<1||L>31||$<1601||l>23||x>59||k>59))return new Date(Date.UTC($,D,L,l,x,k))}function me(d){return r.validate(r.isDate(d),d),d.toUTCString()}function re(d){return d==null?null:(d=d.trim().replace(/^\./,""),T.test(d)&&(d=d.replace("[","").replace("]","")),a&&/[^\u0001-\u007f]/.test(d)&&(d=a.toASCII(d)),d.toLowerCase())}function f(d,u,l){if(d==null||u==null)return null;if(l!==!1&&(d=re(d),u=re(u)),d==u)return!0;const x=d.lastIndexOf(u);return!(x<=0||d.length!==u.length+x||d.substr(x-1,1)!=="."||E.test(d))}function P(d){if(!d||d.substr(0,1)!=="/")return"/";if(d==="/")return d;const u=d.lastIndexOf("/");return u===0?"/":d.slice(0,u)}function V(d){if(r.isEmptyString(d))return d;for(let u=0;u<b.length;u++){const l=d.indexOf(b[u]);l!==-1&&(d=d.substr(0,l))}return d}function B(d,u){d=V(d),r.validate(r.isString(d),d);let l=d.indexOf("=");if(u)l===0&&(d=d.substr(1),l=d.indexOf("="));else if(l<=0)return;let x,k;if(l<=0?(x="",k=d.trim()):(x=d.substr(0,l).trim(),k=d.substr(l+1).trim()),v.test(x)||v.test(k))return;const L=new H;return L.key=x,L.value=k,L}function G(d,u){if((!u||typeof u!="object")&&(u={}),r.isEmptyString(d)||!r.isString(d))return null;d=d.trim();const l=d.indexOf(";"),x=l===-1?d:d.substr(0,l),k=B(x,!!u.loose);if(!k)return;if(l===-1)return k;const L=d.slice(l+1).trim();if(L.length===0)return k;const D=L.split(";");for(;D.length;){const $=D.shift().trim();if($.length===0)continue;const J=$.indexOf("=");let X,O;switch(J===-1?(X=$,O=null):(X=$.substr(0,J),O=$.substr(J+1)),X=X.trim().toLowerCase(),O&&(O=O.trim()),X){case"expires":if(O){const ce=W(O);ce&&(k.expires=ce)}break;case"max-age":if(O&&/^-?[0-9]+$/.test(O)){const ce=parseInt(O,10);k.setMaxAge(ce)}break;case"domain":if(O){const ce=O.trim().replace(/^\./,"");ce&&(k.domain=ce.toLowerCase())}break;case"path":k.path=O&&O[0]==="/"?O:null;break;case"secure":k.secure=!0;break;case"httponly":k.httpOnly=!0;break;case"samesite":switch(O?O.toLowerCase():""){case"strict":k.sameSite="strict";break;case"lax":k.sameSite="lax";break;case"none":k.sameSite="none";break;default:k.sameSite=void 0;break}break;default:k.extensions=k.extensions||[],k.extensions.push($);break}}return k}function Y(d){return r.validate(r.isObject(d),d),!d.key.startsWith("__Secure-")||d.secure}function ue(d){return r.validate(r.isObject(d)),!d.key.startsWith("__Host-")||d.secure&&d.hostOnly&&d.path!=null&&d.path==="/"}function Q(d){let u;try{u=JSON.parse(d)}catch(l){return l}return u}function Z(d){if(!d||r.isEmptyString(d))return null;let u;if(typeof d=="string"){if(u=Q(d),u instanceof Error)return null}else u=d;const l=new H;for(let x=0;x<H.serializableProperties.length;x++){const k=H.serializableProperties[x];u[k]===void 0||u[k]===oe[k]||(k==="expires"||k==="creation"||k==="lastAccessed"?u[k]===null?l[k]=null:l[k]=u[k]=="Infinity"?"Infinity":new Date(u[k]):l[k]=u[k])}return l}function se(d,u){r.validate(r.isObject(d),d),r.validate(r.isObject(u),u);let l=0;const x=d.path?d.path.length:0;if(l=(u.path?u.path.length:0)-x,l!==0)return l;const L=d.creation?d.creation.getTime():A,D=u.creation?u.creation.getTime():A;return l=L-D,l!==0||(l=d.creationIndex-u.creationIndex),l}function ie(d){if(r.validate(r.isString(d)),d==="/")return["/"];const u=[d];for(;d.length>1;){const l=d.lastIndexOf("/");if(l===0)break;d=d.substr(0,l),u.push(d)}return u.push("/"),u}function ne(d){if(d instanceof Object)return d;try{d=decodeURI(d)}catch{}return o(d)}var oe={key:"",value:"",expires:"Infinity",maxAge:null,domain:null,path:null,secure:!1,httpOnly:!1,extensions:null,hostOnly:null,pathIsDefault:null,creation:null,lastAccessed:null,sameSite:void 0},H=class la{constructor(u={}){const l=p();l&&(this[l]=this.inspect),Object.assign(this,oe,u),this.creation=this.creation||new Date,Object.defineProperty(this,"creationIndex",{configurable:!1,enumerable:!1,writable:!0,value:++la.cookiesCreated})}inspect(){const u=Date.now(),l=this.hostOnly!=null?this.hostOnly:"?",x=this.creation?`${u-this.creation.getTime()}ms`:"?",k=this.lastAccessed?`${u-this.lastAccessed.getTime()}ms`:"?";return`Cookie="${this.toString()}; hostOnly=${l}; aAge=${k}; cAge=${x}"`}toJSON(){const u={};for(const l of la.serializableProperties)this[l]!==oe[l]&&(l==="expires"||l==="creation"||l==="lastAccessed"?this[l]===null?u[l]=null:u[l]=this[l]=="Infinity"?"Infinity":this[l].toISOString():l==="maxAge"?this[l]!==null&&(u[l]=this[l]==1/0||this[l]==-1/0?this[l].toString():this[l]):this[l]!==oe[l]&&(u[l]=this[l]));return u}clone(){return Z(this.toJSON())}validate(){if(!m.test(this.value)||this.expires!=1/0&&!(this.expires instanceof Date)&&!W(this.expires)||this.maxAge!=null&&this.maxAge<=0||this.path!=null&&!y.test(this.path))return!1;const u=this.cdomain();return!(u&&(u.match(/\.$/)||t.getPublicSuffix(u)==null))}setExpires(u){u instanceof Date?this.expires=u:this.expires=W(u)||"Infinity"}setMaxAge(u){u===1/0||u===-1/0?this.maxAge=u.toString():this.maxAge=u}cookieString(){let u=this.value;return u==null&&(u=""),this.key===""?u:`${this.key}=${u}`}toString(){let u=this.cookieString();if(this.expires!=1/0&&(this.expires instanceof Date?u+=`; Expires=${me(this.expires)}`:u+=`; Expires=${this.expires}`),this.maxAge!=null&&this.maxAge!=1/0&&(u+=`; Max-Age=${this.maxAge}`),this.domain&&!this.hostOnly&&(u+=`; Domain=${this.domain}`),this.path&&(u+=`; Path=${this.path}`),this.secure&&(u+="; Secure"),this.httpOnly&&(u+="; HttpOnly"),this.sameSite&&this.sameSite!=="none"){const l=la.sameSiteCanonical[this.sameSite.toLowerCase()];u+=`; SameSite=${l||this.sameSite}`}return this.extensions&&this.extensions.forEach(l=>{u+=`; ${l}`}),u}TTL(u){if(this.maxAge!=null)return this.maxAge<=0?0:this.maxAge*1e3;let l=this.expires;return l!=1/0?(l instanceof Date||(l=W(l)||1/0),l==1/0?1/0:l.getTime()-(u||Date.now())):1/0}expiryTime(u){if(this.maxAge!=null){const l=u||this.creation||new Date,x=this.maxAge<=0?-1/0:this.maxAge*1e3;return l.getTime()+x}return this.expires==1/0?1/0:this.expires.getTime()}expiryDate(u){const l=this.expiryTime(u);return l==1/0?new Date(A):l==-1/0?new Date(q):new Date(l)}isPersistent(){return this.maxAge!=null||this.expires!=1/0}canonicalizedDomain(){return this.domain==null?null:re(this.domain)}cdomain(){return this.canonicalizedDomain()}};H.cookiesCreated=0,H.parse=G,H.fromJSON=Z,H.serializableProperties=Object.keys(oe),H.sameSiteLevel={strict:3,lax:2,none:1},H.sameSiteCanonical={strict:"Strict",lax:"Lax"};function je(d){if(d!=null){const u=d.toLowerCase();switch(u){case j.STRICT:case j.SILENT:case j.DISABLED:return u}}return j.SILENT}var pe=class ma{constructor(u,l={rejectPublicSuffixes:!0}){typeof l=="boolean"&&(l={rejectPublicSuffixes:l}),r.validate(r.isObject(l),l),this.rejectPublicSuffixes=l.rejectPublicSuffixes,this.enableLooseMode=!!l.looseMode,this.allowSpecialUseDomain=typeof l.allowSpecialUseDomain=="boolean"?l.allowSpecialUseDomain:!0,this.store=u||new s,this.prefixSecurity=je(l.prefixSecurity),this._cloneSync=ve("clone"),this._importCookiesSync=ve("_importCookies"),this.getCookiesSync=ve("getCookies"),this.getCookieStringSync=ve("getCookieString"),this.getSetCookieStringsSync=ve("getSetCookieStrings"),this.removeAllCookiesSync=ve("removeAllCookies"),this.setCookieSync=ve("setCookie"),this.serializeSync=ve("serialize")}setCookie(u,l,x,k){r.validate(r.isUrlStringOrObject(l),k,x);let L;if(r.isFunction(l))return k=l,k(new Error("No URL was specified"));const D=ne(l);if(r.isFunction(x)&&(k=x,x={}),r.validate(r.isFunction(k),k),!r.isNonEmptyString(u)&&!r.isObject(u)&&u instanceof String&&u.length==0)return k(null);const $=re(D.hostname),J=x.loose||this.enableLooseMode;let X=null;if(x.sameSiteContext&&(X=g(x.sameSiteContext),!X))return k(new Error(R));if(typeof u=="string"||u instanceof String){if(u=H.parse(u,{loose:J}),!u)return L=new Error("Cookie failed to parse"),k(x.ignoreError?null:L)}else if(!(u instanceof H))return L=new Error("First argument to setCookie must be a Cookie object or string"),k(x.ignoreError?null:L);const O=x.now||new Date;if(this.rejectPublicSuffixes&&u.domain&&t.getPublicSuffix(u.cdomain(),{allowSpecialUseDomain:this.allowSpecialUseDomain,ignoreError:x.ignoreError})==null&&!T.test(u.domain))return L=new Error("Cookie has domain set to a public suffix"),k(x.ignoreError?null:L);if(u.domain){if(!f($,u.cdomain(),!1))return L=new Error(`Cookie not in this host's domain. Cookie:${u.cdomain()} Request:${$}`),k(x.ignoreError?null:L);u.hostOnly==null&&(u.hostOnly=!1)}else u.hostOnly=!0,u.domain=$;if((!u.path||u.path[0]!=="/")&&(u.path=P(D.pathname),u.pathIsDefault=!0),x.http===!1&&u.httpOnly)return L=new Error("Cookie is HttpOnly and this isn't an HTTP API"),k(x.ignoreError?null:L);if(u.sameSite!=="none"&&u.sameSite!==void 0&&X&&X==="none")return L=new Error("Cookie is SameSite but this is a cross-origin request"),k(x.ignoreError?null:L);const na=this.prefixSecurity===j.SILENT;if(!(this.prefixSecurity===j.DISABLED)){let N=!1,K;if(Y(u)?ue(u)||(N=!0,K="Cookie has __Host prefix but either Secure or HostOnly attribute is not set or Path is not '/'"):(N=!0,K="Cookie has __Secure prefix but Secure attribute is not set"),N)return k(x.ignoreError||na?null:new Error(K))}const we=this.store;we.updateCookie||(we.updateCookie=function(N,K,qe){this.putCookie(K,qe)});function Ca(N,K){if(N)return k(N);const qe=function(ra){if(ra)return k(ra);k(null,u)};if(K){if(x.http===!1&&K.httpOnly)return N=new Error("old Cookie is HttpOnly and this isn't an HTTP API"),k(x.ignoreError?null:N);u.creation=K.creation,u.creationIndex=K.creationIndex,u.lastAccessed=O,we.updateCookie(K,u,qe)}else u.creation=u.lastAccessed=O,we.putCookie(u,qe)}we.findCookie(u.domain,u.path,u.key,Ca)}getCookies(u,l,x){r.validate(r.isUrlStringOrObject(u),x,u);const k=ne(u);r.isFunction(l)&&(x=l,l={}),r.validate(r.isObject(l),x,l),r.validate(r.isFunction(x),x);const L=re(k.hostname),D=k.pathname||"/";let $=l.secure;$==null&&k.protocol&&(k.protocol=="https:"||k.protocol=="wss:")&&($=!0);let J=0;if(l.sameSiteContext){const N=g(l.sameSiteContext);if(J=H.sameSiteLevel[N],!J)return x(new Error(R))}let X=l.http;X==null&&(X=!0);const O=l.now||Date.now(),na=l.expire!==!1,ce=!!l.allPaths,we=this.store;function Ca(N){if(N.hostOnly){if(N.domain!=L)return!1}else if(!f(L,N.domain,!1))return!1;return!ce&&!n(D,N.path)||N.secure&&!$||N.httpOnly&&!X||J&&H.sameSiteLevel[N.sameSite||"none"]>J?!1:na&&N.expiryTime()<=O?(we.removeCookie(N.domain,N.path,N.key,()=>{}),!1):!0}we.findCookies(L,ce?null:D,this.allowSpecialUseDomain,(N,K)=>{if(N)return x(N);K=K.filter(Ca),l.sort!==!1&&(K=K.sort(se));const qe=new Date;for(const ra of K)ra.lastAccessed=qe;x(null,K)})}getCookieString(...u){const l=u.pop();r.validate(r.isFunction(l),l);const x=function(k,L){k?l(k):l(null,L.sort(se).map(D=>D.cookieString()).join("; "))};u.push(x),this.getCookies.apply(this,u)}getSetCookieStrings(...u){const l=u.pop();r.validate(r.isFunction(l),l);const x=function(k,L){k?l(k):l(null,L.map(D=>D.toString()))};u.push(x),this.getCookies.apply(this,u)}serialize(u){r.validate(r.isFunction(u),u);let l=this.store.constructor.name;r.isObject(l)&&(l=null);const x={version:`tough-cookie@${c}`,storeType:l,rejectPublicSuffixes:!!this.rejectPublicSuffixes,enableLooseMode:!!this.enableLooseMode,allowSpecialUseDomain:!!this.allowSpecialUseDomain,prefixSecurity:je(this.prefixSecurity),cookies:[]};if(!(this.store.getAllCookies&&typeof this.store.getAllCookies=="function"))return u(new Error("store does not support getAllCookies and cannot be serialized"));this.store.getAllCookies((k,L)=>k?u(k):(x.cookies=L.map(D=>(D=D instanceof H?D.toJSON():D,delete D.creationIndex,D)),u(null,x)))}toJSON(){return this.serializeSync()}_importCookies(u,l){let x=u.cookies;if(!x||!Array.isArray(x))return l(new Error("serialized jar has no cookies array"));x=x.slice();const k=L=>{if(L)return l(L);if(!x.length)return l(L,this);let D;try{D=Z(x.shift())}catch($){return l($)}if(D===null)return k(null);this.store.putCookie(D,k)};k()}clone(u,l){arguments.length===1&&(l=u,u=null),this.serialize((x,k)=>{if(x)return l(x);ma.deserialize(k,u,l)})}cloneSync(u){if(arguments.length===0)return this._cloneSync();if(!u.synchronous)throw new Error("CookieJar clone destination store is not synchronous; use async API instead.");return this._cloneSync(u)}removeAllCookies(u){r.validate(r.isFunction(u),u);const l=this.store;if(typeof l.removeAllCookies=="function"&&l.removeAllCookies!==i.prototype.removeAllCookies)return l.removeAllCookies(u);l.getAllCookies((x,k)=>{if(x)return u(x);if(k.length===0)return u(null);let L=0;const D=[];function $(J){if(J&&D.push(J),L++,L===k.length)return u(D.length?D[0]:null)}k.forEach(J=>{l.removeCookie(J.domain,J.path,J.key,$)})})}static deserialize(u,l,x){arguments.length!==3&&(x=l,l=null),r.validate(r.isFunction(x),x);let k;if(typeof u=="string"){if(k=Q(u),k instanceof Error)return x(k)}else k=u;const L=new ma(l,{rejectPublicSuffixes:k.rejectPublicSuffixes,looseMode:k.enableLooseMode,allowSpecialUseDomain:k.allowSpecialUseDomain,prefixSecurity:k.prefixSecurity});L._importCookies(k,D=>{if(D)return x(D);x(null,L)})}static deserializeSync(u,l){const x=typeof u=="string"?JSON.parse(u):u,k=new ma(l,{rejectPublicSuffixes:x.rejectPublicSuffixes,looseMode:x.enableLooseMode});if(!k.store.synchronous)throw new Error("CookieJar store is not synchronous; use async API instead.");return k._importCookiesSync(x),k}};pe.fromJSON=pe.deserializeSync,["_importCookies","clone","getCookies","getCookieString","getSetCookieStrings","removeAllCookies","serialize","setCookie"].forEach(d=>{pe.prototype[d]=h(pe.prototype[d])}),pe.deserialize=h(pe.deserialize);function ve(d){return function(...u){if(!this.store.synchronous)throw new Error("CookieJar store is not synchronous; use async API instead.");let l,x;if(this[d](...u,(k,L)=>{l=k,x=L}),l)throw l;return x}}e.version=c,e.CookieJar=pe,e.Cookie=H,e.Store=i,e.MemoryCookieStore=s,e.parseDate=W,e.formatDate=me,e.parse=G,e.fromJSON=Z,e.domainMatch=f,e.defaultPath=P,e.pathMatch=n,e.getPublicSuffix=t.getPublicSuffix,e.cookieCompare=se,e.permuteDomain=Do().permuteDomain,e.permutePath=ie,e.canonicalDomain=re,e.PrefixSecurityEnum=j,e.ParameterError=r.ParameterError}}),ii=Jt(ti()),si=ii.default;/*! Bundled license information:

tough-cookie/lib/pubsuffix-psl.js:
  (*!
   * Copyright (c) 2018, Salesforce.com, Inc.
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice,
   * this list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. Neither the name of Salesforce.com nor the names of its contributors may
   * be used to endorse or promote products derived from this software without
   * specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
   * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   * POSSIBILITY OF SUCH DAMAGE.
   *)

tough-cookie/lib/store.js:
  (*!
   * Copyright (c) 2015, Salesforce.com, Inc.
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice,
   * this list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. Neither the name of Salesforce.com nor the names of its contributors may
   * be used to endorse or promote products derived from this software without
   * specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
   * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   * POSSIBILITY OF SUCH DAMAGE.
   *)

tough-cookie/lib/permuteDomain.js:
  (*!
   * Copyright (c) 2015, Salesforce.com, Inc.
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice,
   * this list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. Neither the name of Salesforce.com nor the names of its contributors may
   * be used to endorse or promote products derived from this software without
   * specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
   * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   * POSSIBILITY OF SUCH DAMAGE.
   *)

tough-cookie/lib/pathMatch.js:
  (*!
   * Copyright (c) 2015, Salesforce.com, Inc.
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice,
   * this list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. Neither the name of Salesforce.com nor the names of its contributors may
   * be used to endorse or promote products derived from this software without
   * specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
   * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   * POSSIBILITY OF SUCH DAMAGE.
   *)

tough-cookie/lib/memstore.js:
  (*!
   * Copyright (c) 2015, Salesforce.com, Inc.
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice,
   * this list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. Neither the name of Salesforce.com nor the names of its contributors may
   * be used to endorse or promote products derived from this software without
   * specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
   * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   * POSSIBILITY OF SUCH DAMAGE.
   *)

tough-cookie/lib/cookie.js:
  (*!
   * Copyright (c) 2015-2020, Salesforce.com, Inc.
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice,
   * this list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. Neither the name of Salesforce.com nor the names of its contributors may
   * be used to endorse or promote products derived from this software without
   * specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
   * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   * POSSIBILITY OF SUCH DAMAGE.
   *)
*/const{Cookie:ni,CookieJar:ri,Store:ui,MemoryCookieStore:ci,domainMatch:li,pathMatch:mi}=si;class pi extends ui{constructor(){super();M(this,"storage");M(this,"storageKey");le(typeof localStorage<"u","Failed to create a WebStorageCookieStore: `localStorage` is not available in this environment. This is likely an issue with MSW. Please report it on GitHub: https://github.com/mswjs/msw/issues"),this.synchronous=!0,this.storage=localStorage,this.storageKey="__msw-cookie-store__"}findCookie(o,t,i,s){try{const n=this.getStore(),r=this.filterCookiesFromList(n,{domain:o,path:t,key:i});s(null,r[0]||null)}catch(n){n instanceof Error&&s(n,null)}}findCookies(o,t,i,s){if(!o){s(null,[]);return}try{const n=this.getStore(),r=this.filterCookiesFromList(n,{domain:o,path:t});s(null,r)}catch(n){n instanceof Error&&s(n,[])}}putCookie(o,t){try{if(o.maxAge===0)return;const i=this.getStore();i.push(o),this.updateStore(i)}catch(i){i instanceof Error&&t(i)}}updateCookie(o,t,i){if(t.maxAge===0){this.removeCookie(t.domain||"",t.path||"",t.key,i);return}this.putCookie(t,i)}removeCookie(o,t,i,s){try{const n=this.getStore(),r=this.deleteCookiesFromList(n,{domain:o,path:t,key:i});this.updateStore(r),s(null)}catch(n){n instanceof Error&&s(n)}}removeCookies(o,t,i){try{const s=this.getStore(),n=this.deleteCookiesFromList(s,{domain:o,path:t});this.updateStore(n),i(null)}catch(s){s instanceof Error&&i(s)}}getAllCookies(o){try{o(null,this.getStore())}catch(t){t instanceof Error&&o(t,[])}}getStore(){try{const o=this.storage.getItem(this.storageKey);if(o==null)return[];const t=JSON.parse(o),i=[];for(const s of t){const n=ni.fromJSON(s);n!=null&&i.push(n)}return i}catch{return[]}}updateStore(o){this.storage.setItem(this.storageKey,JSON.stringify(o.map(t=>t.toJSON())))}filterCookiesFromList(o,t){const i=[];for(const s of o)t.domain&&!li(t.domain,s.domain||"")||t.path&&!mi(t.path,s.path||"")||t.key&&s.key!==t.key||i.push(s);return i}deleteCookiesFromList(o,t){const i=this.filterCookiesFromList(o,t);return o.filter(s=>!i.includes(s))}}const hi=Ao()?new ci:new pi,Fo=new ri(hi);var di=Object.create,Mo=Object.defineProperty,gi=Object.getOwnPropertyDescriptor,Bo=Object.getOwnPropertyNames,fi=Object.getPrototypeOf,ki=Object.prototype.hasOwnProperty,$o=(e,a)=>function(){return a||(0,e[Bo(e)[0]])((a={exports:{}}).exports,a),a.exports},bi=(e,a,o,t)=>{if(a&&typeof a=="object"||typeof a=="function")for(let i of Bo(a))!ki.call(e,i)&&i!==o&&Mo(e,i,{get:()=>a[i],enumerable:!(t=gi(a,i))||t.enumerable});return e},yi=(e,a,o)=>(o=e!=null?di(fi(e)):{},bi(Mo(o,"default",{value:e,enumerable:!0}),e)),ji=$o({"node_modules/statuses/codes.json"(e,a){a.exports={100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a Teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}}}),vi=$o({"node_modules/statuses/index.js"(e,a){var o=ji();a.exports=r,r.message=o,r.code=t(o),r.codes=i(o),r.redirect={300:!0,301:!0,302:!0,303:!0,305:!0,307:!0,308:!0},r.empty={204:!0,205:!0,304:!0},r.retry={502:!0,503:!0,504:!0};function t(c){var h={};return Object.keys(c).forEach(function(m){var v=c[m],b=Number(m);h[v.toLowerCase()]=b}),h}function i(c){return Object.keys(c).map(function(p){return Number(p)})}function s(c){var h=c.toLowerCase();if(!Object.prototype.hasOwnProperty.call(r.code,h))throw new Error('invalid status message: "'+c+'"');return r.code[h]}function n(c){if(!Object.prototype.hasOwnProperty.call(r.message,c))throw new Error("invalid status code: "+c);return r.message[c]}function r(c){if(typeof c=="number")return n(c);if(typeof c!="string")throw new TypeError("code must be a number or string");var h=parseInt(c,10);return isNaN(h)?s(c):n(h)}}}),wi=yi(vi()),Uo=wi.default;/*! Bundled license information:

statuses/index.js:
  (*!
   * statuses
   * Copyright(c) 2014 Jonathan Ong
   * Copyright(c) 2016 Douglas Christopher Wilson
   * MIT Licensed
   *)
*/var xi=Object.create,No=Object.defineProperty,Ei=Object.getOwnPropertyDescriptor,Ho=Object.getOwnPropertyNames,Ci=Object.getPrototypeOf,zi=Object.prototype.hasOwnProperty,Si=(e,a)=>function(){return a||(0,e[Ho(e)[0]])((a={exports:{}}).exports,a),a.exports},Ai=(e,a,o,t)=>{if(a&&typeof a=="object"||typeof a=="function")for(let i of Ho(a))!zi.call(e,i)&&i!==o&&No(e,i,{get:()=>a[i],enumerable:!(t=Ei(a,i))||t.enumerable});return e},Li=(e,a,o)=>(o=e!=null?xi(Ci(e)):{},Ai(!e||!e.__esModule?No(o,"default",{value:e,enumerable:!0}):o,e)),Ri=Si({"node_modules/set-cookie-parser/lib/set-cookie.js"(e,a){var o={decodeValues:!0,map:!1,silent:!1};function t(c){return typeof c=="string"&&!!c.trim()}function i(c,h){var p=c.split(";").filter(t),m=p.shift(),v=s(m),b=v.name,y=v.value;h=h?Object.assign({},o,h):o;try{y=h.decodeValues?decodeURIComponent(y):y}catch(C){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+y+"'. Set options.decodeValues to false to disable this feature.",C)}var z={name:b,value:y};return p.forEach(function(C){var A=C.split("="),q=A.shift().trimLeft().toLowerCase(),R=A.join("=");q==="expires"?z.expires=new Date(R):q==="max-age"?z.maxAge=parseInt(R,10):q==="secure"?z.secure=!0:q==="httponly"?z.httpOnly=!0:q==="samesite"?z.sameSite=R:z[q]=R}),z}function s(c){var h="",p="",m=c.split("=");return m.length>1?(h=m.shift(),p=m.join("=")):p=c,{name:h,value:p}}function n(c,h){if(h=h?Object.assign({},o,h):o,!c)return h.map?{}:[];if(c.headers)if(typeof c.headers.getSetCookie=="function")c=c.headers.getSetCookie();else if(c.headers["set-cookie"])c=c.headers["set-cookie"];else{var p=c.headers[Object.keys(c.headers).find(function(v){return v.toLowerCase()==="set-cookie"})];!p&&c.headers.cookie&&!h.silent&&console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),c=p}if(Array.isArray(c)||(c=[c]),h=h?Object.assign({},o,h):o,h.map){var m={};return c.filter(t).reduce(function(v,b){var y=i(b,h);return v[y.name]=y,v},m)}else return c.filter(t).map(function(v){return i(v,h)})}function r(c){if(Array.isArray(c))return c;if(typeof c!="string")return[];var h=[],p=0,m,v,b,y,z;function C(){for(;p<c.length&&/\s/.test(c.charAt(p));)p+=1;return p<c.length}function A(){return v=c.charAt(p),v!=="="&&v!==";"&&v!==","}for(;p<c.length;){for(m=p,z=!1;C();)if(v=c.charAt(p),v===","){for(b=p,p+=1,C(),y=p;p<c.length&&A();)p+=1;p<c.length&&c.charAt(p)==="="?(z=!0,p=y,h.push(c.substring(m,b)),m=p):p=b+1}else p+=1;(!z||p>=c.length)&&h.push(c.substring(m,c.length))}return h}a.exports=n,a.exports.parse=n,a.exports.parseString=i,a.exports.splitCookiesString=r}}),_i=Li(Ri()),Ii=/[^a-z0-9\-#$%&'*+.^_`|~]/i;function Je(e){if(Ii.test(e)||e.trim()==="")throw new TypeError("Invalid character in header field name");return e.trim().toLowerCase()}var eo=[`
`,"\r","	"," "],Ti=new RegExp(`(^[${eo.join("")}]|$[${eo.join("")}])`,"g");function Aa(e){return e.replace(Ti,"")}function Xe(e){if(typeof e!="string"||e.length===0)return!1;for(let a=0;a<e.length;a++){const o=e.charCodeAt(a);if(o>127||!Pi(o))return!1}return!0}function Pi(e){return![127,32,"(",")","<",">","@",",",";",":","\\",'"',"/","[","]","?","=","{","}"].includes(e)}function ao(e){if(typeof e!="string"||e.trim()!==e)return!1;for(let a=0;a<e.length;a++){const o=e.charCodeAt(a);if(o===0||o===10||o===13)return!1}return!0}var Fe=Symbol("normalizedHeaders"),La=Symbol("rawHeaderNames"),oo=", ",to,io,so,Di=class Wo{constructor(a){this[to]={},this[io]=new Map,this[so]="Headers",["Headers","HeadersPolyfill"].includes(a==null?void 0:a.constructor.name)||a instanceof Wo||typeof globalThis.Headers<"u"&&a instanceof globalThis.Headers?a.forEach((t,i)=>{this.append(i,t)},this):Array.isArray(a)?a.forEach(([o,t])=>{this.append(o,Array.isArray(t)?t.join(oo):t)}):a&&Object.getOwnPropertyNames(a).forEach(o=>{const t=a[o];this.append(o,Array.isArray(t)?t.join(oo):t)})}[(to=Fe,io=La,so=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}*keys(){for(const[a]of this.entries())yield a}*values(){for(const[,a]of this.entries())yield a}*entries(){let a=Object.keys(this[Fe]).sort((o,t)=>o.localeCompare(t));for(const o of a)if(o==="set-cookie")for(const t of this.getSetCookie())yield[o,t];else yield[o,this.get(o)]}has(a){if(!Xe(a))throw new TypeError(`Invalid header name "${a}"`);return this[Fe].hasOwnProperty(Je(a))}get(a){if(!Xe(a))throw TypeError(`Invalid header name "${a}"`);return this[Fe][Je(a)]??null}set(a,o){if(!Xe(a)||!ao(o))return;const t=Je(a),i=Aa(o);this[Fe][t]=Aa(i),this[La].set(t,a)}append(a,o){if(!Xe(a)||!ao(o))return;const t=Je(a),i=Aa(o);let s=this.has(t)?`${this.get(t)}, ${i}`:i;this.set(a,s)}delete(a){if(!Xe(a)||!this.has(a))return;const o=Je(a);delete this[Fe][o],this[La].delete(o)}forEach(a,o){for(const[t,i]of this.entries())a.call(o,i,t,this)}getSetCookie(){const a=this.get("set-cookie");return a===null?[]:a===""?[""]:(0,_i.splitCookiesString)(a)}};const{message:Oi}=Uo,Go=Symbol("kSetCookie");function Te(e={}){const a=(e==null?void 0:e.status)||200,o=(e==null?void 0:e.statusText)||Oi[a]||"",t=new Headers(e==null?void 0:e.headers);return{...e,headers:t,status:a,statusText:o}}function qi(e,a){a.type&&Object.defineProperty(e,"type",{value:a.type,enumerable:!0,writable:!1});const o=a.headers.get("set-cookie");if(o&&(Object.defineProperty(e,Go,{value:o,enumerable:!1,writable:!1}),typeof document<"u")){const t=Di.prototype.getSetCookie.call(a.headers);for(const i of t)document.cookie=i}return e}function Fi(e,a){const o=Reflect.get(a,Go);o&&Fo.setCookie(o,e.url)}async function Vo(e,a,o,t,i,s){var h,p,m,v,b,y;if(i.emit("request:start",{request:e,requestId:a}),(h=e.headers.get("accept"))!=null&&h.includes("msw/passthrough")){i.emit("request:end",{request:e,requestId:a}),(p=s==null?void 0:s.onPassthroughResponse)==null||p.call(s,e);return}const n=await Mt(()=>Bt({request:e,requestId:a,handlers:o,resolutionContext:s==null?void 0:s.resolutionContext}));if(n.error)throw i.emit("unhandledException",{error:n.error,request:e,requestId:a}),n.error;if(!n.data){await So(e,t.onUnhandledRequest),i.emit("request:unhandled",{request:e,requestId:a}),i.emit("request:end",{request:e,requestId:a}),(m=s==null?void 0:s.onPassthroughResponse)==null||m.call(s,e);return}const{response:r}=n.data;if(!r){i.emit("request:end",{request:e,requestId:a}),(v=s==null?void 0:s.onPassthroughResponse)==null||v.call(s,e);return}if(r.status===302&&r.headers.get("x-msw-intention")==="passthrough"){i.emit("request:end",{request:e,requestId:a}),(b=s==null?void 0:s.onPassthroughResponse)==null||b.call(s,e);return}Fi(e,r),i.emit("request:match",{request:e,requestId:a});const c=n.data;return(y=s==null?void 0:s.onMockedResponse)==null||y.call(s,r,c),i.emit("request:end",{request:e,requestId:a}),r}function Mi(e){return{status:e.status,statusText:e.statusText,headers:Object.fromEntries(e.headers.entries())}}function Wa(e){return a=>a!=null&&typeof a=="object"&&"__kind"in a&&a.__kind===e}function no(e){return e!=null&&typeof e=="object"&&!Array.isArray(e)}function Jo(e,a){return Object.entries(a).reduce((o,[t,i])=>{const s=o[t];return Array.isArray(s)&&Array.isArray(i)?(o[t]=s.concat(i),o):no(s)&&no(i)?(o[t]=Jo(s,i),o):(o[t]=i,o)},Object.assign({},e))}var Bi=class extends Error{constructor(a,o,t){super(`Possible EventEmitter memory leak detected. ${t} ${o.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`),this.emitter=a,this.type=o,this.count=t,this.name="MaxListenersExceededWarning"}},Xo=class{static listenerCount(a,o){return a.listenerCount(o)}constructor(){this.events=new Map,this.maxListeners=Xo.defaultMaxListeners,this.hasWarnedAboutPotentialMemoryLeak=!1}_emitInternalEvent(a,o,t){this.emit(a,o,t)}_getListeners(a){return Array.prototype.concat.apply([],this.events.get(a))||[]}_removeListener(a,o){const t=a.indexOf(o);return t>-1&&a.splice(t,1),[]}_wrapOnceListener(a,o){const t=(...i)=>(this.removeListener(a,t),o.apply(this,i));return Object.defineProperty(t,"name",{value:o.name}),t}setMaxListeners(a){return this.maxListeners=a,this}getMaxListeners(){return this.maxListeners}eventNames(){return Array.from(this.events.keys())}emit(a,...o){const t=this._getListeners(a);return t.forEach(i=>{i.apply(this,o)}),t.length>0}addListener(a,o){this._emitInternalEvent("newListener",a,o);const t=this._getListeners(a).concat(o);if(this.events.set(a,t),this.maxListeners>0&&this.listenerCount(a)>this.maxListeners&&!this.hasWarnedAboutPotentialMemoryLeak){this.hasWarnedAboutPotentialMemoryLeak=!0;const i=new Bi(this,a,this.listenerCount(a));console.warn(i)}return this}on(a,o){return this.addListener(a,o)}once(a,o){return this.addListener(a,this._wrapOnceListener(a,o))}prependListener(a,o){const t=this._getListeners(a);if(t.length>0){const i=[o].concat(t);this.events.set(a,i)}else this.events.set(a,t.concat(o));return this}prependOnceListener(a,o){return this.prependListener(a,this._wrapOnceListener(a,o))}removeListener(a,o){const t=this._getListeners(a);return t.length>0&&(this._removeListener(t,o),this.events.set(a,t),this._emitInternalEvent("removeListener",a,o)),this}off(a,o){return this.removeListener(a,o)}removeAllListeners(a){return a?this.events.delete(a):this.events.clear(),this}listeners(a){return Array.from(this._getListeners(a))}listenerCount(a){return this._getListeners(a).length}rawListeners(a){return this.listeners(a)}},fa=Xo;fa.defaultMaxListeners=10;function $i(e,a){const o=e.emit;if(o._isPiped)return;const t=function(s,...n){return a.emit(s,...n),o.call(this,s,...n)};t._isPiped=!0,e.emit=t}function Ui(e){const a=[...e];return Object.freeze(a),a}class Ni{constructor(){M(this,"subscriptions",[])}dispose(){let a;for(;a=this.subscriptions.shift();)a()}}class Hi{constructor(a){M(this,"handlers");this.initialHandlers=a,this.handlers=[...a]}prepend(a){this.handlers.unshift(...a)}reset(a){this.handlers=a.length>0?[...a]:[...this.initialHandlers]}currentHandlers(){return this.handlers}}class Wi extends Ni{constructor(...o){super();M(this,"handlersController");M(this,"emitter");M(this,"publicEmitter");M(this,"events");le(this.validateHandlers(o),U.formatMessage("Failed to apply given request handlers: invalid input. Did you forget to spread the request handlers Array?")),this.handlersController=new Hi(o),this.emitter=new fa,this.publicEmitter=new fa,$i(this.emitter,this.publicEmitter),this.events=this.createLifeCycleEvents(),this.subscriptions.push(()=>{this.emitter.removeAllListeners(),this.publicEmitter.removeAllListeners()})}validateHandlers(o){return o.every(t=>!Array.isArray(t))}use(...o){le(this.validateHandlers(o),U.formatMessage('Failed to call "use()" with the given request handlers: invalid input. Did you forget to spread the array of request handlers?')),this.handlersController.prepend(o)}restoreHandlers(){this.handlersController.currentHandlers().forEach(o=>{"isUsed"in o&&(o.isUsed=!1)})}resetHandlers(...o){this.handlersController.reset(o)}listHandlers(){return Ui(this.handlersController.currentHandlers())}createLifeCycleEvents(){return{on:(...o)=>this.publicEmitter.on(...o),removeListener:(...o)=>this.publicEmitter.removeListener(...o),removeAllListeners:(...o)=>this.publicEmitter.removeAllListeners(...o)}}}function Gi(e){const a=Object.getOwnPropertyDescriptor(globalThis,e);return typeof a>"u"||typeof a.get=="function"&&typeof a.get()>"u"||typeof a.get>"u"&&a.value==null?!1:typeof a.set>"u"&&!a.configurable?(console.error(`[MSW] Failed to apply interceptor: the global \`${e}\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`),!1):!0}var Vi={},Ji=Object.defineProperty,Xi=(e,a)=>{for(var o in a)Ji(e,o,{get:a[o],enumerable:!0})},qa={};Xi(qa,{blue:()=>Yi,gray:()=>Fa,green:()=>Zi,red:()=>Qi,yellow:()=>Ki});function Ki(e){return`\x1B[33m${e}\x1B[0m`}function Yi(e){return`\x1B[34m${e}\x1B[0m`}function Fa(e){return`\x1B[90m${e}\x1B[0m`}function Qi(e){return`\x1B[31m${e}\x1B[0m`}function Zi(e){return`\x1B[32m${e}\x1B[0m`}var ja=Ao(),Ko=class{constructor(a){M(this,"prefix");this.name=a,this.prefix=`[${this.name}]`;const o=ro("DEBUG"),t=ro("LOG_LEVEL");o==="1"||o==="true"||typeof o<"u"&&this.name.startsWith(o)?(this.debug=Ke(t,"debug")?he:this.debug,this.info=Ke(t,"info")?he:this.info,this.success=Ke(t,"success")?he:this.success,this.warning=Ke(t,"warning")?he:this.warning,this.error=Ke(t,"error")?he:this.error):(this.info=he,this.success=he,this.warning=he,this.error=he,this.only=he)}extend(a){return new Ko(`${this.name}:${a}`)}debug(a,...o){this.logEntry({level:"debug",message:Fa(a),positionals:o,prefix:this.prefix,colors:{prefix:"gray"}})}info(a,...o){this.logEntry({level:"info",message:a,positionals:o,prefix:this.prefix,colors:{prefix:"blue"}});const t=new es;return(i,...s)=>{t.measure(),this.logEntry({level:"info",message:`${i} ${Fa(`${t.deltaTime}ms`)}`,positionals:s,prefix:this.prefix,colors:{prefix:"blue"}})}}success(a,...o){this.logEntry({level:"info",message:a,positionals:o,prefix:`✔ ${this.prefix}`,colors:{timestamp:"green",prefix:"green"}})}warning(a,...o){this.logEntry({level:"warning",message:a,positionals:o,prefix:`⚠ ${this.prefix}`,colors:{timestamp:"yellow",prefix:"yellow"}})}error(a,...o){this.logEntry({level:"error",message:a,positionals:o,prefix:`✖ ${this.prefix}`,colors:{timestamp:"red",prefix:"red"}})}only(a){a()}createEntry(a,o){return{timestamp:new Date,level:a,message:o}}logEntry(a){const{level:o,message:t,prefix:i,colors:s,positionals:n=[]}=a,r=this.createEntry(o,t),c=(s==null?void 0:s.timestamp)||"gray",h=(s==null?void 0:s.prefix)||"gray",p={timestamp:qa[c],prefix:qa[h]};this.getWriter(o)([p.timestamp(this.formatTimestamp(r.timestamp))].concat(i!=null?p.prefix(i):[]).concat(uo(t)).join(" "),...n.map(uo))}formatTimestamp(a){return`${a.toLocaleTimeString("en-GB")}:${a.getMilliseconds()}`}getWriter(a){switch(a){case"debug":case"success":case"info":return as;case"warning":return os;case"error":return ts}}},es=class{constructor(){M(this,"startTime");M(this,"endTime");M(this,"deltaTime");this.startTime=performance.now()}measure(){this.endTime=performance.now();const a=this.endTime-this.startTime;this.deltaTime=a.toFixed(2)}},he=()=>{};function as(e,...a){if(ja){process.stdout.write(Ge(e,...a)+`
`);return}console.log(e,...a)}function os(e,...a){if(ja){process.stderr.write(Ge(e,...a)+`
`);return}console.warn(e,...a)}function ts(e,...a){if(ja){process.stderr.write(Ge(e,...a)+`
`);return}console.error(e,...a)}function ro(e){var a;return ja?Vi[e]:(a=globalThis[e])==null?void 0:a.toString()}function Ke(e,a){return e!==void 0&&e!==a}function uo(e){return typeof e>"u"?"undefined":e===null?"null":typeof e=="string"?e:typeof e=="object"?JSON.stringify(e):e.toString()}function co(e){return globalThis[e]||void 0}function is(e,a){globalThis[e]=a}function ss(e){delete globalThis[e]}var ns=class{constructor(a){this.symbol=a,this.readyState="INACTIVE",this.emitter=new fa,this.subscriptions=[],this.logger=new Ko(a.description),this.emitter.setMaxListeners(0),this.logger.info("constructing the interceptor...")}checkEnvironment(){return!0}apply(){const a=this.logger.extend("apply");if(a.info("applying the interceptor..."),this.readyState==="APPLIED"){a.info("intercepted already applied!");return}if(!this.checkEnvironment()){a.info("the interceptor cannot be applied in this environment!");return}this.readyState="APPLYING";const t=this.getInstance();if(t){a.info("found a running instance, reusing..."),this.on=(i,s)=>(a.info('proxying the "%s" listener',i),t.emitter.addListener(i,s),this.subscriptions.push(()=>{t.emitter.removeListener(i,s),a.info('removed proxied "%s" listener!',i)}),this),this.readyState="APPLIED";return}a.info("no running instance found, setting up a new instance..."),this.setup(),this.setInstance(),this.readyState="APPLIED"}setup(){}on(a,o){const t=this.logger.extend("on");return this.readyState==="DISPOSING"||this.readyState==="DISPOSED"?(t.info("cannot listen to events, already disposed!"),this):(t.info('adding "%s" event listener:',a,o),this.emitter.on(a,o),this)}once(a,o){return this.emitter.once(a,o),this}off(a,o){return this.emitter.off(a,o),this}removeAllListeners(a){return this.emitter.removeAllListeners(a),this}dispose(){const a=this.logger.extend("dispose");if(this.readyState==="DISPOSED"){a.info("cannot dispose, already disposed!");return}if(a.info("disposing the interceptor..."),this.readyState="DISPOSING",!this.getInstance()){a.info("no interceptors running, skipping dispose...");return}if(this.clearInstance(),a.info("global symbol deleted:",co(this.symbol)),this.subscriptions.length>0){a.info("disposing of %d subscriptions...",this.subscriptions.length);for(const o of this.subscriptions)o();this.subscriptions=[],a.info("disposed of all subscriptions!",this.subscriptions.length)}this.emitter.removeAllListeners(),a.info("destroyed the listener!"),this.readyState="DISPOSED"}getInstance(){var a;const o=co(this.symbol);return this.logger.info("retrieved global instance:",(a=o==null?void 0:o.constructor)==null?void 0:a.name),o}setInstance(){is(this.symbol,this),this.logger.info("set global instance!",this.symbol.description)}clearInstance(){ss(this.symbol),this.logger.info("cleared global instance!",this.symbol.description)}};function rs(){return Math.random().toString(16).slice(2)}function us(){const e=(a,o)=>{e.state="pending",e.resolve=t=>{if(e.state!=="pending")return;e.result=t;const i=s=>(e.state="fulfilled",s);return a(t instanceof Promise?t:Promise.resolve(t).then(i))},e.reject=t=>{if(e.state==="pending")return queueMicrotask(()=>{e.state="rejected"}),o(e.rejectionReason=t)}};return e}var Ae,He,pa,xo,cs=(xo=class extends Promise{constructor(o=null){const t=us();super((i,s)=>{t(i,s),o==null||o(t.resolve,t.reject)});Ve(this,He);Ve(this,Ae);M(this,"resolve");M(this,"reject");Sa(this,Ae,t),this.resolve=be(this,Ae).resolve,this.reject=be(this,Ae).reject}get state(){return be(this,Ae).state}get rejectionReason(){return be(this,Ae).rejectionReason}then(o,t){return Ie(this,He,pa).call(this,super.then(o,t))}catch(o){return Ie(this,He,pa).call(this,super.catch(o))}finally(o){return Ie(this,He,pa).call(this,super.finally(o))}},Ae=new WeakMap,He=new WeakSet,pa=function(o){return Object.defineProperties(o,{resolve:{configurable:!0,value:this.resolve},reject:{configurable:!0,value:this.reject}})},xo);function ee(e,a){return Object.defineProperties(a,{target:{value:e,enumerable:!0,writable:!0},currentTarget:{value:e,enumerable:!0,writable:!0}}),a}var Ue=Symbol("kCancelable"),ke=Symbol("kDefaultPrevented"),Ga=class extends MessageEvent{constructor(e,a){super(e,a),this[Ue]=!!a.cancelable,this[ke]=!1}get cancelable(){return this[Ue]}set cancelable(e){this[Ue]=e}get defaultPrevented(){return this[ke]}set defaultPrevented(e){this[ke]=e}preventDefault(){this.cancelable&&!this[ke]&&(this[ke]=!0)}},va=class extends Event{constructor(e,a={}){super(e,a),this.code=a.code===void 0?0:a.code,this.reason=a.reason===void 0?"":a.reason,this.wasClean=a.wasClean===void 0?!1:a.wasClean}},lo=class extends va{constructor(e,a={}){super(e,a),this[Ue]=!!a.cancelable,this[ke]=!1}get cancelable(){return this[Ue]}set cancelable(e){this[Ue]=e}get defaultPrevented(){return this[ke]}set defaultPrevented(e){this[ke]=e}preventDefault(){this.cancelable&&!this[ke]&&(this[ke]=!0)}},Ye=Symbol("kEmitter"),ua=Symbol("kBoundListener"),ls=class{constructor(e,a){this.socket=e,this.transport=a,this.id=rs(),this.url=new URL(e.url),this[Ye]=new EventTarget,this.transport.addEventListener("outgoing",o=>{const t=ee(this.socket,new Ga("message",{data:o.data,origin:o.origin,cancelable:!0}));this[Ye].dispatchEvent(t),t.defaultPrevented&&o.preventDefault()}),this.transport.addEventListener("close",o=>{this[Ye].dispatchEvent(ee(this.socket,new va("close",o)))})}addEventListener(e,a,o){if(!Reflect.has(a,ua)){const t=a.bind(this.socket);Object.defineProperty(a,ua,{value:t,enumerable:!1,configurable:!1})}this[Ye].addEventListener(e,Reflect.get(a,ua),o)}removeEventListener(e,a,o){this[Ye].removeEventListener(e,Reflect.get(a,ua),o)}send(e){this.transport.send(e)}close(e,a){this.transport.close(e,a)}},mo="InvalidAccessError: close code out of user configurable range",ka=Symbol("kPassthroughPromise"),Yo=Symbol("kOnSend"),oa=Symbol("kClose"),ia=class extends EventTarget{constructor(e,a){super(),this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this._onopen=null,this._onmessage=null,this._onerror=null,this._onclose=null,this.url=e.toString(),this.protocol="",this.extensions="",this.binaryType="blob",this.readyState=this.CONNECTING,this.bufferedAmount=0,this[ka]=new cs,queueMicrotask(async()=>{await this[ka]||(this.protocol=typeof a=="string"?a:Array.isArray(a)&&a.length>0?a[0]:"",this.readyState===this.CONNECTING&&(this.readyState=this.OPEN,this.dispatchEvent(ee(this,new Event("open")))))})}set onopen(e){this.removeEventListener("open",this._onopen),this._onopen=e,e!==null&&this.addEventListener("open",e)}get onopen(){return this._onopen}set onmessage(e){this.removeEventListener("message",this._onmessage),this._onmessage=e,e!==null&&this.addEventListener("message",e)}get onmessage(){return this._onmessage}set onerror(e){this.removeEventListener("error",this._onerror),this._onerror=e,e!==null&&this.addEventListener("error",e)}get onerror(){return this._onerror}set onclose(e){this.removeEventListener("close",this._onclose),this._onclose=e,e!==null&&this.addEventListener("close",e)}get onclose(){return this._onclose}send(e){if(this.readyState===this.CONNECTING)throw this.close(),new DOMException("InvalidStateError");this.readyState===this.CLOSING||this.readyState===this.CLOSED||(this.bufferedAmount+=ms(e),queueMicrotask(()=>{var a;this.bufferedAmount=0,(a=this[Yo])==null||a.call(this,e)}))}close(e=1e3,a){le(e,mo),le(e===1e3||e>=3e3&&e<=4999,mo),this[oa](e,a)}[oa](e=1e3,a,o=!0){this.readyState===this.CLOSING||this.readyState===this.CLOSED||(this.readyState=this.CLOSING,queueMicrotask(()=>{this.readyState=this.CLOSED,this.dispatchEvent(ee(this,new va("close",{code:e,reason:a,wasClean:o}))),this._onopen=null,this._onmessage=null,this._onerror=null,this._onclose=null}))}addEventListener(e,a,o){return super.addEventListener(e,a,o)}removeEventListener(e,a,o){return super.removeEventListener(e,a,o)}};ia.CONNECTING=0;ia.OPEN=1;ia.CLOSING=2;ia.CLOSED=3;function ms(e){return typeof e=="string"?e.length:e instanceof Blob?e.size:e.byteLength}var xe=Symbol("kEmitter"),ca=Symbol("kBoundListener"),Ra=Symbol("kSend"),ps=class{constructor(e,a,o){this.client=e,this.transport=a,this.createConnection=o,this[xe]=new EventTarget,this.mockCloseController=new AbortController,this.realCloseController=new AbortController,this.transport.addEventListener("outgoing",t=>{typeof this.realWebSocket>"u"||queueMicrotask(()=>{t.defaultPrevented||this[Ra](t.data)})}),this.transport.addEventListener("incoming",this.handleIncomingMessage.bind(this))}get socket(){return le(this.realWebSocket,'Cannot access "socket" on the original WebSocket server object: the connection is not open. Did you forget to call `server.connect()`?'),this.realWebSocket}connect(){le(!this.realWebSocket||this.realWebSocket.readyState!==WebSocket.OPEN,'Failed to call "connect()" on the original WebSocket instance: the connection already open');const e=this.createConnection();e.binaryType=this.client.binaryType,e.addEventListener("open",a=>{this[xe].dispatchEvent(ee(this.realWebSocket,new Event("open",a)))},{once:!0}),e.addEventListener("message",a=>{this.transport.dispatchEvent(ee(this.realWebSocket,new MessageEvent("incoming",{data:a.data,origin:a.origin})))}),this.client.addEventListener("close",a=>{this.handleMockClose(a)},{signal:this.mockCloseController.signal}),e.addEventListener("close",a=>{this.handleRealClose(a)},{signal:this.realCloseController.signal}),e.addEventListener("error",()=>{const a=ee(e,new Event("error",{cancelable:!0}));this[xe].dispatchEvent(a),a.defaultPrevented||this.client.dispatchEvent(ee(this.client,new Event("error")))}),this.realWebSocket=e}addEventListener(e,a,o){if(!Reflect.has(a,ca)){const t=a.bind(this.client);Object.defineProperty(a,ca,{value:t,enumerable:!1})}this[xe].addEventListener(e,Reflect.get(a,ca),o)}removeEventListener(e,a,o){this[xe].removeEventListener(e,Reflect.get(a,ca),o)}send(e){this[Ra](e)}[Ra](e){const{realWebSocket:a}=this;if(le(a,'Failed to call "server.send()" for "%s": the connection is not open. Did you forget to call "server.connect()"?',this.client.url),!(a.readyState===WebSocket.CLOSING||a.readyState===WebSocket.CLOSED)){if(a.readyState===WebSocket.CONNECTING){a.addEventListener("open",()=>{a.send(e)},{once:!0});return}a.send(e)}}close(){const{realWebSocket:e}=this;le(e,'Failed to close server connection for "%s": the connection is not open. Did you forget to call "server.connect()"?',this.client.url),this.realCloseController.abort(),!(e.readyState===WebSocket.CLOSING||e.readyState===WebSocket.CLOSED)&&(e.close(),queueMicrotask(()=>{this[xe].dispatchEvent(ee(this.realWebSocket,new lo("close",{code:1e3,cancelable:!0})))}))}handleIncomingMessage(e){const a=ee(e.target,new Ga("message",{data:e.data,origin:e.origin,cancelable:!0}));this[xe].dispatchEvent(a),a.defaultPrevented||this.client.dispatchEvent(ee(this.client,new MessageEvent("message",{data:e.data,origin:e.origin})))}handleMockClose(e){this.realWebSocket&&this.realWebSocket.close()}handleRealClose(e){this.mockCloseController.abort();const a=ee(this.realWebSocket,new lo("close",{code:e.code,reason:e.reason,wasClean:e.wasClean,cancelable:!0}));this[xe].dispatchEvent(a),a.defaultPrevented||this.client[oa](e.code,e.reason)}},hs=class extends EventTarget{constructor(e){super(),this.socket=e,this.socket.addEventListener("close",a=>{this.dispatchEvent(ee(this.socket,new va("close",a)))}),this.socket[Yo]=a=>{this.dispatchEvent(ee(this.socket,new Ga("outgoing",{data:a,origin:this.socket.url,cancelable:!0})))}}addEventListener(e,a,o){return super.addEventListener(e,a,o)}dispatchEvent(e){return super.dispatchEvent(e)}send(e){queueMicrotask(()=>{if(this.socket.readyState===this.socket.CLOSING||this.socket.readyState===this.socket.CLOSED)return;const a=()=>{this.socket.dispatchEvent(ee(this.socket,new MessageEvent("message",{data:e,origin:this.socket.url})))};this.socket.readyState===this.socket.CONNECTING?this.socket.addEventListener("open",()=>{a()},{once:!0}):a()})}close(e,a){this.socket[oa](e,a)}},Qo=class extends ns{constructor(){super(Qo.symbol)}checkEnvironment(){return Gi("WebSocket")}setup(){const e=Object.getOwnPropertyDescriptor(globalThis,"WebSocket"),a=new Proxy(globalThis.WebSocket,{construct:(o,t,i)=>{const[s,n]=t,r=()=>Reflect.construct(o,t,i),c=new ia(s,n),h=new hs(c);return queueMicrotask(()=>{try{const p=new ps(c,h,r);this.emitter.emit("connection",{client:new ls(c,h),server:p,info:{protocols:n}})?c[ka].resolve(!1):(c[ka].resolve(!0),p.connect(),p.addEventListener("open",()=>{c.dispatchEvent(ee(c,new Event("open"))),p.realWebSocket&&(c.protocol=p.realWebSocket.protocol)}))}catch(p){p instanceof Error&&(c.dispatchEvent(new Event("error")),c.readyState!==WebSocket.CLOSING&&c.readyState!==WebSocket.CLOSED&&c[oa](1011,p.message,!1),console.error(p))}}),c}});Object.defineProperty(globalThis,"WebSocket",{value:a,configurable:!0}),this.subscriptions.push(()=>{Object.defineProperty(globalThis,"WebSocket",e)})}},Zo=Qo;Zo.symbol=Symbol("websocket");const Ma=new Zo;function ds(e){Ma.on("connection",async a=>{const o=e.getHandlers().filter(Wa("EventHandler"));if(o.length>0){e==null||e.onMockedConnection(a),await Promise.all(o.map(i=>i.run(a)));return}const t=new Request(a.client.url,{headers:{upgrade:"websocket",connection:"upgrade"}});await So(t,e.getUnhandledRequestStrategy()).catch(i=>{const s=new Event("error");Object.defineProperty(s,"cause",{enumerable:!0,configurable:!1,value:i}),a.client.socket.dispatchEvent(s)}),e==null||e.onPassthroughConnection(a),a.server.connect()})}function _e(e){const a=new Date,o=`${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}:${a.getSeconds().toString().padStart(2,"0")}`;return e!=null&&e.milliseconds?`${o}.${a.getMilliseconds().toString().padStart(3,"0")}`:o}function wa(e){return e instanceof Blob?e.size:e instanceof ArrayBuffer?e.byteLength:new Blob([e]).size}const po=24;function _a(e){return e.length<=po?e:`${e.slice(0,po)}…`}async function xa(e){if(e instanceof Blob){const a=await e.text();return`Blob(${_a(a)})`}if(typeof e=="object"&&"byteLength"in e){const a=new TextDecoder().decode(e);return`ArrayBuffer(${_a(a)})`}return _a(e)}const Oe={system:"#3b82f6",outgoing:"#22c55e",incoming:"#ef4444",mocked:"#ff6a33"};function gs(e){const{client:a,server:o}=e;fs(a),a.addEventListener("message",t=>{ys(t)}),a.addEventListener("close",t=>{ks(t)}),a.socket.addEventListener("error",t=>{bs(t)}),a.send=new Proxy(a.send,{apply(t,i,s){const[n]=s,r=new MessageEvent("message",{data:n});return Object.defineProperties(r,{currentTarget:{enumerable:!0,writable:!1,value:a.socket},target:{enumerable:!0,writable:!1,value:a.socket}}),queueMicrotask(()=>{vs(r)}),Reflect.apply(t,i,s)}}),o.addEventListener("open",()=>{o.addEventListener("message",t=>{ws(t)})},{once:!0}),o.send=new Proxy(o.send,{apply(t,i,s){const[n]=s,r=new MessageEvent("message",{data:n});return Object.defineProperties(r,{currentTarget:{enumerable:!0,writable:!1,value:o.socket},target:{enumerable:!0,writable:!1,value:o.socket}}),js(r),Reflect.apply(t,i,s)}})}function fs(e){const a=ta(e.url);console.groupCollapsed(U.formatMessage(`${_e()} %c▶%c ${a}`),`color:${Oe.system}`,"color:inherit"),console.log("Client:",e.socket),console.groupEnd()}function ks(e){const a=e.target,o=ta(a.url);console.groupCollapsed(U.formatMessage(`${_e({milliseconds:!0})} %c■%c ${o}`),`color:${Oe.system}`,"color:inherit"),console.log(e),console.groupEnd()}function bs(e){const a=e.target,o=ta(a.url);console.groupCollapsed(U.formatMessage(`${_e({milliseconds:!0})} %c×%c ${o}`),`color:${Oe.system}`,"color:inherit"),console.log(e),console.groupEnd()}async function ys(e){const a=wa(e.data),o=await xa(e.data),t=e.defaultPrevented?"⇡":"⬆";console.groupCollapsed(U.formatMessage(`${_e({milliseconds:!0})} %c${t}%c ${o} %c${a}%c`),`color:${Oe.outgoing}`,"color:inherit","color:gray;font-weight:normal","color:inherit;font-weight:inherit"),console.log(e),console.groupEnd()}async function js(e){const a=wa(e.data),o=await xa(e.data);console.groupCollapsed(U.formatMessage(`${_e({milliseconds:!0})} %c⬆%c ${o} %c${a}%c`),`color:${Oe.mocked}`,"color:inherit","color:gray;font-weight:normal","color:inherit;font-weight:inherit"),console.log(e),console.groupEnd()}async function vs(e){const a=wa(e.data),o=await xa(e.data);console.groupCollapsed(U.formatMessage(`${_e({milliseconds:!0})} %c⬇%c ${o} %c${a}%c`),`color:${Oe.mocked}`,"color:inherit","color:gray;font-weight:normal","color:inherit;font-weight:inherit"),console.log(e),console.groupEnd()}async function ws(e){const a=wa(e.data),o=await xa(e.data),t=e.defaultPrevented?"⇣":"⬇";console.groupCollapsed(U.formatMessage(`${_e({milliseconds:!0})} %c${t}%c ${o} %c${a}%c`),`color:${Oe.incoming}`,"color:inherit","color:gray;font-weight:normal","color:inherit;font-weight:inherit"),console.log(e),console.groupEnd()}var xs={},Es=/(%?)(%([sdijo]))/g;function Cs(e,a){switch(a){case"s":return e;case"d":case"i":return Number(e);case"j":return JSON.stringify(e);case"o":{if(typeof e=="string")return e;const o=JSON.stringify(e);return o==="{}"||o==="[]"||/^\[object .+?\]$/.test(o)?e:o}}}function sa(e,...a){if(a.length===0)return e;let o=0,t=e.replace(Es,(i,s,n,r)=>{const c=a[o],h=Cs(c,r);return s?i:(o++,h)});return o<a.length&&(t+=` ${a.slice(o).join(" ")}`),t=t.replace(/%{2,2}/g,"%"),t}var zs=2;function Ss(e){if(!e.stack)return;const a=e.stack.split(`
`);a.splice(1,zs),e.stack=a.join(`
`)}var As=class extends Error{constructor(e,...a){super(e),this.message=e,this.name="Invariant Violation",this.message=sa(e,...a),Ss(this)}},Re=(e,a,...o)=>{if(!e)throw new As(a,...o)};Re.as=(e,a,o,...t)=>{if(!a){const i=t.length===0?o:sa(o,...t);let s;try{s=Reflect.construct(e,[i])}catch{s=e(i)}throw s}};function Va(){if(typeof navigator<"u"&&navigator.product==="ReactNative")return!0;if(typeof process<"u"){const e=process.type;return e==="renderer"||e==="worker"?!1:!!(process.versions&&process.versions.node)}return!1}var Ba=async e=>{try{return{error:null,data:await e().catch(o=>{throw o})}}catch(a){return{error:a,data:null}}};function Ls(e){return new URL(e,location.href).href}function Ia(e,a,o){return[e.active,e.installing,e.waiting].filter(n=>n!=null).find(n=>o(n.scriptURL,a))||null}var Rs=async(e,a={},o)=>{const t=Ls(e),i=await navigator.serviceWorker.getRegistrations().then(r=>r.filter(c=>Ia(c,t,o)));!navigator.serviceWorker.controller&&i.length>0&&location.reload();const[s]=i;if(s)return s.update(),[Ia(s,t,o),s];const n=await Ba(async()=>{const r=await navigator.serviceWorker.register(e,a);return[Ia(r,t,o),r]});if(n.error){if(n.error.message.includes("(404)")){const c=new URL((a==null?void 0:a.scope)||"/",location.href);throw new Error(U.formatMessage(`Failed to register a Service Worker for scope ('${c.href}') with script ('${t}'): Service Worker script does not exist at the given path.

Did you forget to run "npx msw init <PUBLIC_DIR>"?

Learn more about creating the Service Worker script: https://mswjs.io/docs/cli/init`))}throw new Error(U.formatMessage(`Failed to register the Service Worker:

%s`,n.error.message))}return n.data};function et(e={}){if(e.quiet)return;const a=e.message||"Mocking enabled.";console.groupCollapsed(`%c${U.formatMessage(a)}`,"color:orangered;font-weight:bold;"),console.log("%cDocumentation: %chttps://mswjs.io/docs","font-weight:bold","font-weight:normal"),console.log("Found an issue? https://github.com/mswjs/msw/issues"),e.workerUrl&&console.log("Worker script URL:",e.workerUrl),e.workerScope&&console.log("Worker scope:",e.workerScope),e.client&&console.log("Client ID: %s (%s)",e.client.id,e.client.frameType),console.groupEnd()}async function _s(e,a){var t,i;e.workerChannel.send("MOCK_ACTIVATE");const{payload:o}=await e.events.once("MOCKING_ENABLED");if(e.isMockingEnabled){U.warn('Found a redundant "worker.start()" call. Note that starting the worker while mocking is already enabled will have no effect. Consider removing this "worker.start()" call.');return}e.isMockingEnabled=!0,et({quiet:a.quiet,workerScope:(t=e.registration)==null?void 0:t.scope,workerUrl:(i=e.worker)==null?void 0:i.scriptURL,client:o.client})}var Is=class{constructor(e){this.port=e}postMessage(e,...a){const[o,t]=a;this.port.postMessage({type:e,data:o},{transfer:t})}};function Ts(e){if(!["HEAD","GET"].includes(e.method))return e.body}function at(e){return new Request(e.url,{...e,body:Ts(e)})}var Ps=(e,a)=>async(o,t)=>{const i=new Is(o.ports[0]),s=t.payload.id,n=at(t.payload),r=n.clone(),c=n.clone();ga.cache.set(n,c);try{await Vo(n,s,e.getRequestHandlers().filter(Wa("RequestHandler")),a,e.emitter,{onPassthroughResponse(){i.postMessage("PASSTHROUGH")},async onMockedResponse(h,{handler:p,parsedResult:m}){const v=h.clone(),b=h.clone(),y=Mi(h);if(e.supports.readableStreamTransfer){const z=h.body;i.postMessage("MOCK_RESPONSE",{...y,body:z},z?[z]:void 0)}else{const z=h.body===null?null:await v.arrayBuffer();i.postMessage("MOCK_RESPONSE",{...y,body:z})}a.quiet||e.emitter.once("response:mocked",()=>{p.log({request:r,response:b,parsedResult:m})})}})}catch(h){h instanceof Error&&(U.error(`Uncaught exception in the request handler for "%s %s":

%s

This exception has been gracefully handled as a 500 response, however, it's strongly recommended to resolve this error, as it indicates a mistake in your code. If you wish to mock an error response, please see this guide: https://mswjs.io/docs/http/mocking-responses/error-responses`,n.method,n.url,h.stack??h),i.postMessage("MOCK_RESPONSE",{status:500,statusText:"Request Handler Error",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:h.name,message:h.message,stack:h.stack})}))}};async function Ds(e){e.workerChannel.send("INTEGRITY_CHECK_REQUEST");const{payload:a}=await e.events.once("INTEGRITY_CHECK_RESPONSE");a.checksum!=="f5825c521429caf22a4dd13b66e243af"&&U.warn(`The currently registered Service Worker has been generated by a different version of MSW (${a.packageVersion}) and may not be fully compatible with the installed version.

It's recommended you update your worker script by running this command:

  • npx msw init <PUBLIC_DIR>

You can also automate this process and make the worker script update automatically upon the library installations. Read more: https://mswjs.io/docs/cli/init.`)}var Os=new TextEncoder;function qs(e){return Os.encode(e)}function Fs(e,a){return new TextDecoder(a).decode(e)}function Ms(e){return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)}var Ne=Symbol("isPatchedModule");function ot(e){try{return new URL(e),!0}catch{return!1}}function ho(e,a){const t=Object.getOwnPropertySymbols(a).find(i=>i.description===e);if(t)return Reflect.get(a,t)}var Be=class extends Response{static isConfigurableStatusCode(a){return a>=200&&a<=599}static isRedirectResponse(a){return Be.STATUS_CODES_WITH_REDIRECT.includes(a)}static isResponseWithBody(a){return!Be.STATUS_CODES_WITHOUT_BODY.includes(a)}static setUrl(a,o){if(!a||a==="about:"||!ot(a))return;const t=ho("state",o);t?t.urlList.push(new URL(a)):Object.defineProperty(o,"url",{value:a,enumerable:!0,configurable:!0,writable:!1})}static parseRawHeaders(a){const o=new Headers;for(let t=0;t<a.length;t+=2)o.append(a[t],a[t+1]);return o}constructor(a,o={}){var t;const i=(t=o.status)!=null?t:200,s=Be.isConfigurableStatusCode(i)?i:200,n=Be.isResponseWithBody(i)?a:null;if(super(n,{status:s,statusText:o.statusText,headers:o.headers}),i!==s){const r=ho("state",this);r?r.status=i:Object.defineProperty(this,"status",{value:i,enumerable:!0,configurable:!0,writable:!1})}Be.setUrl(o.url,this)}},ye=Be;ye.STATUS_CODES_WITHOUT_BODY=[101,103,204,205,304];ye.STATUS_CODES_WITH_REDIRECT=[301,302,303,307,308];var Bs=Symbol("kRawRequest");function tt(e,a){Reflect.set(e,Bs,a)}var $s=Object.defineProperty,Us=(e,a)=>{for(var o in a)$s(e,o,{get:a[o],enumerable:!0})},$a={};Us($a,{blue:()=>Hs,gray:()=>Ua,green:()=>Gs,red:()=>Ws,yellow:()=>Ns});function Ns(e){return`\x1B[33m${e}\x1B[0m`}function Hs(e){return`\x1B[34m${e}\x1B[0m`}function Ua(e){return`\x1B[90m${e}\x1B[0m`}function Ws(e){return`\x1B[31m${e}\x1B[0m`}function Gs(e){return`\x1B[32m${e}\x1B[0m`}var Ea=Va(),it=class{constructor(e){M(this,"prefix");this.name=e,this.prefix=`[${this.name}]`;const a=go("DEBUG"),o=go("LOG_LEVEL");a==="1"||a==="true"||typeof a<"u"&&this.name.startsWith(a)?(this.debug=Qe(o,"debug")?de:this.debug,this.info=Qe(o,"info")?de:this.info,this.success=Qe(o,"success")?de:this.success,this.warning=Qe(o,"warning")?de:this.warning,this.error=Qe(o,"error")?de:this.error):(this.info=de,this.success=de,this.warning=de,this.error=de,this.only=de)}extend(e){return new it(`${this.name}:${e}`)}debug(e,...a){this.logEntry({level:"debug",message:Ua(e),positionals:a,prefix:this.prefix,colors:{prefix:"gray"}})}info(e,...a){this.logEntry({level:"info",message:e,positionals:a,prefix:this.prefix,colors:{prefix:"blue"}});const o=new Vs;return(t,...i)=>{o.measure(),this.logEntry({level:"info",message:`${t} ${Ua(`${o.deltaTime}ms`)}`,positionals:i,prefix:this.prefix,colors:{prefix:"blue"}})}}success(e,...a){this.logEntry({level:"info",message:e,positionals:a,prefix:`✔ ${this.prefix}`,colors:{timestamp:"green",prefix:"green"}})}warning(e,...a){this.logEntry({level:"warning",message:e,positionals:a,prefix:`⚠ ${this.prefix}`,colors:{timestamp:"yellow",prefix:"yellow"}})}error(e,...a){this.logEntry({level:"error",message:e,positionals:a,prefix:`✖ ${this.prefix}`,colors:{timestamp:"red",prefix:"red"}})}only(e){e()}createEntry(e,a){return{timestamp:new Date,level:e,message:a}}logEntry(e){const{level:a,message:o,prefix:t,colors:i,positionals:s=[]}=e,n=this.createEntry(a,o),r=(i==null?void 0:i.timestamp)||"gray",c=(i==null?void 0:i.prefix)||"gray",h={timestamp:$a[r],prefix:$a[c]};this.getWriter(a)([h.timestamp(this.formatTimestamp(n.timestamp))].concat(t!=null?h.prefix(t):[]).concat(fo(o)).join(" "),...s.map(fo))}formatTimestamp(e){return`${e.toLocaleTimeString("en-GB")}:${e.getMilliseconds()}`}getWriter(e){switch(e){case"debug":case"success":case"info":return Js;case"warning":return Xs;case"error":return Ks}}},Vs=class{constructor(){M(this,"startTime");M(this,"endTime");M(this,"deltaTime");this.startTime=performance.now()}measure(){this.endTime=performance.now();const e=this.endTime-this.startTime;this.deltaTime=e.toFixed(2)}},de=()=>{};function Js(e,...a){if(Ea){process.stdout.write(sa(e,...a)+`
`);return}console.log(e,...a)}function Xs(e,...a){if(Ea){process.stderr.write(sa(e,...a)+`
`);return}console.warn(e,...a)}function Ks(e,...a){if(Ea){process.stderr.write(sa(e,...a)+`
`);return}console.error(e,...a)}function go(e){var a;return Ea?xs[e]:(a=globalThis[e])==null?void 0:a.toString()}function Qe(e,a){return e!==void 0&&e!==a}function fo(e){return typeof e>"u"?"undefined":e===null?"null":typeof e=="string"?e:typeof e=="object"?JSON.stringify(e):e.toString()}var Ys=class extends Error{constructor(e,a,o){super(`Possible EventEmitter memory leak detected. ${o} ${a.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`),this.emitter=e,this.type=a,this.count=o,this.name="MaxListenersExceededWarning"}},st=class{static listenerCount(e,a){return e.listenerCount(a)}constructor(){this.events=new Map,this.maxListeners=st.defaultMaxListeners,this.hasWarnedAboutPotentialMemoryLeak=!1}_emitInternalEvent(e,a,o){this.emit(e,a,o)}_getListeners(e){return Array.prototype.concat.apply([],this.events.get(e))||[]}_removeListener(e,a){const o=e.indexOf(a);return o>-1&&e.splice(o,1),[]}_wrapOnceListener(e,a){const o=(...t)=>(this.removeListener(e,o),a.apply(this,t));return Object.defineProperty(o,"name",{value:a.name}),o}setMaxListeners(e){return this.maxListeners=e,this}getMaxListeners(){return this.maxListeners}eventNames(){return Array.from(this.events.keys())}emit(e,...a){const o=this._getListeners(e);return o.forEach(t=>{t.apply(this,a)}),o.length>0}addListener(e,a){this._emitInternalEvent("newListener",e,a);const o=this._getListeners(e).concat(a);if(this.events.set(e,o),this.maxListeners>0&&this.listenerCount(e)>this.maxListeners&&!this.hasWarnedAboutPotentialMemoryLeak){this.hasWarnedAboutPotentialMemoryLeak=!0;const t=new Ys(this,e,this.listenerCount(e));console.warn(t)}return this}on(e,a){return this.addListener(e,a)}once(e,a){return this.addListener(e,this._wrapOnceListener(e,a))}prependListener(e,a){const o=this._getListeners(e);if(o.length>0){const t=[a].concat(o);this.events.set(e,t)}else this.events.set(e,o.concat(a));return this}prependOnceListener(e,a){return this.prependListener(e,this._wrapOnceListener(e,a))}removeListener(e,a){const o=this._getListeners(e);return o.length>0&&(this._removeListener(o,a),this.events.set(e,o),this._emitInternalEvent("removeListener",e,a)),this}off(e,a){return this.removeListener(e,a)}removeAllListeners(e){return e?this.events.delete(e):this.events.clear(),this}listeners(e){return Array.from(this._getListeners(e))}listenerCount(e){return this._getListeners(e).length}rawListeners(e){return this.listeners(e)}},nt=st;nt.defaultMaxListeners=10;var Qs="x-interceptors-internal-request-id";function ko(e){return globalThis[e]||void 0}function Zs(e,a){globalThis[e]=a}function en(e){delete globalThis[e]}var Ja=class{constructor(e){this.symbol=e,this.readyState="INACTIVE",this.emitter=new nt,this.subscriptions=[],this.logger=new it(e.description),this.emitter.setMaxListeners(0),this.logger.info("constructing the interceptor...")}checkEnvironment(){return!0}apply(){const e=this.logger.extend("apply");if(e.info("applying the interceptor..."),this.readyState==="APPLIED"){e.info("intercepted already applied!");return}if(!this.checkEnvironment()){e.info("the interceptor cannot be applied in this environment!");return}this.readyState="APPLYING";const o=this.getInstance();if(o){e.info("found a running instance, reusing..."),this.on=(t,i)=>(e.info('proxying the "%s" listener',t),o.emitter.addListener(t,i),this.subscriptions.push(()=>{o.emitter.removeListener(t,i),e.info('removed proxied "%s" listener!',t)}),this),this.readyState="APPLIED";return}e.info("no running instance found, setting up a new instance..."),this.setup(),this.setInstance(),this.readyState="APPLIED"}setup(){}on(e,a){const o=this.logger.extend("on");return this.readyState==="DISPOSING"||this.readyState==="DISPOSED"?(o.info("cannot listen to events, already disposed!"),this):(o.info('adding "%s" event listener:',e,a),this.emitter.on(e,a),this)}once(e,a){return this.emitter.once(e,a),this}off(e,a){return this.emitter.off(e,a),this}removeAllListeners(e){return this.emitter.removeAllListeners(e),this}dispose(){const e=this.logger.extend("dispose");if(this.readyState==="DISPOSED"){e.info("cannot dispose, already disposed!");return}if(e.info("disposing the interceptor..."),this.readyState="DISPOSING",!this.getInstance()){e.info("no interceptors running, skipping dispose...");return}if(this.clearInstance(),e.info("global symbol deleted:",ko(this.symbol)),this.subscriptions.length>0){e.info("disposing of %d subscriptions...",this.subscriptions.length);for(const a of this.subscriptions)a();this.subscriptions=[],e.info("disposed of all subscriptions!",this.subscriptions.length)}this.emitter.removeAllListeners(),e.info("destroyed the listener!"),this.readyState="DISPOSED"}getInstance(){var e;const a=ko(this.symbol);return this.logger.info("retrieved global instance:",(e=a==null?void 0:a.constructor)==null?void 0:e.name),a}setInstance(){Zs(this.symbol,this),this.logger.info("set global instance!",this.symbol.description)}clearInstance(){en(this.symbol),this.logger.info("cleared global instance!",this.symbol.description)}};function rt(){return Math.random().toString(16).slice(2)}var Na=class extends Ja{constructor(e){Na.symbol=Symbol(e.name),super(Na.symbol),this.interceptors=e.interceptors}setup(){const e=this.logger.extend("setup");e.info("applying all %d interceptors...",this.interceptors.length);for(const a of this.interceptors)e.info('applying "%s" interceptor...',a.constructor.name),a.apply(),e.info("adding interceptor dispose subscription"),this.subscriptions.push(()=>a.dispose())}on(e,a){for(const o of this.interceptors)o.on(e,a);return this}once(e,a){for(const o of this.interceptors)o.once(e,a);return this}off(e,a){for(const o of this.interceptors)o.off(e,a);return this}removeAllListeners(e){for(const a of this.interceptors)a.removeAllListeners(e);return this}};function an(e){return(a,o)=>{var n;const{payload:t}=o,i=at(t.request);if((n=t.response.type)!=null&&n.includes("opaque"))return;const s=t.response.status===0?Response.error():new ye(ye.isResponseWithBody(t.response.status)?t.response.body:null,{...t,url:i.url});e.emitter.emit(t.isMockedResponse?"response:mocked":"response:bypass",{requestId:t.request.id,request:i,response:s})}}function on(e,a){!(a!=null&&a.quiet)&&!location.href.startsWith(e.scope)&&U.warn(`Cannot intercept requests on this page because it's outside of the worker's scope ("${e.scope}"). If you wish to mock API requests on this page, you must resolve this scope issue.

- (Recommended) Register the worker at the root level ("/") of your application.
- Set the "Service-Worker-Allowed" response header to allow out-of-scope workers.`)}var tn=e=>function(o,t){return(async()=>{e.events.removeAllListeners(),e.workerChannel.on("REQUEST",Ps(e,o)),e.workerChannel.on("RESPONSE",an(e));const n=await Rs(o.serviceWorker.url,o.serviceWorker.options,o.findWorker),[r,c]=n;if(!r){const h=t!=null&&t.findWorker?U.formatMessage(`Failed to locate the Service Worker registration using a custom "findWorker" predicate.

Please ensure that the custom predicate properly locates the Service Worker registration at "%s".
More details: https://mswjs.io/docs/api/setup-worker/start#findworker
`,o.serviceWorker.url):U.formatMessage(`Failed to locate the Service Worker registration.

This most likely means that the worker script URL "%s" cannot resolve against the actual public hostname (%s). This may happen if your application runs behind a proxy, or has a dynamic hostname.

Please consider using a custom "serviceWorker.url" option to point to the actual worker script location, or a custom "findWorker" option to resolve the Service Worker registration manually. More details: https://mswjs.io/docs/api/setup-worker/start`,o.serviceWorker.url,location.host);throw new Error(h)}return e.worker=r,e.registration=c,e.events.addListener(window,"beforeunload",()=>{r.state!=="redundant"&&e.workerChannel.send("CLIENT_CLOSED"),window.clearInterval(e.keepAliveInterval),window.postMessage({type:"msw/worker:stop"})}),await Ds(e).catch(h=>{U.error("Error while checking the worker script integrity. Please report this on GitHub (https://github.com/mswjs/msw/issues), including the original error below."),console.error(h)}),e.keepAliveInterval=window.setInterval(()=>e.workerChannel.send("KEEPALIVE_REQUEST"),5e3),on(c,e.startOptions),c})().then(async n=>{const r=n.installing||n.waiting;return r&&await new Promise(c=>{r.addEventListener("statechange",()=>{if(r.state==="activated")return c()})}),await _s(e,o).catch(c=>{throw new Error(`Failed to enable mocking: ${c==null?void 0:c.message}`)}),n})};function ut(e={}){e.quiet||console.log(`%c${U.formatMessage("Mocking disabled.")}`,"color:orangered;font-weight:bold;")}var sn=e=>function(){var o;if(!e.isMockingEnabled){U.warn('Found a redundant "worker.stop()" call. Note that stopping the worker while mocking already stopped has no effect. Consider removing this "worker.stop()" call.');return}e.workerChannel.send("MOCK_DEACTIVATE"),e.isMockingEnabled=!1,window.clearInterval(e.keepAliveInterval),window.postMessage({type:"msw/worker:stop"}),ut({quiet:(o=e.startOptions)==null?void 0:o.quiet})},nn={serviceWorker:{url:"/mockServiceWorker.js",options:null},quiet:!1,waitUntilReady:!0,onUnhandledRequest:"warn",findWorker(e,a){return e===a}};function rn(){const e=(a,o)=>{e.state="pending",e.resolve=t=>{if(e.state!=="pending")return;e.result=t;const i=s=>(e.state="fulfilled",s);return a(t instanceof Promise?t:Promise.resolve(t).then(i))},e.reject=t=>{if(e.state==="pending")return queueMicrotask(()=>{e.state="rejected"}),o(e.rejectionReason=t)}};return e}var Le,We,ha,Eo,Xa=(Eo=class extends Promise{constructor(a=null){const o=rn();super((t,i)=>{o(t,i),a==null||a(o.resolve,o.reject)});Ve(this,We);Ve(this,Le);M(this,"resolve");M(this,"reject");Sa(this,Le,o),this.resolve=be(this,Le).resolve,this.reject=be(this,Le).reject}get state(){return be(this,Le).state}get rejectionReason(){return be(this,Le).rejectionReason}then(a,o){return Ie(this,We,ha).call(this,super.then(a,o))}catch(a){return Ie(this,We,ha).call(this,super.catch(a))}finally(a){return Ie(this,We,ha).call(this,super.finally(a))}},Le=new WeakMap,We=new WeakSet,ha=function(a){return Object.defineProperties(a,{resolve:{configurable:!0,value:this.resolve},reject:{configurable:!0,value:this.reject}})},Eo),ba=class extends Error{constructor(e){super(e),this.name="InterceptorError",Object.setPrototypeOf(this,ba.prototype)}},Ze=Symbol("kRequestHandled"),fe=Symbol("kResponsePromise"),Ka=class{constructor(e){this.request=e,this[Ze]=!1,this[fe]=new Xa}respondWith(e){Re.as(ba,!this[Ze],'Failed to respond to the "%s %s" request: the "request" event has already been handled.',this.request.method,this.request.url),this[Ze]=!0,this[fe].resolve(e)}errorWith(e){Re.as(ba,!this[Ze],'Failed to error the "%s %s" request: the "request" event has already been handled.',this.request.method,this.request.url),this[Ze]=!0,this[fe].resolve(e)}};async function ya(e,a,...o){const t=e.listeners(a);if(t.length!==0)for(const i of t)await i.apply(e,o)}function ct(e,a=!1){return a?Object.prototype.toString.call(e).startsWith("[object "):Object.prototype.toString.call(e)==="[object Object]"}function da(e,a){try{return e[a],!0}catch{return!1}}function un(e){return new Response(JSON.stringify(e instanceof Error?{name:e.name,message:e.message,stack:e.stack}:e),{status:500,statusText:"Unhandled Exception",headers:{"Content-Type":"application/json"}})}function cn(e){return e!=null&&e instanceof Response&&da(e,"type")&&e.type==="error"}function ln(e){return ct(e,!0)&&da(e,"status")&&da(e,"statusText")&&da(e,"bodyUsed")}function mn(e){return e==null||!(e instanceof Error)?!1:"code"in e&&"errno"in e}async function lt(e){const a=async s=>s instanceof Error?(e.onError(s),!0):cn(s)?(e.onRequestError(s),!0):ln(s)?(await e.onResponse(s),!0):ct(s)?(e.onError(s),!0):!1,o=async s=>{if(s instanceof ba)throw i.error;return mn(s)?(e.onError(s),!0):s instanceof Response?await a(s):!1};e.emitter.once("request",({requestId:s})=>{s===e.requestId&&e.controller[fe].state==="pending"&&e.controller[fe].resolve(void 0)});const t=new Xa;e.request.signal&&(e.request.signal.aborted?t.reject(e.request.signal.reason):e.request.signal.addEventListener("abort",()=>{t.reject(e.request.signal.reason)},{once:!0}));const i=await Ba(async()=>{const s=ya(e.emitter,"request",{requestId:e.requestId,request:e.request,controller:e.controller});return await Promise.race([t,s,e.controller[fe]]),await e.controller[fe]});if(t.state==="rejected")return e.onError(t.rejectionReason),!0;if(i.error){if(await o(i.error))return!0;if(e.emitter.listenerCount("unhandledException")>0){const s=new Ka(e.request);await ya(e.emitter,"unhandledException",{error:i.error,request:e.request,requestId:e.requestId,controller:s}).then(()=>{s[fe].state==="pending"&&s[fe].resolve(void 0)});const n=await Ba(()=>s[fe]);if(n.error)return o(n.error);if(n.data)return a(n.data)}return e.onResponse(un(i.error)),!0}return i.data?a(i.data):!1}function mt(e){const a=Object.getOwnPropertyDescriptor(globalThis,e);return typeof a>"u"||typeof a.get=="function"&&typeof a.get()>"u"||typeof a.get>"u"&&a.value==null?!1:typeof a.set>"u"&&!a.configurable?(console.error(`[MSW] Failed to apply interceptor: the global \`${e}\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`),!1):!0}function De(e){return Object.assign(new TypeError("Failed to fetch"),{cause:e})}var pn=["content-encoding","content-language","content-location","content-type","content-length"],Ta=Symbol("kRedirectCount");async function hn(e,a){if(a.status!==303&&e.body!=null)return Promise.reject(De());const o=new URL(e.url);let t;try{t=new URL(a.headers.get("location"),e.url)}catch(s){return Promise.reject(De(s))}if(!(t.protocol==="http:"||t.protocol==="https:"))return Promise.reject(De("URL scheme must be a HTTP(S) scheme"));if(Reflect.get(e,Ta)>20)return Promise.reject(De("redirect count exceeded"));if(Object.defineProperty(e,Ta,{value:(Reflect.get(e,Ta)||0)+1}),e.mode==="cors"&&(t.username||t.password)&&!bo(o,t))return Promise.reject(De('cross origin not allowed for request mode "cors"'));const i={};return([301,302].includes(a.status)&&e.method==="POST"||a.status===303&&!["HEAD","GET"].includes(e.method))&&(i.method="GET",i.body=null,pn.forEach(s=>{e.headers.delete(s)})),bo(o,t)||(e.headers.delete("authorization"),e.headers.delete("proxy-authorization"),e.headers.delete("cookie"),e.headers.delete("host")),i.headers=e.headers,fetch(new Request(t,i))}function bo(e,a){return e.origin===a.origin&&e.origin==="null"||e.protocol===a.protocol&&e.hostname===a.hostname&&e.port===a.port}var dn=class extends TransformStream{constructor(){console.warn("[Interceptors]: Brotli decompression of response streams is not supported in the browser"),super({transform(e,a){a.enqueue(e)}})}},gn=class extends TransformStream{constructor(e,...a){super({},...a);const o=[super.readable,...e].reduce((t,i)=>t.pipeThrough(i));Object.defineProperty(this,"readable",{get(){return o}})}};function fn(e){return e.toLowerCase().split(",").map(a=>a.trim())}function kn(e){if(e==="")return null;const a=fn(e);if(a.length===0)return null;const o=a.reduceRight((t,i)=>i==="gzip"||i==="x-gzip"?t.concat(new DecompressionStream("gzip")):i==="deflate"?t.concat(new DecompressionStream("deflate")):i==="br"?t.concat(new dn):(t.length=0,t),[]);return new gn(o)}function bn(e){if(e.body===null)return null;const a=kn(e.headers.get("content-encoding")||"");return a?(e.body.pipeTo(a.writable),a.readable):null}var pt=class extends Ja{constructor(){super(pt.symbol)}checkEnvironment(){return mt("fetch")}async setup(){const e=globalThis.fetch;Re(!e[Ne],'Failed to patch the "fetch" module: already patched.'),globalThis.fetch=async(a,o)=>{const t=rt(),i=typeof a=="string"&&typeof location<"u"&&!ot(a)?new URL(a,location.href):a,s=new Request(i,o);a instanceof Request&&tt(s,a);const n=new Xa,r=new Ka(s);if(this.logger.info("[%s] %s",s.method,s.url),this.logger.info("awaiting for the mocked response..."),this.logger.info('emitting the "request" event for %s listener(s)...',this.emitter.listenerCount("request")),await lt({request:s,requestId:t,emitter:this.emitter,controller:r,onResponse:async p=>{this.logger.info("received mocked response!",{rawResponse:p});const m=bn(p),v=m===null?p:new ye(m,p);if(ye.setUrl(s.url,v),ye.isRedirectResponse(v.status)){if(s.redirect==="error"){n.reject(De("unexpected redirect"));return}if(s.redirect==="follow"){hn(s,v).then(b=>{n.resolve(b)},b=>{n.reject(b)});return}}this.emitter.listenerCount("response")>0&&(this.logger.info('emitting the "response" event...'),await ya(this.emitter,"response",{response:v.clone(),isMockedResponse:!0,request:s,requestId:t})),n.resolve(v)},onRequestError:p=>{this.logger.info("request has errored!",{response:p}),n.reject(De(p))},onError:p=>{this.logger.info("request has been aborted!",{error:p}),n.reject(p)}}))return this.logger.info("request has been handled, returning mock promise..."),n;this.logger.info("no mocked response received, performing request as-is...");const h=s.clone();return e(s).then(async p=>{if(this.logger.info("original fetch performed",p),this.emitter.listenerCount("response")>0){this.logger.info('emitting the "response" event...');const m=p.clone();await ya(this.emitter,"response",{response:m,isMockedResponse:!1,request:h,requestId:t})}return p})},Object.defineProperty(globalThis.fetch,Ne,{enumerable:!0,configurable:!0,value:!0}),this.subscriptions.push(()=>{Object.defineProperty(globalThis.fetch,Ne,{value:void 0}),globalThis.fetch=e,this.logger.info('restored native "globalThis.fetch"!',globalThis.fetch.name)})}},ht=pt;ht.symbol=Symbol("fetch");function yn(e,a){const o=new Uint8Array(e.byteLength+a.byteLength);return o.set(e,0),o.set(a,e.byteLength),o}var dt=class{constructor(e,a){this.NONE=0,this.CAPTURING_PHASE=1,this.AT_TARGET=2,this.BUBBLING_PHASE=3,this.type="",this.srcElement=null,this.currentTarget=null,this.eventPhase=0,this.isTrusted=!0,this.composed=!1,this.cancelable=!0,this.defaultPrevented=!1,this.bubbles=!0,this.lengthComputable=!0,this.loaded=0,this.total=0,this.cancelBubble=!1,this.returnValue=!0,this.type=e,this.target=(a==null?void 0:a.target)||null,this.currentTarget=(a==null?void 0:a.currentTarget)||null,this.timeStamp=Date.now()}composedPath(){return[]}initEvent(e,a,o){this.type=e,this.bubbles=!!a,this.cancelable=!!o}preventDefault(){this.defaultPrevented=!0}stopPropagation(){}stopImmediatePropagation(){}},jn=class extends dt{constructor(e,a){super(e),this.lengthComputable=(a==null?void 0:a.lengthComputable)||!1,this.composed=(a==null?void 0:a.composed)||!1,this.loaded=(a==null?void 0:a.loaded)||0,this.total=(a==null?void 0:a.total)||0}},vn=typeof ProgressEvent<"u";function wn(e,a,o){const t=["error","progress","loadstart","loadend","load","timeout","abort"],i=vn?ProgressEvent:jn;return t.includes(a)?new i(a,{lengthComputable:!0,loaded:(o==null?void 0:o.loaded)||0,total:(o==null?void 0:o.total)||0}):new dt(a,{target:e,currentTarget:e})}function gt(e,a){if(!(a in e))return null;if(Object.prototype.hasOwnProperty.call(e,a))return e;const t=Reflect.getPrototypeOf(e);return t?gt(t,a):null}function Pa(e,a){return new Proxy(e,xn(a))}function xn(e){const{constructorCall:a,methodCall:o,getProperty:t,setProperty:i}=e,s={};return typeof a<"u"&&(s.construct=function(n,r,c){const h=Reflect.construct.bind(null,n,r,c);return a.call(c,r,h)}),s.set=function(n,r,c){const h=()=>{const p=gt(n,r)||n,m=Reflect.getOwnPropertyDescriptor(p,r);return typeof(m==null?void 0:m.set)<"u"?(m.set.apply(n,[c]),!0):Reflect.defineProperty(p,r,{writable:!0,enumerable:!0,configurable:!0,value:c})};return typeof i<"u"?i.call(n,[r,c],h):h()},s.get=function(n,r,c){const h=()=>n[r],p=typeof t<"u"?t.call(n,[r,c],h):h();return typeof p=="function"?(...m)=>{const v=p.bind(n,...m);return typeof o<"u"?o.call(n,[r,m],v):v()}:p},s}function En(e){return["application/xhtml+xml","application/xml","image/svg+xml","text/html","text/xml"].some(o=>e.startsWith(o))}function Cn(e){try{return JSON.parse(e)}catch{return null}}function zn(e,a){const o=ye.isResponseWithBody(e.status)?a:null;return new ye(o,{url:e.responseURL,status:e.status,statusText:e.statusText,headers:Sn(e.getAllResponseHeaders())})}function Sn(e){const a=new Headers,o=e.split(/[\r\n]+/);for(const t of o){if(t.trim()==="")continue;const[i,...s]=t.split(": "),n=s.join(": ");a.append(i,n)}return a}async function yo(e){const a=e.headers.get("content-length");return a!=null&&a!==""?Number(a):(await e.arrayBuffer()).byteLength}var ea=Symbol("kIsRequestHandled"),An=Va(),Da=Symbol("kFetchRequest"),Ln=class{constructor(e,a){this.initialRequest=e,this.logger=a,this.method="GET",this.url=null,this[ea]=!1,this.events=new Map,this.uploadEvents=new Map,this.requestId=rt(),this.requestHeaders=new Headers,this.responseBuffer=new Uint8Array,this.request=Pa(e,{setProperty:([o,t],i)=>{switch(o){case"ontimeout":{const s=o.slice(2);return this.request.addEventListener(s,t),i()}default:return i()}},methodCall:([o,t],i)=>{var s;switch(o){case"open":{const[n,r]=t;return typeof r>"u"?(this.method="GET",this.url=jo(n)):(this.method=n,this.url=jo(r)),this.logger=this.logger.extend(`${this.method} ${this.url.href}`),this.logger.info("open",this.method,this.url.href),i()}case"addEventListener":{const[n,r]=t;return this.registerEvent(n,r),this.logger.info("addEventListener",n,r),i()}case"setRequestHeader":{const[n,r]=t;return this.requestHeaders.set(n,r),this.logger.info("setRequestHeader",n,r),i()}case"send":{const[n]=t;this.request.addEventListener("load",()=>{if(typeof this.onResponse<"u"){const p=zn(this.request,this.request.response);this.onResponse.call(this,{response:p,isMockedResponse:this[ea],request:c,requestId:this.requestId})}});const r=typeof n=="string"?qs(n):n,c=this.toFetchApiRequest(r);this[Da]=c.clone(),(((s=this.onRequest)==null?void 0:s.call(this,{request:c,requestId:this.requestId}))||Promise.resolve()).finally(()=>{if(!this[ea])return this.logger.info("request callback settled but request has not been handled (readystate %d), performing as-is...",this.request.readyState),An&&this.request.setRequestHeader(Qs,this.requestId),i()});break}default:return i()}}}),Me(this.request,"upload",Pa(this.request.upload,{setProperty:([o,t],i)=>{switch(o){case"onloadstart":case"onprogress":case"onaboart":case"onerror":case"onload":case"ontimeout":case"onloadend":{const s=o.slice(2);this.registerUploadEvent(s,t)}}return i()},methodCall:([o,t],i)=>{switch(o){case"addEventListener":{const[s,n]=t;return this.registerUploadEvent(s,n),this.logger.info("upload.addEventListener",s,n),i()}}}}))}registerEvent(e,a){const t=(this.events.get(e)||[]).concat(a);this.events.set(e,t),this.logger.info('registered event "%s"',e,a)}registerUploadEvent(e,a){const t=(this.uploadEvents.get(e)||[]).concat(a);this.uploadEvents.set(e,t),this.logger.info('registered upload event "%s"',e,a)}async respondWith(e){if(this[ea]=!0,this[Da]){const t=await yo(this[Da]);this.trigger("loadstart",this.request.upload,{loaded:0,total:t}),this.trigger("progress",this.request.upload,{loaded:t,total:t}),this.trigger("load",this.request.upload,{loaded:t,total:t}),this.trigger("loadend",this.request.upload,{loaded:t,total:t})}this.logger.info("responding with a mocked response: %d %s",e.status,e.statusText),Me(this.request,"status",e.status),Me(this.request,"statusText",e.statusText),Me(this.request,"responseURL",this.url.href),this.request.getResponseHeader=new Proxy(this.request.getResponseHeader,{apply:(t,i,s)=>{if(this.logger.info("getResponseHeader",s[0]),this.request.readyState<this.request.HEADERS_RECEIVED)return this.logger.info("headers not received yet, returning null"),null;const n=e.headers.get(s[0]);return this.logger.info('resolved response header "%s" to',s[0],n),n}}),this.request.getAllResponseHeaders=new Proxy(this.request.getAllResponseHeaders,{apply:()=>{if(this.logger.info("getAllResponseHeaders"),this.request.readyState<this.request.HEADERS_RECEIVED)return this.logger.info("headers not received yet, returning empty string"),"";const i=Array.from(e.headers.entries()).map(([s,n])=>`${s}: ${n}`).join(`\r
`);return this.logger.info("resolved all response headers to",i),i}}),Object.defineProperties(this.request,{response:{enumerable:!0,configurable:!1,get:()=>this.response},responseText:{enumerable:!0,configurable:!1,get:()=>this.responseText},responseXML:{enumerable:!0,configurable:!1,get:()=>this.responseXML}});const a=await yo(e.clone());this.logger.info("calculated response body length",a),this.trigger("loadstart",this.request,{loaded:0,total:a}),this.setReadyState(this.request.HEADERS_RECEIVED),this.setReadyState(this.request.LOADING);const o=()=>{this.logger.info("finalizing the mocked response..."),this.setReadyState(this.request.DONE),this.trigger("load",this.request,{loaded:this.responseBuffer.byteLength,total:a}),this.trigger("loadend",this.request,{loaded:this.responseBuffer.byteLength,total:a})};if(e.body){this.logger.info("mocked response has body, streaming...");const t=e.body.getReader(),i=async()=>{const{value:s,done:n}=await t.read();if(n){this.logger.info("response body stream done!"),o();return}s&&(this.logger.info("read response body chunk:",s),this.responseBuffer=yn(this.responseBuffer,s),this.trigger("progress",this.request,{loaded:this.responseBuffer.byteLength,total:a})),i()};i()}else o()}responseBufferToText(){return Fs(this.responseBuffer)}get response(){if(this.logger.info("getResponse (responseType: %s)",this.request.responseType),this.request.readyState!==this.request.DONE)return null;switch(this.request.responseType){case"json":{const e=Cn(this.responseBufferToText());return this.logger.info("resolved response JSON",e),e}case"arraybuffer":{const e=Ms(this.responseBuffer);return this.logger.info("resolved response ArrayBuffer",e),e}case"blob":{const e=this.request.getResponseHeader("Content-Type")||"text/plain",a=new Blob([this.responseBufferToText()],{type:e});return this.logger.info("resolved response Blob (mime type: %s)",a,e),a}default:{const e=this.responseBufferToText();return this.logger.info('resolving "%s" response type as text',this.request.responseType,e),e}}}get responseText(){if(Re(this.request.responseType===""||this.request.responseType==="text","InvalidStateError: The object is in invalid state."),this.request.readyState!==this.request.LOADING&&this.request.readyState!==this.request.DONE)return"";const e=this.responseBufferToText();return this.logger.info('getResponseText: "%s"',e),e}get responseXML(){if(Re(this.request.responseType===""||this.request.responseType==="document","InvalidStateError: The object is in invalid state."),this.request.readyState!==this.request.DONE)return null;const e=this.request.getResponseHeader("Content-Type")||"";return typeof DOMParser>"u"?(console.warn("Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly."),null):En(e)?new DOMParser().parseFromString(this.responseBufferToText(),e):null}errorWith(e){this[ea]=!0,this.logger.info("responding with an error"),this.setReadyState(this.request.DONE),this.trigger("error",this.request),this.trigger("loadend",this.request)}setReadyState(e){if(this.logger.info("setReadyState: %d -> %d",this.request.readyState,e),this.request.readyState===e){this.logger.info("ready state identical, skipping transition...");return}Me(this.request,"readyState",e),this.logger.info("set readyState to: %d",e),e!==this.request.UNSENT&&(this.logger.info('triggerring "readystatechange" event...'),this.trigger("readystatechange",this.request))}trigger(e,a,o){const t=a[`on${e}`],i=wn(a,e,o);this.logger.info('trigger "%s"',e,o||""),typeof t=="function"&&(this.logger.info('found a direct "%s" callback, calling...',e),t.call(a,i));const s=a instanceof XMLHttpRequestUpload?this.uploadEvents:this.events;for(const[n,r]of s)n===e&&(this.logger.info('found %d listener(s) for "%s" event, calling...',r.length,e),r.forEach(c=>c.call(a,i)))}toFetchApiRequest(e){this.logger.info("converting request to a Fetch API Request...");const a=e instanceof Document?e.documentElement.innerText:e,o=new Request(this.url.href,{method:this.method,headers:this.requestHeaders,credentials:this.request.withCredentials?"include":"same-origin",body:["GET","HEAD"].includes(this.method.toUpperCase())?null:a}),t=Pa(o.headers,{methodCall:([i,s],n)=>{switch(i){case"append":case"set":{const[r,c]=s;this.request.setRequestHeader(r,c);break}case"delete":{const[r]=s;console.warn(`XMLHttpRequest: Cannot remove a "${r}" header from the Fetch API representation of the "${o.method} ${o.url}" request. XMLHttpRequest headers cannot be removed.`);break}}return n()}});return Me(o,"headers",t),tt(o,this.request),this.logger.info("converted request to a Fetch API Request!",o),o}};function jo(e){return typeof location>"u"?new URL(e):new URL(e.toString(),location.href)}function Me(e,a,o){Reflect.defineProperty(e,a,{writable:!0,enumerable:!0,value:o})}function Rn({emitter:e,logger:a}){return new Proxy(globalThis.XMLHttpRequest,{construct(t,i,s){a.info("constructed new XMLHttpRequest");const n=Reflect.construct(t,i,s),r=Object.getOwnPropertyDescriptors(t.prototype);for(const h in r)Reflect.defineProperty(n,h,r[h]);const c=new Ln(n,a);return c.onRequest=async function({request:h,requestId:p}){const m=new Ka(h);this.logger.info("awaiting mocked response..."),this.logger.info('emitting the "request" event for %s listener(s)...',e.listenerCount("request")),await lt({request:h,requestId:p,controller:m,emitter:e,onResponse:async b=>{await this.respondWith(b)},onRequestError:()=>{this.errorWith(new TypeError("Network error"))},onError:b=>{this.logger.info("request errored!",{error:b}),b instanceof Error&&this.errorWith(b)}})||this.logger.info("no mocked response received, performing request as-is...")},c.onResponse=async function({response:h,isMockedResponse:p,request:m,requestId:v}){this.logger.info('emitting the "response" event for %s listener(s)...',e.listenerCount("response")),e.emit("response",{response:h,isMockedResponse:p,request:m,requestId:v})},c.request}})}var ft=class extends Ja{constructor(){super(ft.interceptorSymbol)}checkEnvironment(){return mt("XMLHttpRequest")}setup(){const e=this.logger.extend("setup");e.info('patching "XMLHttpRequest" module...');const a=globalThis.XMLHttpRequest;Re(!a[Ne],'Failed to patch the "XMLHttpRequest" module: already patched.'),globalThis.XMLHttpRequest=Rn({emitter:this.emitter,logger:this.logger}),e.info('native "XMLHttpRequest" module patched!',globalThis.XMLHttpRequest.name),Object.defineProperty(globalThis.XMLHttpRequest,Ne,{enumerable:!0,configurable:!0,value:!0}),this.subscriptions.push(()=>{Object.defineProperty(globalThis.XMLHttpRequest,Ne,{value:void 0}),globalThis.XMLHttpRequest=a,e.info('native "XMLHttpRequest" module restored!',globalThis.XMLHttpRequest.name)})}},kt=ft;kt.interceptorSymbol=Symbol("xhr");function _n(e,a){const o=new Na({name:"fallback",interceptors:[new ht,new kt]});return o.on("request",async({request:t,requestId:i,controller:s})=>{const n=t.clone(),r=await Vo(t,i,e.getRequestHandlers().filter(Wa("RequestHandler")),a,e.emitter,{onMockedResponse(c,{handler:h,parsedResult:p}){a.quiet||e.emitter.once("response:mocked",({response:m})=>{h.log({request:n,response:m,parsedResult:p})})}});r&&s.respondWith(r)}),o.on("response",({response:t,isMockedResponse:i,request:s,requestId:n})=>{e.emitter.emit(i?"response:mocked":"response:bypass",{response:t,request:s,requestId:n})}),o.apply(),o}function In(e){return async function(o){e.fallbackInterceptor=_n(e,o),et({message:"Mocking enabled (fallback mode).",quiet:o.quiet})}}function Tn(e){return function(){var o,t;(o=e.fallbackInterceptor)==null||o.dispose(),ut({quiet:(t=e.startOptions)==null?void 0:t.quiet})}}function Pn(){try{const e=new ReadableStream({start:o=>o.close()});return new MessageChannel().port1.postMessage(e,[e]),!0}catch{return!1}}var Dn=class extends Wi{constructor(...a){super(...a);M(this,"context");M(this,"startHandler",null);M(this,"stopHandler",null);M(this,"listeners");Re(!Va(),U.formatMessage("Failed to execute `setupWorker` in a non-browser environment. Consider using `setupServer` for Node.js environment instead.")),this.listeners=[],this.context=this.createWorkerContext()}createWorkerContext(){const a={isMockingEnabled:!1,startOptions:null,worker:null,getRequestHandlers:()=>this.handlersController.currentHandlers(),registration:null,emitter:this.emitter,workerChannel:{on:(o,t)=>{this.context.events.addListener(navigator.serviceWorker,"message",i=>{if(i.source!==this.context.worker)return;const s=i.data;s&&s.type===o&&t(i,s)})},send:o=>{var t;(t=this.context.worker)==null||t.postMessage(o)}},events:{addListener:(o,t,i)=>(o.addEventListener(t,i),this.listeners.push({eventType:t,target:o,callback:i}),()=>{o.removeEventListener(t,i)}),removeAllListeners:()=>{for(const{target:o,eventType:t,callback:i}of this.listeners)o.removeEventListener(t,i);this.listeners=[]},once:o=>{const t=[];return new Promise((i,s)=>{const n=r=>{try{const c=r.data;c.type===o&&i(c)}catch(c){s(c)}};t.push(this.context.events.addListener(navigator.serviceWorker,"message",n),this.context.events.addListener(navigator.serviceWorker,"messageerror",s))}).finally(()=>{t.forEach(i=>i())})}},supports:{serviceWorkerApi:!("serviceWorker"in navigator)||location.protocol==="file:",readableStreamTransfer:Pn()}};return this.startHandler=a.supports.serviceWorkerApi?In(a):tn(a),this.stopHandler=a.supports.serviceWorkerApi?Tn(a):sn(a),a}async start(a={}){return a.waitUntilReady===!0&&U.warn('The "waitUntilReady" option has been deprecated. Please remove it from this "worker.start()" call. Follow the recommended Browser integration (https://mswjs.io/docs/integrations/browser) to eliminate any race conditions between the Service Worker registration and any requests made by your application on initial render.'),this.context.startOptions=Jo(nn,a),ds({getUnhandledRequestStrategy:()=>this.context.startOptions.onUnhandledRequest,getHandlers:()=>this.handlersController.currentHandlers(),onMockedConnection:o=>{this.context.startOptions.quiet||gs(o)},onPassthroughConnection(){}}),Ma.apply(),this.subscriptions.push(()=>{Ma.dispose()}),await this.startHandler(this.context.startOptions,a)}stop(){super.dispose(),this.context.events.removeAllListeners(),this.context.emitter.removeAllListeners(),this.stopHandler()}};function On(...e){return new Dn(...e)}function qn(){le(typeof URL<"u",U.formatMessage(`Global "URL" class is not defined. This likely means that you're running MSW in an environment that doesn't support all Node.js standard API (e.g. React Native). If that's the case, please use an appropriate polyfill for the "URL" class, like "react-native-url-polyfill".`))}function Fn(e,a){return e.toLowerCase()===a.toLowerCase()}function Mn(e){return e<300?"#69AB32":e<400?"#F0BB4B":"#E95F5D"}async function Bn(e){const o=await e.clone().text();return{url:new URL(e.url),method:e.method,headers:Object.fromEntries(e.headers.entries()),body:o}}const{message:$n}=Uo;async function Un(e){const a=e.clone(),o=await a.text(),t=a.status||200,i=a.statusText||$n[t]||"OK";return{status:t,statusText:i,headers:Object.fromEntries(a.headers.entries()),body:o}}function Nn(e){for(var a=[],o=0;o<e.length;){var t=e[o];if(t==="*"||t==="+"||t==="?"){a.push({type:"MODIFIER",index:o,value:e[o++]});continue}if(t==="\\"){a.push({type:"ESCAPED_CHAR",index:o++,value:e[o++]});continue}if(t==="{"){a.push({type:"OPEN",index:o,value:e[o++]});continue}if(t==="}"){a.push({type:"CLOSE",index:o,value:e[o++]});continue}if(t===":"){for(var i="",s=o+1;s<e.length;){var n=e.charCodeAt(s);if(n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||n===95){i+=e[s++];continue}break}if(!i)throw new TypeError("Missing parameter name at ".concat(o));a.push({type:"NAME",index:o,value:i}),o=s;continue}if(t==="("){var r=1,c="",s=o+1;if(e[s]==="?")throw new TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if(e[s]==="\\"){c+=e[s++]+e[s++];continue}if(e[s]===")"){if(r--,r===0){s++;break}}else if(e[s]==="("&&(r++,e[s+1]!=="?"))throw new TypeError("Capturing groups are not allowed at ".concat(s));c+=e[s++]}if(r)throw new TypeError("Unbalanced pattern at ".concat(o));if(!c)throw new TypeError("Missing pattern at ".concat(o));a.push({type:"PATTERN",index:o,value:c}),o=s;continue}a.push({type:"CHAR",index:o,value:e[o++]})}return a.push({type:"END",index:o,value:""}),a}function Hn(e,a){a===void 0&&(a={});for(var o=Nn(e),t=a.prefixes,i=t===void 0?"./":t,s=a.delimiter,n=s===void 0?"/#?":s,r=[],c=0,h=0,p="",m=function(S){if(h<o.length&&o[h].type===S)return o[h++].value},v=function(S){var _=m(S);if(_!==void 0)return _;var I=o[h],W=I.type,me=I.index;throw new TypeError("Unexpected ".concat(W," at ").concat(me,", expected ").concat(S))},b=function(){for(var S="",_;_=m("CHAR")||m("ESCAPED_CHAR");)S+=_;return S},y=function(S){for(var _=0,I=n;_<I.length;_++){var W=I[_];if(S.indexOf(W)>-1)return!0}return!1},z=function(S){var _=r[r.length-1],I=S||(_&&typeof _=="string"?_:"");if(_&&!I)throw new TypeError('Must have text between two parameters, missing text after "'.concat(_.name,'"'));return!I||y(I)?"[^".concat(Se(n),"]+?"):"(?:(?!".concat(Se(I),")[^").concat(Se(n),"])+?")};h<o.length;){var C=m("CHAR"),A=m("NAME"),q=m("PATTERN");if(A||q){var R=C||"";i.indexOf(R)===-1&&(p+=R,R=""),p&&(r.push(p),p=""),r.push({name:A||c++,prefix:R,suffix:"",pattern:q||z(R),modifier:m("MODIFIER")||""});continue}var g=C||m("ESCAPED_CHAR");if(g){p+=g;continue}p&&(r.push(p),p="");var j=m("OPEN");if(j){var R=b(),E=m("NAME")||"",w=m("PATTERN")||"",T=b();v("CLOSE"),r.push({name:E||(w?c++:""),pattern:E&&!w?z(R):w,prefix:R,suffix:T,modifier:m("MODIFIER")||""});continue}v("END")}return r}function Wn(e,a){var o=[],t=yt(e,o,a);return Gn(t,o,a)}function Gn(e,a,o){o===void 0&&(o={});var t=o.decode,i=t===void 0?function(s){return s}:t;return function(s){var n=e.exec(s);if(!n)return!1;for(var r=n[0],c=n.index,h=Object.create(null),p=function(v){if(n[v]===void 0)return"continue";var b=a[v-1];b.modifier==="*"||b.modifier==="+"?h[b.name]=n[v].split(b.prefix+b.suffix).map(function(y){return i(y,b)}):h[b.name]=i(n[v],b)},m=1;m<n.length;m++)p(m);return{path:r,index:c,params:h}}}function Se(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function bt(e){return e&&e.sensitive?"":"i"}function Vn(e,a){if(!a)return e;for(var o=/\((?:\?<(.*?)>)?(?!\?)/g,t=0,i=o.exec(e.source);i;)a.push({name:i[1]||t++,prefix:"",suffix:"",modifier:"",pattern:""}),i=o.exec(e.source);return e}function Jn(e,a,o){var t=e.map(function(i){return yt(i,a,o).source});return new RegExp("(?:".concat(t.join("|"),")"),bt(o))}function Xn(e,a,o){return Kn(Hn(e,o),a,o)}function Kn(e,a,o){o===void 0&&(o={});for(var t=o.strict,i=t===void 0?!1:t,s=o.start,n=s===void 0?!0:s,r=o.end,c=r===void 0?!0:r,h=o.encode,p=h===void 0?function(_){return _}:h,m=o.delimiter,v=m===void 0?"/#?":m,b=o.endsWith,y=b===void 0?"":b,z="[".concat(Se(y),"]|$"),C="[".concat(Se(v),"]"),A=n?"^":"",q=0,R=e;q<R.length;q++){var g=R[q];if(typeof g=="string")A+=Se(p(g));else{var j=Se(p(g.prefix)),E=Se(p(g.suffix));if(g.pattern)if(a&&a.push(g),j||E)if(g.modifier==="+"||g.modifier==="*"){var w=g.modifier==="*"?"?":"";A+="(?:".concat(j,"((?:").concat(g.pattern,")(?:").concat(E).concat(j,"(?:").concat(g.pattern,"))*)").concat(E,")").concat(w)}else A+="(?:".concat(j,"(").concat(g.pattern,")").concat(E,")").concat(g.modifier);else{if(g.modifier==="+"||g.modifier==="*")throw new TypeError('Can not repeat "'.concat(g.name,'" without a prefix and suffix'));A+="(".concat(g.pattern,")").concat(g.modifier)}else A+="(?:".concat(j).concat(E,")").concat(g.modifier)}}if(c)i||(A+="".concat(C,"?")),A+=o.endsWith?"(?=".concat(z,")"):"$";else{var T=e[e.length-1],S=typeof T=="string"?C.indexOf(T[T.length-1])>-1:T===void 0;i||(A+="(?:".concat(C,"(?=").concat(z,"))?")),S||(A+="(?=".concat(C,"|").concat(z,")"))}return new RegExp(A,bt(o))}function yt(e,a,o){return e instanceof RegExp?Vn(e,a):Array.isArray(e)?Jn(e,a,o):Xn(e,a,o)}new TextEncoder;function Yn(e){try{return new URL(e),!0}catch{return!1}}function vo(e,a){const t=Object.getOwnPropertySymbols(a).find(i=>i.description===e);if(t)return Reflect.get(a,t)}var $e=class extends Response{static isConfigurableStatusCode(e){return e>=200&&e<=599}static isRedirectResponse(e){return $e.STATUS_CODES_WITH_REDIRECT.includes(e)}static isResponseWithBody(e){return!$e.STATUS_CODES_WITHOUT_BODY.includes(e)}static setUrl(e,a){if(!e||e==="about:"||!Yn(e))return;const o=vo("state",a);o?o.urlList.push(new URL(e)):Object.defineProperty(a,"url",{value:e,enumerable:!0,configurable:!0,writable:!1})}static parseRawHeaders(e){const a=new Headers;for(let o=0;o<e.length;o+=2)a.append(e[o],e[o+1]);return a}constructor(e,a={}){var o;const t=(o=a.status)!=null?o:200,i=$e.isConfigurableStatusCode(t)?t:200,s=$e.isResponseWithBody(t)?e:null;if(super(s,{status:i,statusText:a.statusText,headers:a.headers}),t!==i){const n=vo("state",this);n?n.status=t:Object.defineProperty(this,"status",{value:t,enumerable:!0,configurable:!0,writable:!1})}$e.setUrl(a.url,this)}},Ya=$e;Ya.STATUS_CODES_WITHOUT_BODY=[101,103,204,205,304];Ya.STATUS_CODES_WITH_REDIRECT=[301,302,303,307,308];function Qn(e,a=!0){return[a&&e.origin,e.pathname].filter(Boolean).join("")}const Zn=/[\?|#].*$/g;function er(e){return new URL(`/${e}`,"http://localhost").searchParams}function jt(e){return e.endsWith("?")?e:e.replace(Zn,"")}function ar(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}function or(e,a){if(ar(e)||e.startsWith("*"))return e;const o=a||typeof location<"u"&&location.href;return o?decodeURI(new URL(encodeURI(e),o).href):e}function tr(e,a){if(e instanceof RegExp)return e;const o=or(e,a);return jt(o)}function ir(e){return e.replace(/([:a-zA-Z_-]*)(\*{1,2})+/g,(a,o,t)=>{const i="(.*)";return o?o.startsWith(":")?`${o}${t}`:`${o}${i}`:i}).replace(/([^\/])(:)(?=\d+)/,"$1\\$2").replace(/^([^\/]+)(:)(?=\/\/)/,"$1\\$2")}function sr(e,a,o){const t=tr(a,o),i=typeof t=="string"?ir(t):t,s=Qn(e),n=Wn(i,{decode:decodeURIComponent})(s),r=n&&n.params||{};return{matches:n!==!1,params:r}}var nr=Object.create,vt=Object.defineProperty,rr=Object.getOwnPropertyDescriptor,wt=Object.getOwnPropertyNames,ur=Object.getPrototypeOf,cr=Object.prototype.hasOwnProperty,lr=(e,a)=>function(){return a||(0,e[wt(e)[0]])((a={exports:{}}).exports,a),a.exports},mr=(e,a,o,t)=>{if(a&&typeof a=="object"||typeof a=="function")for(let i of wt(a))!cr.call(e,i)&&i!==o&&vt(e,i,{get:()=>a[i],enumerable:!(t=rr(a,i))||t.enumerable});return e},pr=(e,a,o)=>(o=e!=null?nr(ur(e)):{},mr(vt(o,"default",{value:e,enumerable:!0}),e)),hr=lr({"node_modules/cookie/index.js"(e){e.parse=r,e.serialize=p;var a=Object.prototype.toString,o=Object.prototype.hasOwnProperty,t=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/;function r(y,z){if(typeof y!="string")throw new TypeError("argument str must be a string");var C={},A=y.length;if(A<2)return C;var q=z&&z.decode||m,R=0,g=0,j=0;do{if(g=y.indexOf("=",R),g===-1)break;if(j=y.indexOf(";",R),j===-1)j=A;else if(g>j){R=y.lastIndexOf(";",g-1)+1;continue}var E=c(y,R,g),w=h(y,g,E),T=y.slice(E,w);if(!o.call(C,T)){var S=c(y,g+1,j),_=h(y,j,S);y.charCodeAt(S)===34&&y.charCodeAt(_-1)===34&&(S++,_--);var I=y.slice(S,_);C[T]=b(I,q)}R=j+1}while(R<A);return C}function c(y,z,C){do{var A=y.charCodeAt(z);if(A!==32&&A!==9)return z}while(++z<C);return C}function h(y,z,C){for(;z>C;){var A=y.charCodeAt(--z);if(A!==32&&A!==9)return z+1}return C}function p(y,z,C){var A=C&&C.encode||encodeURIComponent;if(typeof A!="function")throw new TypeError("option encode is invalid");if(!t.test(y))throw new TypeError("argument name is invalid");var q=A(z);if(!i.test(q))throw new TypeError("argument val is invalid");var R=y+"="+q;if(!C)return R;if(C.maxAge!=null){var g=Math.floor(C.maxAge);if(!isFinite(g))throw new TypeError("option maxAge is invalid");R+="; Max-Age="+g}if(C.domain){if(!s.test(C.domain))throw new TypeError("option domain is invalid");R+="; Domain="+C.domain}if(C.path){if(!n.test(C.path))throw new TypeError("option path is invalid");R+="; Path="+C.path}if(C.expires){var j=C.expires;if(!v(j)||isNaN(j.valueOf()))throw new TypeError("option expires is invalid");R+="; Expires="+j.toUTCString()}if(C.httpOnly&&(R+="; HttpOnly"),C.secure&&(R+="; Secure"),C.partitioned&&(R+="; Partitioned"),C.priority){var E=typeof C.priority=="string"?C.priority.toLowerCase():C.priority;switch(E){case"low":R+="; Priority=Low";break;case"medium":R+="; Priority=Medium";break;case"high":R+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(C.sameSite){var w=typeof C.sameSite=="string"?C.sameSite.toLowerCase():C.sameSite;switch(w){case!0:R+="; SameSite=Strict";break;case"lax":R+="; SameSite=Lax";break;case"strict":R+="; SameSite=Strict";break;case"none":R+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return R}function m(y){return y.indexOf("%")!==-1?decodeURIComponent(y):y}function v(y){return a.call(y)==="[object Date]"}function b(y,z){try{return z(y)}catch{return y}}}}),dr=pr(hr()),xt=dr.default;/*! Bundled license information:

cookie/index.js:
  (*!
   * cookie
   * Copyright(c) 2012-2014 Roman Shtylman
   * Copyright(c) 2015 Douglas Christopher Wilson
   * MIT Licensed
   *)
*/function Et(e){const a=xt.parse(e),o={};for(const t in a)typeof a[t]<"u"&&(o[t]=a[t]);return o}function wo(){return Et(document.cookie)}function gr(e){if(typeof document>"u"||typeof location>"u")return{};switch(e.credentials){case"same-origin":{const a=new URL(e.url);return location.origin===a.origin?wo():{}}case"include":return wo();default:return{}}}function fr(e){const a=e.headers.get("cookie"),o=a?Et(a):{},t=gr(e);for(const n in t)e.headers.append("cookie",xt.serialize(n,t[n]));const i=Fo.getCookiesSync(e.url),s=Object.fromEntries(i.map(n=>[n.key,n.value]));for(const n of i)e.headers.append("cookie",n.toString());return{...t,...s,...o}}var ze=(e=>(e.HEAD="HEAD",e.GET="GET",e.POST="POST",e.PUT="PUT",e.PATCH="PATCH",e.OPTIONS="OPTIONS",e.DELETE="DELETE",e))(ze||{});class kr extends ga{constructor(a,o,t,i){super({info:{header:`${a} ${o}`,path:o,method:a},resolver:t,options:i}),this.checkRedundantQueryParameters()}checkRedundantQueryParameters(){const{method:a,path:o}=this.info;if(o instanceof RegExp||jt(o)===o)return;er(o).forEach((s,n)=>{}),U.warn(`Found a redundant usage of query parameters in the request handler URL for "${a} ${o}". Please match against a path instead and access query parameters using "new URL(request.url).searchParams" instead. Learn more: https://mswjs.io/docs/http/intercepting-requestsquery-parameters`)}async parse(a){var s;const o=new URL(a.request.url),t=sr(o,this.info.path,(s=a.resolutionContext)==null?void 0:s.baseUrl),i=fr(a.request);return{match:t,cookies:i}}predicate(a){const o=this.matchMethod(a.request.method),t=a.parsedResult.match.matches;return o&&t}matchMethod(a){return this.info.method instanceof RegExp?this.info.method.test(a):Fn(this.info.method,a)}extendResolverArgs(a){var o;return{params:((o=a.parsedResult.match)==null?void 0:o.params)||{},cookies:a.parsedResult.cookies}}async log(a){const o=ta(a.request.url),t=await Bn(a.request),i=await Un(a.response),s=Mn(i.status);console.groupCollapsed(U.formatMessage(`${_e()} ${a.request.method} ${o} (%c${i.status} ${i.statusText}%c)`),`color:${s}`,"color:inherit"),console.log("Request",t),console.log("Handler:",this),console.log("Response",i),console.groupEnd()}}function Ee(e){return(a,o,t={})=>new kr(e,a,o,t)}const te={all:Ee(/.+/),head:Ee(ze.HEAD),get:Ee(ze.GET),post:Ee(ze.POST),put:Ee(ze.PUT),delete:Ee(ze.DELETE),patch:Ee(ze.PATCH),options:Ee(ze.OPTIONS)},br=Symbol("bodyType");var Co,zo;class F extends(zo=Ya,Co=br,zo){constructor(o,t){const i=Te(t);super(o,i);M(this,Co,null);qi(this,i)}static error(){return super.error()}static text(o,t){const i=Te(t);return i.headers.has("Content-Type")||i.headers.set("Content-Type","text/plain"),i.headers.has("Content-Length")||i.headers.set("Content-Length",o?new Blob([o]).size.toString():"0"),new F(o,i)}static json(o,t){const i=Te(t);i.headers.has("Content-Type")||i.headers.set("Content-Type","application/json");const s=JSON.stringify(o);return i.headers.has("Content-Length")||i.headers.set("Content-Length",s?new Blob([s]).size.toString():"0"),new F(s,i)}static xml(o,t){const i=Te(t);return i.headers.has("Content-Type")||i.headers.set("Content-Type","text/xml"),new F(o,i)}static html(o,t){const i=Te(t);return i.headers.has("Content-Type")||i.headers.set("Content-Type","text/html"),new F(o,i)}static arrayBuffer(o,t){const i=Te(t);return i.headers.has("Content-Type")||i.headers.set("Content-Type","application/octet-stream"),o&&!i.headers.has("Content-Length")&&i.headers.set("Content-Length",o.byteLength.toString()),new F(o,i)}static formData(o,t){return new F(o,Te(t))}}qn();const yr=()=>{const e=[],a=new Date;for(let o=0;o<7;o++){const t=new Date(a);t.setDate(a.getDate()+o);const i=t.toISOString().split("T")[0];["14:00","16:00","18:00","20:00"].forEach((n,r)=>{const c=`${parseInt(n.split(":")[0])+2}:00`;e.push({id:o*10+r+1,date:i,startTime:n,endTime:c,availableSpots:Math.floor(Math.random()*6)+2,maxSpots:8})})}return e},Oa=yr(),Ce=[{id:1,name:"Le Manoir Hanté",description:"Explorez un manoir ancien où hantent des esprits et secrets terrifiants. Préparez-vous à résoudre des énigmes dans un décor gothique.",theme:"Gothique",duration:60,price:25,minParticipants:3,maxParticipants:8,availableSlots:Oa.filter(e=>e.id%3===1)},{id:2,name:"L'Asile Abandonné",description:"Echappez à un asile psychiatrique désaffecté, entre mystères sombres et présences inquiétantes. Sensations fortes garanties.",theme:"Psychologique",duration:75,price:30,minParticipants:2,maxParticipants:6,availableSlots:Oa.filter(e=>e.id%3===2)},{id:3,name:"La Crypte Maudite",description:"Descendez dans une crypte ancestrale remplie de pièges et de malédictions. Chaque pas peut être le dernier, saurez-vous sortir vivant ?",theme:"Mystique",duration:90,price:35,minParticipants:4,maxParticipants:10,availableSlots:Oa.filter(e=>e.id%3===0)}];let Pe=[];const ge=[{id:1,password:"alice",firstName:"Alice",lastName:"Durand",email:"<EMAIL>",role:"Manager",hireDate:"2022-03-15"},{id:2,password:"lucas",firstName:"Lucas",lastName:"Bernard",email:"<EMAIL>",role:"Game Master",hireDate:"2023-06-01"},{id:3,password:"sophie",firstName:"Sophie",lastName:"Martin",email:"<EMAIL>",role:"Receptioniste",hireDate:"2021-11-12"},{id:4,password:"antoine",firstName:"Antoine",lastName:"Leclerc",email:"<EMAIL>",role:"Technicien",hireDate:"2020-08-24"},{id:5,password:"julie",firstName:"Julie",lastName:"Moreau",email:"<EMAIL>",role:"Game Master",hireDate:"2023-01-10"},{id:6,password:"stéphane",firstName:"Stéphane",lastName:"Debré",email:"<EMAIL>",role:"Technicien",hireDate:"2024-01-16"},{id:7,password:"gecko",firstName:"Gecko",lastName:"Moria",email:"<EMAIL>",role:"Receptioniste",hireDate:"2025-02-22"}],jr=[te.get("https://maison.hor/rooms",({request:e})=>{const a=new URL(e.url),o=parseInt(a.searchParams.get("page")??"0",10),t=parseInt(a.searchParams.get("limit")??"10",10),i=o*t,s=Ce.slice(i,i+t);return console.log(o,t),F.json({data:s,page:o,total:Ce.length,sub_total:s.length})}),te.get("https://maison.hor/rooms/:id",({params:e})=>{const a=parseInt(e.id),o=Ce.find(t=>t.id===a);return o?F.json(o):new F(null,{status:404})}),te.get("https://maison.hor/rooms/:id/slots",({params:e})=>{const a=parseInt(e.id),o=Ce.find(t=>t.id===a);return o?F.json(o.availableSlots||[]):new F(null,{status:404})}),te.delete("https://maison.hor/rooms/:id",({params:e})=>{const a=Ce.findIndex(o=>o.id===parseInt(e.id,10));return a===-1?F.json({error:"Session non trouvée"},{status:404}):(Ce.splice(a,1),F.json({message:"Element deleted."}))}),te.patch("https://maison.hor/rooms/:id",async({params:e,request:a})=>{const o=await a.json(),t=Ce.find(i=>i.id===parseInt(e.id,10));return t===void 0?F.json({error:"Session non trouvée"},{status:404}):(o.name&&(t.name=o.name),o.description&&(t.description=o.description),o.theme&&(t.theme=o.theme),o.duration&&(t.duration=o.duration),o.price&&(t.price=o.price),o.minParticipants&&(t.minParticipants=o.minParticipants),o.maxParticipants&&(t.maxParticipants=o.maxParticipants),F.json(t))}),te.post("https://maison.hor/bookings",async({request:e})=>{var s;const a=await e.json(),o=Ce.find(n=>n.id===a.roomId);if(!o)return F.json({error:"Session non trouvée"},{status:404});const t=(s=o.availableSlots)==null?void 0:s.find(n=>n.id===a.slotId);if(!t)return F.json({error:"Créneau non trouvé"},{status:404});if(a.participantCount<(o.minParticipants||1))return F.json({error:`Minimum ${o.minParticipants} participants requis`},{status:400});if(a.participantCount>t.availableSpots)return F.json({error:`Seulement ${t.availableSpots} places disponibles`},{status:400});const i={id:Pe.length+1,roomId:a.roomId,slotId:a.slotId,customerEmail:a.customerEmail,participantCount:a.participantCount,createdAt:new Date().toISOString(),status:"confirmed"};return Pe.push(i),t.availableSpots-=a.participantCount,F.json(i,{status:201})}),te.get("https://maison.hor/bookings",({request:e})=>{const a=new URL(e.url),o=parseInt(a.searchParams.get("page")??"0",10),t=parseInt(a.searchParams.get("limit")??"10",10),i=o*t,s=Pe.slice(i,i+t);return F.json({data:s,page:o,total:Pe.length,sub_total:s.length})}),te.delete("https://maison.hor/bookings/:id",({params:e})=>{const a=Pe.findIndex(o=>o.id===parseInt(e.id,10));return a===-1?F.json({error:"Réservation non trouvée"},{status:404}):(Pe.splice(a,1),F.json({message:"Element deleted."}))}),te.patch("https://maison.hor/bookings/:id",async({params:e,request:a})=>{const o=await a.json(),t=Pe.find(i=>i.id===parseInt(e.id,10));return t===void 0?F.json({error:"Réservation non trouvée"},{status:404}):(o.roomId&&(t.roomId=o.roomId),o.slotId&&(t.slotId=o.slotId),o.customerEmail&&(t.customerEmail=o.customerEmail),o.status&&(t.status=o.status),F.json(t))}),te.get("https://maison.hor/users",({request:e})=>{const a=new URL(e.url),o=parseInt(a.searchParams.get("page")??"0",10),t=parseInt(a.searchParams.get("limit")??"10",10),i=o*t,s=ge.slice(i,i+t).map(({password:n,...r})=>r);return F.json({data:s,page:o,total:ge.length,sub_total:s.length})}),te.get("https://maison.hor/users/:id",({params:e})=>{const a=ge.find(i=>i.id===parseInt(e.id,10));if(a===void 0)return F.json({error:"Utilisateur non trouvé"},{status:404});const{password:o,...t}=a;return F.json(t)}),te.get("https://maison.hor/auth/login",async({request:e})=>{const a=await e.json(),o=ge.find(s=>s.email===a.email&&s.password===a.password);if(!o)return F.json({error:"Utilisateur non trouvé"},{status:404});const{password:t,...i}=o;return F.json(i)}),te.post("https://maison.hor/users/",async({request:e})=>{const a=await e.json(),o={id:ge[ge.length-1].id+1,...a,hireDate:new Date().toISOString()};ge.push(o);const{password:t,...i}=o;return F.json(i)}),te.patch("https://maison.hor/users/:id",async({params:e,request:a})=>{const o=await a.json(),t=ge.find(n=>n.id===parseInt(e.id,10));if(t===void 0)return F.json({error:"Utilisateur non trouvé"},{status:404});o.email!==void 0&&(t.email=o.email),o.role!==void 0&&(t.role=o.role);const{password:i,...s}=t;return F.json(s)}),te.delete("https://maison.hor/users/:id",({params:e})=>{const a=ge.findIndex(o=>o.id===parseInt(e.id,10));return a===-1?F.json({error:"Utilisateur non trouvé"},{status:404}):(ge.splice(a,1),F.json({message:"Element deleted."}))})],Rr=On(...jr);export{Rr as worker};
