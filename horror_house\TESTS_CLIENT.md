# 🧪 Guide de test - Partie Client

## 🚀 Démarrage
```bash
cd horror_house
npm install
npm run dev
```
Ouvrir http://localhost:5173/

## ✅ Tests fonctionnels

### 1. **Affichage des sessions** (`/sessions`)
- [ ] Les 3 sessions s'affichent avec cartes enrichies
- [ ] Chaque carte montre : thème, durée, prix, participants min/max
- [ ] Places disponibles et prochaine date affichées
- [ ] Bouton "Réserver cette session" présent
- [ ] Spinner de chargement au début
- [ ] Bouton "Réessayer" si erreur réseau

### 2. **Formulaire de réservation** (`/reservation`)
- [ ] Sélection de session avec prix/durée
- [ ] Sélection de créneaux avec dates/heures
- [ ] Champ email avec validation
- [ ] Champ nombre de participants avec validation min/max
- [ ] Messages d'erreur contextuels
- [ ] Bouton désactivé pendant soumission
- [ ] Confirmation avec détails de réservation

### 3. **Pré-remplissage depuis Sessions**
- [ ] <PERSON><PERSON><PERSON> "Réserver" sur une session
- [ ] Redirection vers `/reservation?roomId=X`
- [ ] Session pré-sélectionnée dans le formulaire
- [ ] Informations de session affichées

### 4. **Thème sombre persistant**
- [ ] Thème sombre par défaut au premier chargement
- [ ] Toggle thème dans la navbar fonctionne
- [ ] Couleurs horrifiques (rouge sang, noir profond)
- [ ] Refresh de page conserve le thème choisi
- [ ] localStorage contient `horror-house-theme`

### 5. **Gestion d'erreurs**
- [ ] Messages d'erreur différenciés par type (validation, réseau, serveur)
- [ ] Boutons de fermeture des erreurs
- [ ] États de chargement avec spinners
- [ ] Boutons "Réessayer" fonctionnels
- [ ] ErrorBoundary capture les erreurs React

## 🔧 Tests techniques

### Validation formulaire
- [ ] Email invalide → erreur validation
- [ ] Participants < minimum → erreur validation  
- [ ] Participants > places disponibles → erreur validation
- [ ] Champs vides → erreurs appropriées

### API et données
- [ ] `/client/rooms` retourne sessions enrichies
- [ ] `/client/bookings` POST crée réservation
- [ ] Places disponibles mises à jour après réservation
- [ ] Gestion erreurs 400/404/500

### Persistance
- [ ] localStorage sauvegarde thème
- [ ] Restauration thème au refresh
- [ ] Gestion erreurs localStorage

## 🐛 Tests d'erreurs

### Simuler erreurs réseau
1. Ouvrir DevTools → Network
2. Cocher "Offline" 
3. Recharger `/sessions`
4. Vérifier message d'erreur réseau
5. Décocher "Offline" et cliquer "Réessayer"

### Simuler erreur serveur
1. Modifier temporairement l'URL API dans `const.ts`
2. Tenter une réservation
3. Vérifier message d'erreur serveur

## 📱 Tests responsive
- [ ] Mobile : cartes sessions empilées
- [ ] Tablet : formulaire centré
- [ ] Desktop : layout optimal

## ⚡ Tests performance
- [ ] Chargement initial < 2s
- [ ] Navigation fluide entre pages
- [ ] Pas de re-renders inutiles
- [ ] Images optimisées

## 🔒 Tests sécurité
- [ ] Validation côté client ET serveur
- [ ] Pas d'injection XSS possible
- [ ] Données sensibles non exposées
- [ ] HTTPS en production

## 📊 Métriques de succès
- ✅ Toutes les fonctionnalités client implémentées
- ✅ UX fluide et intuitive  
- ✅ Gestion d'erreurs robuste
- ✅ Thème horrifique persistant
- ✅ Code propre et maintenable
- ✅ Compatible avec partie employé
