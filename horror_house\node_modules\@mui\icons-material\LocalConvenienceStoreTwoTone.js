"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M14 14h2v4h4V9h-3V6H7v3H4v9h4v-4zm-1-7h1v2h1V7h1v5h-1v-2h-2zM8 9h2V8H8V7h3v3H9v1h2v1H8z",
  opacity: ".3"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M10 16h4v4h8V7h-3V4H5v3H2v13h8zm-2 0v2H4V9h3V6h10v3h3v9h-4v-4H8zm3-5H9v-1h2V7H8v1h2v1H8v3h3zm4 1h1V7h-1v2h-1V7h-1v3h2z"
}, "1")], 'LocalConvenienceStoreTwoTone');