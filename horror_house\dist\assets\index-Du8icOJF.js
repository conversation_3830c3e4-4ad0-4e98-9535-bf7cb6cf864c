var Yv=Object.defineProperty;var Gv=(a,r,o)=>r in a?Yv(a,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[r]=o;var $r=(a,r,o)=>Gv(a,typeof r!="symbol"?r+"":r,o);function Vv(a,r){for(var o=0;o<r.length;o++){const u=r[o];if(typeof u!="string"&&!Array.isArray(u)){for(const s in u)if(s!=="default"&&!(s in a)){const f=Object.getOwnPropertyDescriptor(u,s);f&&Object.defineProperty(a,s,f.get?f:{enumerable:!0,get:()=>u[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))u(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&u(d)}).observe(document,{childList:!0,subtree:!0});function o(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function u(s){if(s.ep)return;s.ep=!0;const f=o(s);fetch(s.href,f)}})();const Xv="modulepreload",Qv=function(a){return"/"+a},gp={},Zv=function(r,o,u){let s=Promise.resolve();if(o&&o.length>0){let d=function(m){return Promise.all(m.map(v=>Promise.resolve(v).then(S=>({status:"fulfilled",value:S}),S=>({status:"rejected",reason:S}))))};document.getElementsByTagName("link");const p=document.querySelector("meta[property=csp-nonce]"),g=(p==null?void 0:p.nonce)||(p==null?void 0:p.getAttribute("nonce"));s=d(o.map(m=>{if(m=Qv(m),m in gp)return;gp[m]=!0;const v=m.endsWith(".css"),S=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${S}`))return;const T=document.createElement("link");if(T.rel=v?"stylesheet":Xv,v||(T.as="script"),T.crossOrigin="",T.href=m,g&&T.setAttribute("nonce",g),document.head.appendChild(T),v)return new Promise((N,O)=>{T.addEventListener("load",N),T.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${m}`)))})}))}function f(d){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=d,window.dispatchEvent(p),!p.defaultPrevented)throw d}return s.then(d=>{for(const p of d||[])p.status==="rejected"&&f(p.reason);return r().catch(f)})};function Kv(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var gs={exports:{}},Yr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp;function Jv(){if(yp)return Yr;yp=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(u,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var p in s)p!=="key"&&(f[p]=s[p])}else f=s;return s=f.ref,{$$typeof:a,type:u,key:d,ref:s!==void 0?s:null,props:f}}return Yr.Fragment=r,Yr.jsx=o,Yr.jsxs=o,Yr}var vp;function Wv(){return vp||(vp=1,gs.exports=Jv()),gs.exports}var E=Wv(),ys={exports:{}},ge={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bp;function Fv(){if(bp)return ge;bp=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),S=Symbol.iterator;function T(_){return _===null||typeof _!="object"?null:(_=S&&_[S]||_["@@iterator"],typeof _=="function"?_:null)}var N={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},O=Object.assign,C={};function B(_,X,J){this.props=_,this.context=X,this.refs=C,this.updater=J||N}B.prototype.isReactComponent={},B.prototype.setState=function(_,X){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,X,"setState")},B.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function k(){}k.prototype=B.prototype;function V(_,X,J){this.props=_,this.context=X,this.refs=C,this.updater=J||N}var L=V.prototype=new k;L.constructor=V,O(L,B.prototype),L.isPureReactComponent=!0;var q=Array.isArray,z={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function P(_,X,J,F,ie,le){return J=le.ref,{$$typeof:a,type:_,key:X,ref:J!==void 0?J:null,props:le}}function ee(_,X){return P(_.type,X,void 0,void 0,void 0,_.props)}function te(_){return typeof _=="object"&&_!==null&&_.$$typeof===a}function b(_){var X={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(J){return X[J]})}var Z=/\/+/g;function I(_,X){return typeof _=="object"&&_!==null&&_.key!=null?b(""+_.key):X.toString(36)}function se(){}function Ee(_){switch(_.status){case"fulfilled":return _.value;case"rejected":throw _.reason;default:switch(typeof _.status=="string"?_.then(se,se):(_.status="pending",_.then(function(X){_.status==="pending"&&(_.status="fulfilled",_.value=X)},function(X){_.status==="pending"&&(_.status="rejected",_.reason=X)})),_.status){case"fulfilled":return _.value;case"rejected":throw _.reason}}throw _}function pe(_,X,J,F,ie){var le=typeof _;(le==="undefined"||le==="boolean")&&(_=null);var re=!1;if(_===null)re=!0;else switch(le){case"bigint":case"string":case"number":re=!0;break;case"object":switch(_.$$typeof){case a:case r:re=!0;break;case v:return re=_._init,pe(re(_._payload),X,J,F,ie)}}if(re)return ie=ie(_),re=F===""?"."+I(_,0):F,q(ie)?(J="",re!=null&&(J=re.replace(Z,"$&/")+"/"),pe(ie,X,J,"",function(Dt){return Dt})):ie!=null&&(te(ie)&&(ie=ee(ie,J+(ie.key==null||_&&_.key===ie.key?"":(""+ie.key).replace(Z,"$&/")+"/")+re)),X.push(ie)),1;re=0;var Ce=F===""?".":F+":";if(q(_))for(var Me=0;Me<_.length;Me++)F=_[Me],le=Ce+I(F,Me),re+=pe(F,X,J,le,ie);else if(Me=T(_),typeof Me=="function")for(_=Me.call(_),Me=0;!(F=_.next()).done;)F=F.value,le=Ce+I(F,Me++),re+=pe(F,X,J,le,ie);else if(le==="object"){if(typeof _.then=="function")return pe(Ee(_),X,J,F,ie);throw X=String(_),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return re}function H(_,X,J){if(_==null)return _;var F=[],ie=0;return pe(_,F,"","",function(le){return X.call(J,le,ie++)}),F}function K(_){if(_._status===-1){var X=_._result;X=X(),X.then(function(J){(_._status===0||_._status===-1)&&(_._status=1,_._result=J)},function(J){(_._status===0||_._status===-1)&&(_._status=2,_._result=J)}),_._status===-1&&(_._status=0,_._result=X)}if(_._status===1)return _._result.default;throw _._result}var ne=typeof reportError=="function"?reportError:function(_){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof _=="object"&&_!==null&&typeof _.message=="string"?String(_.message):String(_),error:_});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",_);return}console.error(_)};function ce(){}return ge.Children={map:H,forEach:function(_,X,J){H(_,function(){X.apply(this,arguments)},J)},count:function(_){var X=0;return H(_,function(){X++}),X},toArray:function(_){return H(_,function(X){return X})||[]},only:function(_){if(!te(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},ge.Component=B,ge.Fragment=o,ge.Profiler=s,ge.PureComponent=V,ge.StrictMode=u,ge.Suspense=g,ge.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=z,ge.__COMPILER_RUNTIME={__proto__:null,c:function(_){return z.H.useMemoCache(_)}},ge.cache=function(_){return function(){return _.apply(null,arguments)}},ge.cloneElement=function(_,X,J){if(_==null)throw Error("The argument must be a React element, but you passed "+_+".");var F=O({},_.props),ie=_.key,le=void 0;if(X!=null)for(re in X.ref!==void 0&&(le=void 0),X.key!==void 0&&(ie=""+X.key),X)!W.call(X,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&X.ref===void 0||(F[re]=X[re]);var re=arguments.length-2;if(re===1)F.children=J;else if(1<re){for(var Ce=Array(re),Me=0;Me<re;Me++)Ce[Me]=arguments[Me+2];F.children=Ce}return P(_.type,ie,void 0,void 0,le,F)},ge.createContext=function(_){return _={$$typeof:d,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null},_.Provider=_,_.Consumer={$$typeof:f,_context:_},_},ge.createElement=function(_,X,J){var F,ie={},le=null;if(X!=null)for(F in X.key!==void 0&&(le=""+X.key),X)W.call(X,F)&&F!=="key"&&F!=="__self"&&F!=="__source"&&(ie[F]=X[F]);var re=arguments.length-2;if(re===1)ie.children=J;else if(1<re){for(var Ce=Array(re),Me=0;Me<re;Me++)Ce[Me]=arguments[Me+2];ie.children=Ce}if(_&&_.defaultProps)for(F in re=_.defaultProps,re)ie[F]===void 0&&(ie[F]=re[F]);return P(_,le,void 0,void 0,null,ie)},ge.createRef=function(){return{current:null}},ge.forwardRef=function(_){return{$$typeof:p,render:_}},ge.isValidElement=te,ge.lazy=function(_){return{$$typeof:v,_payload:{_status:-1,_result:_},_init:K}},ge.memo=function(_,X){return{$$typeof:m,type:_,compare:X===void 0?null:X}},ge.startTransition=function(_){var X=z.T,J={};z.T=J;try{var F=_(),ie=z.S;ie!==null&&ie(J,F),typeof F=="object"&&F!==null&&typeof F.then=="function"&&F.then(ce,ne)}catch(le){ne(le)}finally{z.T=X}},ge.unstable_useCacheRefresh=function(){return z.H.useCacheRefresh()},ge.use=function(_){return z.H.use(_)},ge.useActionState=function(_,X,J){return z.H.useActionState(_,X,J)},ge.useCallback=function(_,X){return z.H.useCallback(_,X)},ge.useContext=function(_){return z.H.useContext(_)},ge.useDebugValue=function(){},ge.useDeferredValue=function(_,X){return z.H.useDeferredValue(_,X)},ge.useEffect=function(_,X,J){var F=z.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return F.useEffect(_,X)},ge.useId=function(){return z.H.useId()},ge.useImperativeHandle=function(_,X,J){return z.H.useImperativeHandle(_,X,J)},ge.useInsertionEffect=function(_,X){return z.H.useInsertionEffect(_,X)},ge.useLayoutEffect=function(_,X){return z.H.useLayoutEffect(_,X)},ge.useMemo=function(_,X){return z.H.useMemo(_,X)},ge.useOptimistic=function(_,X){return z.H.useOptimistic(_,X)},ge.useReducer=function(_,X,J){return z.H.useReducer(_,X,J)},ge.useRef=function(_){return z.H.useRef(_)},ge.useState=function(_){return z.H.useState(_)},ge.useSyncExternalStore=function(_,X,J){return z.H.useSyncExternalStore(_,X,J)},ge.useTransition=function(){return z.H.useTransition()},ge.version="19.1.0",ge}var Sp;function Ps(){return Sp||(Sp=1,ys.exports=Fv()),ys.exports}var A=Ps();const Bl=Kv(A),Hu=Vv({__proto__:null,default:Bl},[A]);var vs={exports:{}},Gr={},bs={exports:{}},Ss={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xp;function Pv(){return xp||(xp=1,function(a){function r(H,K){var ne=H.length;H.push(K);e:for(;0<ne;){var ce=ne-1>>>1,_=H[ce];if(0<s(_,K))H[ce]=K,H[ne]=_,ne=ce;else break e}}function o(H){return H.length===0?null:H[0]}function u(H){if(H.length===0)return null;var K=H[0],ne=H.pop();if(ne!==K){H[0]=ne;e:for(var ce=0,_=H.length,X=_>>>1;ce<X;){var J=2*(ce+1)-1,F=H[J],ie=J+1,le=H[ie];if(0>s(F,ne))ie<_&&0>s(le,F)?(H[ce]=le,H[ie]=ne,ce=ie):(H[ce]=F,H[J]=ne,ce=J);else if(ie<_&&0>s(le,ne))H[ce]=le,H[ie]=ne,ce=ie;else break e}}return K}function s(H,K){var ne=H.sortIndex-K.sortIndex;return ne!==0?ne:H.id-K.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,p=d.now();a.unstable_now=function(){return d.now()-p}}var g=[],m=[],v=1,S=null,T=3,N=!1,O=!1,C=!1,B=!1,k=typeof setTimeout=="function"?setTimeout:null,V=typeof clearTimeout=="function"?clearTimeout:null,L=typeof setImmediate<"u"?setImmediate:null;function q(H){for(var K=o(m);K!==null;){if(K.callback===null)u(m);else if(K.startTime<=H)u(m),K.sortIndex=K.expirationTime,r(g,K);else break;K=o(m)}}function z(H){if(C=!1,q(H),!O)if(o(g)!==null)O=!0,W||(W=!0,I());else{var K=o(m);K!==null&&pe(z,K.startTime-H)}}var W=!1,P=-1,ee=5,te=-1;function b(){return B?!0:!(a.unstable_now()-te<ee)}function Z(){if(B=!1,W){var H=a.unstable_now();te=H;var K=!0;try{e:{O=!1,C&&(C=!1,V(P),P=-1),N=!0;var ne=T;try{t:{for(q(H),S=o(g);S!==null&&!(S.expirationTime>H&&b());){var ce=S.callback;if(typeof ce=="function"){S.callback=null,T=S.priorityLevel;var _=ce(S.expirationTime<=H);if(H=a.unstable_now(),typeof _=="function"){S.callback=_,q(H),K=!0;break t}S===o(g)&&u(g),q(H)}else u(g);S=o(g)}if(S!==null)K=!0;else{var X=o(m);X!==null&&pe(z,X.startTime-H),K=!1}}break e}finally{S=null,T=ne,N=!1}K=void 0}}finally{K?I():W=!1}}}var I;if(typeof L=="function")I=function(){L(Z)};else if(typeof MessageChannel<"u"){var se=new MessageChannel,Ee=se.port2;se.port1.onmessage=Z,I=function(){Ee.postMessage(null)}}else I=function(){k(Z,0)};function pe(H,K){P=k(function(){H(a.unstable_now())},K)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(H){H.callback=null},a.unstable_forceFrameRate=function(H){0>H||125<H?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ee=0<H?Math.floor(1e3/H):5},a.unstable_getCurrentPriorityLevel=function(){return T},a.unstable_next=function(H){switch(T){case 1:case 2:case 3:var K=3;break;default:K=T}var ne=T;T=K;try{return H()}finally{T=ne}},a.unstable_requestPaint=function(){B=!0},a.unstable_runWithPriority=function(H,K){switch(H){case 1:case 2:case 3:case 4:case 5:break;default:H=3}var ne=T;T=H;try{return K()}finally{T=ne}},a.unstable_scheduleCallback=function(H,K,ne){var ce=a.unstable_now();switch(typeof ne=="object"&&ne!==null?(ne=ne.delay,ne=typeof ne=="number"&&0<ne?ce+ne:ce):ne=ce,H){case 1:var _=-1;break;case 2:_=250;break;case 5:_=1073741823;break;case 4:_=1e4;break;default:_=5e3}return _=ne+_,H={id:v++,callback:K,priorityLevel:H,startTime:ne,expirationTime:_,sortIndex:-1},ne>ce?(H.sortIndex=ne,r(m,H),o(g)===null&&H===o(m)&&(C?(V(P),P=-1):C=!0,pe(z,ne-ce))):(H.sortIndex=_,r(g,H),O||N||(O=!0,W||(W=!0,I()))),H},a.unstable_shouldYield=b,a.unstable_wrapCallback=function(H){var K=T;return function(){var ne=T;T=K;try{return H.apply(this,arguments)}finally{T=ne}}}}(Ss)),Ss}var Ep;function Iv(){return Ep||(Ep=1,bs.exports=Pv()),bs.exports}var xs={exports:{}},bt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tp;function e1(){if(Tp)return bt;Tp=1;var a=Ps();function r(g){var m="https://react.dev/errors/"+g;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)m+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+g+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var u={d:{f:o,r:function(){throw Error(r(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(g,m,v){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:S==null?null:""+S,children:g,containerInfo:m,implementation:v}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(g,m){if(g==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return bt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,bt.createPortal=function(g,m){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(r(299));return f(g,m,null,v)},bt.flushSync=function(g){var m=d.T,v=u.p;try{if(d.T=null,u.p=2,g)return g()}finally{d.T=m,u.p=v,u.d.f()}},bt.preconnect=function(g,m){typeof g=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,u.d.C(g,m))},bt.prefetchDNS=function(g){typeof g=="string"&&u.d.D(g)},bt.preinit=function(g,m){if(typeof g=="string"&&m&&typeof m.as=="string"){var v=m.as,S=p(v,m.crossOrigin),T=typeof m.integrity=="string"?m.integrity:void 0,N=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;v==="style"?u.d.S(g,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:T,fetchPriority:N}):v==="script"&&u.d.X(g,{crossOrigin:S,integrity:T,fetchPriority:N,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},bt.preinitModule=function(g,m){if(typeof g=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var v=p(m.as,m.crossOrigin);u.d.M(g,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&u.d.M(g)},bt.preload=function(g,m){if(typeof g=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var v=m.as,S=p(v,m.crossOrigin);u.d.L(g,v,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},bt.preloadModule=function(g,m){if(typeof g=="string")if(m){var v=p(m.as,m.crossOrigin);u.d.m(g,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else u.d.m(g)},bt.requestFormReset=function(g){u.d.r(g)},bt.unstable_batchedUpdates=function(g,m){return g(m)},bt.useFormState=function(g,m,v){return d.H.useFormState(g,m,v)},bt.useFormStatus=function(){return d.H.useHostTransitionStatus()},bt.version="19.1.0",bt}var Cp;function S0(){if(Cp)return xs.exports;Cp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),xs.exports=e1(),xs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _p;function t1(){if(_p)return Gr;_p=1;var a=Iv(),r=Ps(),o=S0();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(f(e)!==e)throw Error(u(188))}function g(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(u(188));return t!==e?null:e}for(var n=e,l=t;;){var i=n.return;if(i===null)break;var c=i.alternate;if(c===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===c.child){for(c=i.child;c;){if(c===n)return p(i),e;if(c===l)return p(i),t;c=c.sibling}throw Error(u(188))}if(n.return!==l.return)n=i,l=c;else{for(var h=!1,y=i.child;y;){if(y===n){h=!0,n=i,l=c;break}if(y===l){h=!0,l=i,n=c;break}y=y.sibling}if(!h){for(y=c.child;y;){if(y===n){h=!0,n=c,l=i;break}if(y===l){h=!0,l=c,n=i;break}y=y.sibling}if(!h)throw Error(u(189))}}if(n.alternate!==l)throw Error(u(190))}if(n.tag!==3)throw Error(u(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,S=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),N=Symbol.for("react.portal"),O=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),k=Symbol.for("react.provider"),V=Symbol.for("react.consumer"),L=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),ee=Symbol.for("react.lazy"),te=Symbol.for("react.activity"),b=Symbol.for("react.memo_cache_sentinel"),Z=Symbol.iterator;function I(e){return e===null||typeof e!="object"?null:(e=Z&&e[Z]||e["@@iterator"],typeof e=="function"?e:null)}var se=Symbol.for("react.client.reference");function Ee(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===se?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case O:return"Fragment";case B:return"Profiler";case C:return"StrictMode";case z:return"Suspense";case W:return"SuspenseList";case te:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case N:return"Portal";case L:return(e.displayName||"Context")+".Provider";case V:return(e._context.displayName||"Context")+".Consumer";case q:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case P:return t=e.displayName||null,t!==null?t:Ee(e.type)||"Memo";case ee:t=e._payload,e=e._init;try{return Ee(e(t))}catch{}}return null}var pe=Array.isArray,H=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ne={pending:!1,data:null,method:null,action:null},ce=[],_=-1;function X(e){return{current:e}}function J(e){0>_||(e.current=ce[_],ce[_]=null,_--)}function F(e,t){_++,ce[_]=e.current,e.current=t}var ie=X(null),le=X(null),re=X(null),Ce=X(null);function Me(e,t){switch(F(re,t),F(le,e),F(ie,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Vm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Vm(t),e=Xm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(ie),F(ie,e)}function Dt(){J(ie),J(le),J(re)}function Sa(e){e.memoizedState!==null&&F(Ce,e);var t=ie.current,n=Xm(t,e.type);t!==n&&(F(le,e),F(ie,n))}function Yn(e){le.current===e&&(J(ie),J(le)),Ce.current===e&&(J(Ce),Ur._currentValue=ne)}var Gn=Object.prototype.hasOwnProperty,Vn=a.unstable_scheduleCallback,on=a.unstable_cancelCallback,Va=a.unstable_shouldYield,Vl=a.unstable_requestPaint,at=a.unstable_now,nn=a.unstable_getCurrentPriorityLevel,dt=a.unstable_ImmediatePriority,cn=a.unstable_UserBlockingPriority,Gt=a.unstable_NormalPriority,me=a.unstable_LowPriority,hi=a.unstable_IdlePriority,mi=a.log,pi=a.unstable_setDisableYieldValue,Xn=null,ht=null;function an(e){if(typeof mi=="function"&&pi(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode(Xn,e)}catch{}}var mt=Math.clz32?Math.clz32:xn,pt=Math.log,Ye=Math.LN2;function xn(e){return e>>>=0,e===0?32:31-(pt(e)/Ye|0)|0}var Et=256,gt=4194304;function En(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function gi(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var i=0,c=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var y=l&134217727;return y!==0?(l=y&~c,l!==0?i=En(l):(h&=y,h!==0?i=En(h):n||(n=y&~e,n!==0&&(i=En(n))))):(y=l&~c,y!==0?i=En(y):h!==0?i=En(h):n||(n=l&~e,n!==0&&(i=En(n)))),i===0?0:t!==0&&t!==i&&(t&c)===0&&(c=i&-i,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:i}function Xl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Mg(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rf(){var e=Et;return Et<<=1,(Et&4194048)===0&&(Et=256),e}function Of(){var e=gt;return gt<<=1,(gt&62914560)===0&&(gt=4194304),e}function lo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ql(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function zg(e,t,n,l,i,c){var h=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var y=e.entanglements,x=e.expirationTimes,w=e.hiddenUpdates;for(n=h&~n;0<n;){var Y=31-mt(n),Q=1<<Y;y[Y]=0,x[Y]=-1;var j=w[Y];if(j!==null)for(w[Y]=null,Y=0;Y<j.length;Y++){var U=j[Y];U!==null&&(U.lane&=-536870913)}n&=~Q}l!==0&&Mf(e,l,0),c!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=c&~(h&~t))}function Mf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-mt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function zf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-mt(n),i=1<<l;i&t|e[l]&t&&(e[l]|=t),n&=~i}}function ro(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function io(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Df(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:sp(e.type))}function Dg(e,t){var n=K.p;try{return K.p=e,t()}finally{K.p=n}}var Qn=Math.random().toString(36).slice(2),yt="__reactFiber$"+Qn,_t="__reactProps$"+Qn,Xa="__reactContainer$"+Qn,uo="__reactEvents$"+Qn,wg="__reactListeners$"+Qn,Ng="__reactHandles$"+Qn,wf="__reactResources$"+Qn,Zl="__reactMarker$"+Qn;function oo(e){delete e[yt],delete e[_t],delete e[uo],delete e[wg],delete e[Ng]}function Qa(e){var t=e[yt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Xa]||n[yt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Jm(e);e!==null;){if(n=e[yt])return n;e=Jm(e)}return t}e=n,n=e.parentNode}return null}function Za(e){if(e=e[yt]||e[Xa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Kl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function Ka(e){var t=e[wf];return t||(t=e[wf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function lt(e){e[Zl]=!0}var Nf=new Set,jf={};function xa(e,t){Ja(e,t),Ja(e+"Capture",t)}function Ja(e,t){for(jf[e]=t,e=0;e<t.length;e++)Nf.add(t[e])}var jg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Bf={},Uf={};function Bg(e){return Gn.call(Uf,e)?!0:Gn.call(Bf,e)?!1:jg.test(e)?Uf[e]=!0:(Bf[e]=!0,!1)}function yi(e,t,n){if(Bg(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function vi(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function Tn(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var co,Hf;function Wa(e){if(co===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);co=t&&t[1]||"",Hf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+co+e+Hf}var so=!1;function fo(e,t){if(!e||so)return"";so=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(U){var j=U}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(U){j=U}e.call(Q.prototype)}}else{try{throw Error()}catch(U){j=U}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(U){if(U&&j&&typeof U.stack=="string")return[U.stack,j.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),h=c[0],y=c[1];if(h&&y){var x=h.split(`
`),w=y.split(`
`);for(i=l=0;l<x.length&&!x[l].includes("DetermineComponentFrameRoot");)l++;for(;i<w.length&&!w[i].includes("DetermineComponentFrameRoot");)i++;if(l===x.length||i===w.length)for(l=x.length-1,i=w.length-1;1<=l&&0<=i&&x[l]!==w[i];)i--;for(;1<=l&&0<=i;l--,i--)if(x[l]!==w[i]){if(l!==1||i!==1)do if(l--,i--,0>i||x[l]!==w[i]){var Y=`
`+x[l].replace(" at new "," at ");return e.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",e.displayName)),Y}while(1<=l&&0<=i);break}}}finally{so=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Wa(n):""}function Ug(e){switch(e.tag){case 26:case 27:case 5:return Wa(e.type);case 16:return Wa("Lazy");case 13:return Wa("Suspense");case 19:return Wa("SuspenseList");case 0:case 15:return fo(e.type,!1);case 11:return fo(e.type.render,!1);case 1:return fo(e.type,!0);case 31:return Wa("Activity");default:return""}}function Lf(e){try{var t="";do t+=Ug(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Vt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function kf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Hg(e){var t=kf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(h){l=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function bi(e){e._valueTracker||(e._valueTracker=Hg(e))}function qf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=kf(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function Si(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Lg=/[\n"\\]/g;function Xt(e){return e.replace(Lg,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function ho(e,t,n,l,i,c,h,y){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Vt(t)):e.value!==""+Vt(t)&&(e.value=""+Vt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?mo(e,h,Vt(t)):n!=null?mo(e,h,Vt(n)):l!=null&&e.removeAttribute("value"),i==null&&c!=null&&(e.defaultChecked=!!c),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+Vt(y):e.removeAttribute("name")}function $f(e,t,n,l,i,c,h,y){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+Vt(n):"",t=t!=null?""+Vt(t):n,y||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=y?e.checked:!!l,e.defaultChecked=!!l,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function mo(e,t,n){t==="number"&&Si(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Fa(e,t,n,l){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&l&&(e[n].defaultSelected=!0)}else{for(n=""+Vt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Yf(e,t,n){if(t!=null&&(t=""+Vt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Vt(n):""}function Gf(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(u(92));if(pe(l)){if(1<l.length)throw Error(u(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=Vt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function Pa(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var kg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Vf(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||kg.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Xf(e,t,n){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&n[i]!==l&&Vf(e,i,l)}else for(var c in t)t.hasOwnProperty(c)&&Vf(e,c,t[c])}function po(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),$g=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function xi(e){return $g.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var go=null;function yo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ia=null,el=null;function Qf(e){var t=Za(e);if(t&&(e=t.stateNode)){var n=e[_t]||null;e:switch(e=t.stateNode,t.type){case"input":if(ho(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Xt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var i=l[_t]||null;if(!i)throw Error(u(90));ho(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&qf(l)}break e;case"textarea":Yf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Fa(e,!!n.multiple,t,!1)}}}var vo=!1;function Zf(e,t,n){if(vo)return e(t,n);vo=!0;try{var l=e(t);return l}finally{if(vo=!1,(Ia!==null||el!==null)&&(iu(),Ia&&(t=Ia,e=el,el=Ia=null,Qf(t),e)))for(t=0;t<e.length;t++)Qf(e[t])}}function Jl(e,t){var n=e.stateNode;if(n===null)return null;var l=n[_t]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(u(231,t,typeof n));return n}var Cn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bo=!1;if(Cn)try{var Wl={};Object.defineProperty(Wl,"passive",{get:function(){bo=!0}}),window.addEventListener("test",Wl,Wl),window.removeEventListener("test",Wl,Wl)}catch{bo=!1}var Zn=null,So=null,Ei=null;function Kf(){if(Ei)return Ei;var e,t=So,n=t.length,l,i="value"in Zn?Zn.value:Zn.textContent,c=i.length;for(e=0;e<n&&t[e]===i[e];e++);var h=n-e;for(l=1;l<=h&&t[n-l]===i[c-l];l++);return Ei=i.slice(e,1<l?1-l:void 0)}function Ti(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ci(){return!0}function Jf(){return!1}function At(e){function t(n,l,i,c,h){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(n=e[y],this[y]=n?n(c):c[y]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Ci:Jf,this.isPropagationStopped=Jf,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ci)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ci)},persist:function(){},isPersistent:Ci}),t}var Ea={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_i=At(Ea),Fl=v({},Ea,{view:0,detail:0}),Yg=At(Fl),xo,Eo,Pl,Ai=v({},Fl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Co,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Pl&&(Pl&&e.type==="mousemove"?(xo=e.screenX-Pl.screenX,Eo=e.screenY-Pl.screenY):Eo=xo=0,Pl=e),xo)},movementY:function(e){return"movementY"in e?e.movementY:Eo}}),Wf=At(Ai),Gg=v({},Ai,{dataTransfer:0}),Vg=At(Gg),Xg=v({},Fl,{relatedTarget:0}),To=At(Xg),Qg=v({},Ea,{animationName:0,elapsedTime:0,pseudoElement:0}),Zg=At(Qg),Kg=v({},Ea,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Jg=At(Kg),Wg=v({},Ea,{data:0}),Ff=At(Wg),Fg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Pg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ig={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ey(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ig[e])?!!t[e]:!1}function Co(){return ey}var ty=v({},Fl,{key:function(e){if(e.key){var t=Fg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ti(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Pg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Co,charCode:function(e){return e.type==="keypress"?Ti(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ti(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ny=At(ty),ay=v({},Ai,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Pf=At(ay),ly=v({},Fl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Co}),ry=At(ly),iy=v({},Ea,{propertyName:0,elapsedTime:0,pseudoElement:0}),uy=At(iy),oy=v({},Ai,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),cy=At(oy),sy=v({},Ea,{newState:0,oldState:0}),fy=At(sy),dy=[9,13,27,32],_o=Cn&&"CompositionEvent"in window,Il=null;Cn&&"documentMode"in document&&(Il=document.documentMode);var hy=Cn&&"TextEvent"in window&&!Il,If=Cn&&(!_o||Il&&8<Il&&11>=Il),ed=" ",td=!1;function nd(e,t){switch(e){case"keyup":return dy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ad(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var tl=!1;function my(e,t){switch(e){case"compositionend":return ad(t);case"keypress":return t.which!==32?null:(td=!0,ed);case"textInput":return e=t.data,e===ed&&td?null:e;default:return null}}function py(e,t){if(tl)return e==="compositionend"||!_o&&nd(e,t)?(e=Kf(),Ei=So=Zn=null,tl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return If&&t.locale!=="ko"?null:t.data;default:return null}}var gy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ld(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!gy[e.type]:t==="textarea"}function rd(e,t,n,l){Ia?el?el.push(l):el=[l]:Ia=l,t=du(t,"onChange"),0<t.length&&(n=new _i("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var er=null,tr=null;function yy(e){km(e,0)}function Ri(e){var t=Kl(e);if(qf(t))return e}function id(e,t){if(e==="change")return t}var ud=!1;if(Cn){var Ao;if(Cn){var Ro="oninput"in document;if(!Ro){var od=document.createElement("div");od.setAttribute("oninput","return;"),Ro=typeof od.oninput=="function"}Ao=Ro}else Ao=!1;ud=Ao&&(!document.documentMode||9<document.documentMode)}function cd(){er&&(er.detachEvent("onpropertychange",sd),tr=er=null)}function sd(e){if(e.propertyName==="value"&&Ri(tr)){var t=[];rd(t,tr,e,yo(e)),Zf(yy,t)}}function vy(e,t,n){e==="focusin"?(cd(),er=t,tr=n,er.attachEvent("onpropertychange",sd)):e==="focusout"&&cd()}function by(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ri(tr)}function Sy(e,t){if(e==="click")return Ri(t)}function xy(e,t){if(e==="input"||e==="change")return Ri(t)}function Ey(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wt=typeof Object.is=="function"?Object.is:Ey;function nr(e,t){if(wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Gn.call(t,i)||!wt(e[i],t[i]))return!1}return!0}function fd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dd(e,t){var n=fd(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=fd(n)}}function hd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?hd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function md(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Si(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Si(e.document)}return t}function Oo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Ty=Cn&&"documentMode"in document&&11>=document.documentMode,nl=null,Mo=null,ar=null,zo=!1;function pd(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zo||nl==null||nl!==Si(l)||(l=nl,"selectionStart"in l&&Oo(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ar&&nr(ar,l)||(ar=l,l=du(Mo,"onSelect"),0<l.length&&(t=new _i("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=nl)))}function Ta(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var al={animationend:Ta("Animation","AnimationEnd"),animationiteration:Ta("Animation","AnimationIteration"),animationstart:Ta("Animation","AnimationStart"),transitionrun:Ta("Transition","TransitionRun"),transitionstart:Ta("Transition","TransitionStart"),transitioncancel:Ta("Transition","TransitionCancel"),transitionend:Ta("Transition","TransitionEnd")},Do={},gd={};Cn&&(gd=document.createElement("div").style,"AnimationEvent"in window||(delete al.animationend.animation,delete al.animationiteration.animation,delete al.animationstart.animation),"TransitionEvent"in window||delete al.transitionend.transition);function Ca(e){if(Do[e])return Do[e];if(!al[e])return e;var t=al[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in gd)return Do[e]=t[n];return e}var yd=Ca("animationend"),vd=Ca("animationiteration"),bd=Ca("animationstart"),Cy=Ca("transitionrun"),_y=Ca("transitionstart"),Ay=Ca("transitioncancel"),Sd=Ca("transitionend"),xd=new Map,wo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");wo.push("scrollEnd");function ln(e,t){xd.set(e,t),xa(t,[e])}var Ed=new WeakMap;function Qt(e,t){if(typeof e=="object"&&e!==null){var n=Ed.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Lf(t)},Ed.set(e,t),t)}return{value:e,source:t,stack:Lf(t)}}var Zt=[],ll=0,No=0;function Oi(){for(var e=ll,t=No=ll=0;t<e;){var n=Zt[t];Zt[t++]=null;var l=Zt[t];Zt[t++]=null;var i=Zt[t];Zt[t++]=null;var c=Zt[t];if(Zt[t++]=null,l!==null&&i!==null){var h=l.pending;h===null?i.next=i:(i.next=h.next,h.next=i),l.pending=i}c!==0&&Td(n,i,c)}}function Mi(e,t,n,l){Zt[ll++]=e,Zt[ll++]=t,Zt[ll++]=n,Zt[ll++]=l,No|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function jo(e,t,n,l){return Mi(e,t,n,l),zi(e)}function rl(e,t){return Mi(e,null,null,t),zi(e)}function Td(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var i=!1,c=e.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(i=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,i&&t!==null&&(i=31-mt(n),e=c.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=n|536870912),c):null}function zi(e){if(50<Or)throw Or=0,qc=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var il={};function Ry(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Nt(e,t,n,l){return new Ry(e,t,n,l)}function Bo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function _n(e,t){var n=e.alternate;return n===null?(n=Nt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Cd(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Di(e,t,n,l,i,c){var h=0;if(l=e,typeof e=="function")Bo(e)&&(h=1);else if(typeof e=="string")h=Mv(e,n,ie.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case te:return e=Nt(31,n,t,i),e.elementType=te,e.lanes=c,e;case O:return _a(n.children,i,c,t);case C:h=8,i|=24;break;case B:return e=Nt(12,n,t,i|2),e.elementType=B,e.lanes=c,e;case z:return e=Nt(13,n,t,i),e.elementType=z,e.lanes=c,e;case W:return e=Nt(19,n,t,i),e.elementType=W,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case k:case L:h=10;break e;case V:h=9;break e;case q:h=11;break e;case P:h=14;break e;case ee:h=16,l=null;break e}h=29,n=Error(u(130,e===null?"null":typeof e,"")),l=null}return t=Nt(h,n,t,i),t.elementType=e,t.type=l,t.lanes=c,t}function _a(e,t,n,l){return e=Nt(7,e,l,t),e.lanes=n,e}function Uo(e,t,n){return e=Nt(6,e,null,t),e.lanes=n,e}function Ho(e,t,n){return t=Nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ul=[],ol=0,wi=null,Ni=0,Kt=[],Jt=0,Aa=null,An=1,Rn="";function Ra(e,t){ul[ol++]=Ni,ul[ol++]=wi,wi=e,Ni=t}function _d(e,t,n){Kt[Jt++]=An,Kt[Jt++]=Rn,Kt[Jt++]=Aa,Aa=e;var l=An;e=Rn;var i=32-mt(l)-1;l&=~(1<<i),n+=1;var c=32-mt(t)+i;if(30<c){var h=i-i%5;c=(l&(1<<h)-1).toString(32),l>>=h,i-=h,An=1<<32-mt(t)+i|n<<i|l,Rn=c+e}else An=1<<c|n<<i|l,Rn=e}function Lo(e){e.return!==null&&(Ra(e,1),_d(e,1,0))}function ko(e){for(;e===wi;)wi=ul[--ol],ul[ol]=null,Ni=ul[--ol],ul[ol]=null;for(;e===Aa;)Aa=Kt[--Jt],Kt[Jt]=null,Rn=Kt[--Jt],Kt[Jt]=null,An=Kt[--Jt],Kt[Jt]=null}var Tt=null,Ve=null,_e=!1,Oa=null,sn=!1,qo=Error(u(519));function Ma(e){var t=Error(u(418,""));throw ir(Qt(t,e)),qo}function Ad(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[yt]=e,t[_t]=l,n){case"dialog":Se("cancel",t),Se("close",t);break;case"iframe":case"object":case"embed":Se("load",t);break;case"video":case"audio":for(n=0;n<zr.length;n++)Se(zr[n],t);break;case"source":Se("error",t);break;case"img":case"image":case"link":Se("error",t),Se("load",t);break;case"details":Se("toggle",t);break;case"input":Se("invalid",t),$f(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),bi(t);break;case"select":Se("invalid",t);break;case"textarea":Se("invalid",t),Gf(t,l.value,l.defaultValue,l.children),bi(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||Gm(t.textContent,n)?(l.popover!=null&&(Se("beforetoggle",t),Se("toggle",t)),l.onScroll!=null&&Se("scroll",t),l.onScrollEnd!=null&&Se("scrollend",t),l.onClick!=null&&(t.onclick=hu),t=!0):t=!1,t||Ma(e)}function Rd(e){for(Tt=e.return;Tt;)switch(Tt.tag){case 5:case 13:sn=!1;return;case 27:case 3:sn=!0;return;default:Tt=Tt.return}}function lr(e){if(e!==Tt)return!1;if(!_e)return Rd(e),_e=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||ns(e.type,e.memoizedProps)),n=!n),n&&Ve&&Ma(e),Rd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ve=un(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ve=null}}else t===27?(t=Ve,ca(e.type)?(e=is,is=null,Ve=e):Ve=t):Ve=Tt?un(e.stateNode.nextSibling):null;return!0}function rr(){Ve=Tt=null,_e=!1}function Od(){var e=Oa;return e!==null&&(Mt===null?Mt=e:Mt.push.apply(Mt,e),Oa=null),e}function ir(e){Oa===null?Oa=[e]:Oa.push(e)}var $o=X(null),za=null,On=null;function Kn(e,t,n){F($o,t._currentValue),t._currentValue=n}function Mn(e){e._currentValue=$o.current,J($o)}function Yo(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function Go(e,t,n,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var c=i.dependencies;if(c!==null){var h=i.child;c=c.firstContext;e:for(;c!==null;){var y=c;c=i;for(var x=0;x<t.length;x++)if(y.context===t[x]){c.lanes|=n,y=c.alternate,y!==null&&(y.lanes|=n),Yo(c.return,n,e),l||(h=null);break e}c=y.next}}else if(i.tag===18){if(h=i.return,h===null)throw Error(u(341));h.lanes|=n,c=h.alternate,c!==null&&(c.lanes|=n),Yo(h,n,e),h=null}else h=i.child;if(h!==null)h.return=i;else for(h=i;h!==null;){if(h===e){h=null;break}if(i=h.sibling,i!==null){i.return=h.return,h=i;break}h=h.return}i=h}}function ur(e,t,n,l){e=null;for(var i=t,c=!1;i!==null;){if(!c){if((i.flags&524288)!==0)c=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var h=i.alternate;if(h===null)throw Error(u(387));if(h=h.memoizedProps,h!==null){var y=i.type;wt(i.pendingProps.value,h.value)||(e!==null?e.push(y):e=[y])}}else if(i===Ce.current){if(h=i.alternate,h===null)throw Error(u(387));h.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ur):e=[Ur])}i=i.return}e!==null&&Go(t,e,n,l),t.flags|=262144}function ji(e){for(e=e.firstContext;e!==null;){if(!wt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Da(e){za=e,On=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function vt(e){return Md(za,e)}function Bi(e,t){return za===null&&Da(e),Md(e,t)}function Md(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},On===null){if(e===null)throw Error(u(308));On=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else On=On.next=t;return n}var Oy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},My=a.unstable_scheduleCallback,zy=a.unstable_NormalPriority,tt={$$typeof:L,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Vo(){return{controller:new Oy,data:new Map,refCount:0}}function or(e){e.refCount--,e.refCount===0&&My(zy,function(){e.controller.abort()})}var cr=null,Xo=0,cl=0,sl=null;function Dy(e,t){if(cr===null){var n=cr=[];Xo=0,cl=Zc(),sl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Xo++,t.then(zd,zd),t}function zd(){if(--Xo===0&&cr!==null){sl!==null&&(sl.status="fulfilled");var e=cr;cr=null,cl=0,sl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function wy(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var Dd=H.S;H.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Dy(e,t),Dd!==null&&Dd(e,t)};var wa=X(null);function Qo(){var e=wa.current;return e!==null?e:ke.pooledCache}function Ui(e,t){t===null?F(wa,wa.current):F(wa,t.pool)}function wd(){var e=Qo();return e===null?null:{parent:tt._currentValue,pool:e}}var sr=Error(u(460)),Nd=Error(u(474)),Hi=Error(u(542)),Zo={then:function(){}};function jd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Li(){}function Bd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Li,Li),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hd(e),e;default:if(typeof t.status=="string")t.then(Li,Li);else{if(e=ke,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Hd(e),e}throw fr=t,sr}}var fr=null;function Ud(){if(fr===null)throw Error(u(459));var e=fr;return fr=null,e}function Hd(e){if(e===sr||e===Hi)throw Error(u(483))}var Jn=!1;function Ko(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Jo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Wn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Fn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ze&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=zi(e),Td(e,null,n),t}return Mi(e,l,t,n),zi(e)}function dr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,zf(e,n)}}function Wo(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?i=c=h:c=c.next=h,n=n.next}while(n!==null);c===null?i=c=t:c=c.next=t}else i=c=t;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Fo=!1;function hr(){if(Fo){var e=sl;if(e!==null)throw e}}function mr(e,t,n,l){Fo=!1;var i=e.updateQueue;Jn=!1;var c=i.firstBaseUpdate,h=i.lastBaseUpdate,y=i.shared.pending;if(y!==null){i.shared.pending=null;var x=y,w=x.next;x.next=null,h===null?c=w:h.next=w,h=x;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,y=Y.lastBaseUpdate,y!==h&&(y===null?Y.firstBaseUpdate=w:y.next=w,Y.lastBaseUpdate=x))}if(c!==null){var Q=i.baseState;h=0,Y=w=x=null,y=c;do{var j=y.lane&-536870913,U=j!==y.lane;if(U?(xe&j)===j:(l&j)===j){j!==0&&j===cl&&(Fo=!0),Y!==null&&(Y=Y.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var he=e,fe=y;j=t;var je=n;switch(fe.tag){case 1:if(he=fe.payload,typeof he=="function"){Q=he.call(je,Q,j);break e}Q=he;break e;case 3:he.flags=he.flags&-65537|128;case 0:if(he=fe.payload,j=typeof he=="function"?he.call(je,Q,j):he,j==null)break e;Q=v({},Q,j);break e;case 2:Jn=!0}}j=y.callback,j!==null&&(e.flags|=64,U&&(e.flags|=8192),U=i.callbacks,U===null?i.callbacks=[j]:U.push(j))}else U={lane:j,tag:y.tag,payload:y.payload,callback:y.callback,next:null},Y===null?(w=Y=U,x=Q):Y=Y.next=U,h|=j;if(y=y.next,y===null){if(y=i.shared.pending,y===null)break;U=y,y=U.next,U.next=null,i.lastBaseUpdate=U,i.shared.pending=null}}while(!0);Y===null&&(x=Q),i.baseState=x,i.firstBaseUpdate=w,i.lastBaseUpdate=Y,c===null&&(i.shared.lanes=0),ra|=h,e.lanes=h,e.memoizedState=Q}}function Ld(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function kd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Ld(n[e],t)}var fl=X(null),ki=X(0);function qd(e,t){e=Un,F(ki,e),F(fl,t),Un=e|t.baseLanes}function Po(){F(ki,Un),F(fl,fl.current)}function Io(){Un=ki.current,J(fl),J(ki)}var Pn=0,ye=null,we=null,Fe=null,qi=!1,dl=!1,Na=!1,$i=0,pr=0,hl=null,Ny=0;function Ke(){throw Error(u(321))}function ec(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!wt(e[n],t[n]))return!1;return!0}function tc(e,t,n,l,i,c){return Pn=c,ye=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,H.H=e===null||e.memoizedState===null?Th:Ch,Na=!1,c=n(l,i),Na=!1,dl&&(c=Yd(t,n,l,i)),$d(e),c}function $d(e){H.H=Zi;var t=we!==null&&we.next!==null;if(Pn=0,Fe=we=ye=null,qi=!1,pr=0,hl=null,t)throw Error(u(300));e===null||rt||(e=e.dependencies,e!==null&&ji(e)&&(rt=!0))}function Yd(e,t,n,l){ye=e;var i=0;do{if(dl&&(hl=null),pr=0,dl=!1,25<=i)throw Error(u(301));if(i+=1,Fe=we=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}H.H=qy,c=t(n,l)}while(dl);return c}function jy(){var e=H.H,t=e.useState()[0];return t=typeof t.then=="function"?gr(t):t,e=e.useState()[0],(we!==null?we.memoizedState:null)!==e&&(ye.flags|=1024),t}function nc(){var e=$i!==0;return $i=0,e}function ac(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function lc(e){if(qi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}qi=!1}Pn=0,Fe=we=ye=null,dl=!1,pr=$i=0,hl=null}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Fe===null?ye.memoizedState=Fe=e:Fe=Fe.next=e,Fe}function Pe(){if(we===null){var e=ye.alternate;e=e!==null?e.memoizedState:null}else e=we.next;var t=Fe===null?ye.memoizedState:Fe.next;if(t!==null)Fe=t,we=e;else{if(e===null)throw ye.alternate===null?Error(u(467)):Error(u(310));we=e,e={memoizedState:we.memoizedState,baseState:we.baseState,baseQueue:we.baseQueue,queue:we.queue,next:null},Fe===null?ye.memoizedState=Fe=e:Fe=Fe.next=e}return Fe}function rc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function gr(e){var t=pr;return pr+=1,hl===null&&(hl=[]),e=Bd(hl,e,t),t=ye,(Fe===null?t.memoizedState:Fe.next)===null&&(t=t.alternate,H.H=t===null||t.memoizedState===null?Th:Ch),e}function Yi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return gr(e);if(e.$$typeof===L)return vt(e)}throw Error(u(438,String(e)))}function ic(e){var t=null,n=ye.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=ye.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=rc(),ye.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=b;return t.index++,n}function zn(e,t){return typeof t=="function"?t(e):t}function Gi(e){var t=Pe();return uc(t,we,e)}function uc(e,t,n){var l=e.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=n;var i=e.baseQueue,c=l.pending;if(c!==null){if(i!==null){var h=i.next;i.next=c.next,c.next=h}t.baseQueue=i=c,l.pending=null}if(c=e.baseState,i===null)e.memoizedState=c;else{t=i.next;var y=h=null,x=null,w=t,Y=!1;do{var Q=w.lane&-536870913;if(Q!==w.lane?(xe&Q)===Q:(Pn&Q)===Q){var j=w.revertLane;if(j===0)x!==null&&(x=x.next={lane:0,revertLane:0,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null}),Q===cl&&(Y=!0);else if((Pn&j)===j){w=w.next,j===cl&&(Y=!0);continue}else Q={lane:0,revertLane:w.revertLane,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null},x===null?(y=x=Q,h=c):x=x.next=Q,ye.lanes|=j,ra|=j;Q=w.action,Na&&n(c,Q),c=w.hasEagerState?w.eagerState:n(c,Q)}else j={lane:Q,revertLane:w.revertLane,action:w.action,hasEagerState:w.hasEagerState,eagerState:w.eagerState,next:null},x===null?(y=x=j,h=c):x=x.next=j,ye.lanes|=Q,ra|=Q;w=w.next}while(w!==null&&w!==t);if(x===null?h=c:x.next=y,!wt(c,e.memoizedState)&&(rt=!0,Y&&(n=sl,n!==null)))throw n;e.memoizedState=c,e.baseState=h,e.baseQueue=x,l.lastRenderedState=c}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function oc(e){var t=Pe(),n=t.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=e;var l=n.dispatch,i=n.pending,c=t.memoizedState;if(i!==null){n.pending=null;var h=i=i.next;do c=e(c,h.action),h=h.next;while(h!==i);wt(c,t.memoizedState)||(rt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,l]}function Gd(e,t,n){var l=ye,i=Pe(),c=_e;if(c){if(n===void 0)throw Error(u(407));n=n()}else n=t();var h=!wt((we||i).memoizedState,n);h&&(i.memoizedState=n,rt=!0),i=i.queue;var y=Qd.bind(null,l,i,e);if(yr(2048,8,y,[e]),i.getSnapshot!==t||h||Fe!==null&&Fe.memoizedState.tag&1){if(l.flags|=2048,ml(9,Vi(),Xd.bind(null,l,i,n,t),null),ke===null)throw Error(u(349));c||(Pn&124)!==0||Vd(l,t,n)}return n}function Vd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ye.updateQueue,t===null?(t=rc(),ye.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Xd(e,t,n,l){t.value=n,t.getSnapshot=l,Zd(t)&&Kd(e)}function Qd(e,t,n){return n(function(){Zd(t)&&Kd(e)})}function Zd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!wt(e,n)}catch{return!0}}function Kd(e){var t=rl(e,2);t!==null&&Lt(t,e,2)}function cc(e){var t=Rt();if(typeof e=="function"){var n=e;if(e=n(),Na){an(!0);try{n()}finally{an(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:zn,lastRenderedState:e},t}function Jd(e,t,n,l){return e.baseState=n,uc(e,we,typeof l=="function"?l:zn)}function By(e,t,n,l,i){if(Qi(e))throw Error(u(485));if(e=t.action,e!==null){var c={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){c.listeners.push(h)}};H.T!==null?n(!0):c.isTransition=!1,l(c),n=t.pending,n===null?(c.next=t.pending=c,Wd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Wd(e,t){var n=t.action,l=t.payload,i=e.state;if(t.isTransition){var c=H.T,h={};H.T=h;try{var y=n(i,l),x=H.S;x!==null&&x(h,y),Fd(e,t,y)}catch(w){sc(e,t,w)}finally{H.T=c}}else try{c=n(i,l),Fd(e,t,c)}catch(w){sc(e,t,w)}}function Fd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Pd(e,t,l)},function(l){return sc(e,t,l)}):Pd(e,t,n)}function Pd(e,t,n){t.status="fulfilled",t.value=n,Id(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Wd(e,n)))}function sc(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,Id(t),t=t.next;while(t!==l)}e.action=null}function Id(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function eh(e,t){return t}function th(e,t){if(_e){var n=ke.formState;if(n!==null){e:{var l=ye;if(_e){if(Ve){t:{for(var i=Ve,c=sn;i.nodeType!==8;){if(!c){i=null;break t}if(i=un(i.nextSibling),i===null){i=null;break t}}c=i.data,i=c==="F!"||c==="F"?i:null}if(i){Ve=un(i.nextSibling),l=i.data==="F!";break e}}Ma(l)}l=!1}l&&(t=n[0])}}return n=Rt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:eh,lastRenderedState:t},n.queue=l,n=Sh.bind(null,ye,l),l.dispatch=n,l=cc(!1),c=pc.bind(null,ye,!1,l.queue),l=Rt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,n=By.bind(null,ye,i,c,n),i.dispatch=n,l.memoizedState=e,[t,n,!1]}function nh(e){var t=Pe();return ah(t,we,e)}function ah(e,t,n){if(t=uc(e,t,eh)[0],e=Gi(zn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=gr(t)}catch(h){throw h===sr?Hi:h}else l=t;t=Pe();var i=t.queue,c=i.dispatch;return n!==t.memoizedState&&(ye.flags|=2048,ml(9,Vi(),Uy.bind(null,i,n),null)),[l,c,e]}function Uy(e,t){e.action=t}function lh(e){var t=Pe(),n=we;if(n!==null)return ah(t,n,e);Pe(),t=t.memoizedState,n=Pe();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function ml(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=ye.updateQueue,t===null&&(t=rc(),ye.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function Vi(){return{destroy:void 0,resource:void 0}}function rh(){return Pe().memoizedState}function Xi(e,t,n,l){var i=Rt();l=l===void 0?null:l,ye.flags|=e,i.memoizedState=ml(1|t,Vi(),n,l)}function yr(e,t,n,l){var i=Pe();l=l===void 0?null:l;var c=i.memoizedState.inst;we!==null&&l!==null&&ec(l,we.memoizedState.deps)?i.memoizedState=ml(t,c,n,l):(ye.flags|=e,i.memoizedState=ml(1|t,c,n,l))}function ih(e,t){Xi(8390656,8,e,t)}function uh(e,t){yr(2048,8,e,t)}function oh(e,t){return yr(4,2,e,t)}function ch(e,t){return yr(4,4,e,t)}function sh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function fh(e,t,n){n=n!=null?n.concat([e]):null,yr(4,4,sh.bind(null,t,e),n)}function fc(){}function dh(e,t){var n=Pe();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&ec(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function hh(e,t){var n=Pe();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&ec(t,l[1]))return l[0];if(l=e(),Na){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[l,t],l}function dc(e,t,n){return n===void 0||(Pn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=gm(),ye.lanes|=e,ra|=e,n)}function mh(e,t,n,l){return wt(n,t)?n:fl.current!==null?(e=dc(e,n,l),wt(e,t)||(rt=!0),e):(Pn&42)===0?(rt=!0,e.memoizedState=n):(e=gm(),ye.lanes|=e,ra|=e,t)}function ph(e,t,n,l,i){var c=K.p;K.p=c!==0&&8>c?c:8;var h=H.T,y={};H.T=y,pc(e,!1,t,n);try{var x=i(),w=H.S;if(w!==null&&w(y,x),x!==null&&typeof x=="object"&&typeof x.then=="function"){var Y=wy(x,l);vr(e,t,Y,Ht(e))}else vr(e,t,l,Ht(e))}catch(Q){vr(e,t,{then:function(){},status:"rejected",reason:Q},Ht())}finally{K.p=c,H.T=h}}function Hy(){}function hc(e,t,n,l){if(e.tag!==5)throw Error(u(476));var i=gh(e).queue;ph(e,i,t,ne,n===null?Hy:function(){return yh(e),n(l)})}function gh(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ne,baseState:ne,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:zn,lastRenderedState:ne},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:zn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function yh(e){var t=gh(e).next.queue;vr(e,t,{},Ht())}function mc(){return vt(Ur)}function vh(){return Pe().memoizedState}function bh(){return Pe().memoizedState}function Ly(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Ht();e=Wn(n);var l=Fn(t,e,n);l!==null&&(Lt(l,t,n),dr(l,t,n)),t={cache:Vo()},e.payload=t;return}t=t.return}}function ky(e,t,n){var l=Ht();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Qi(e)?xh(t,n):(n=jo(e,t,n,l),n!==null&&(Lt(n,e,l),Eh(n,t,l)))}function Sh(e,t,n){var l=Ht();vr(e,t,n,l)}function vr(e,t,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qi(e))xh(t,i);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,y=c(h,n);if(i.hasEagerState=!0,i.eagerState=y,wt(y,h))return Mi(e,t,i,0),ke===null&&Oi(),!1}catch{}finally{}if(n=jo(e,t,i,l),n!==null)return Lt(n,e,l),Eh(n,t,l),!0}return!1}function pc(e,t,n,l){if(l={lane:2,revertLane:Zc(),action:l,hasEagerState:!1,eagerState:null,next:null},Qi(e)){if(t)throw Error(u(479))}else t=jo(e,n,l,2),t!==null&&Lt(t,e,2)}function Qi(e){var t=e.alternate;return e===ye||t!==null&&t===ye}function xh(e,t){dl=qi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Eh(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,zf(e,n)}}var Zi={readContext:vt,use:Yi,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useLayoutEffect:Ke,useInsertionEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useSyncExternalStore:Ke,useId:Ke,useHostTransitionStatus:Ke,useFormState:Ke,useActionState:Ke,useOptimistic:Ke,useMemoCache:Ke,useCacheRefresh:Ke},Th={readContext:vt,use:Yi,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:ih,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Xi(4194308,4,sh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){Xi(4,2,e,t)},useMemo:function(e,t){var n=Rt();t=t===void 0?null:t;var l=e();if(Na){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=Rt();if(n!==void 0){var i=n(t);if(Na){an(!0);try{n(t)}finally{an(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=ky.bind(null,ye,e),[l.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:function(e){e=cc(e);var t=e.queue,n=Sh.bind(null,ye,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:fc,useDeferredValue:function(e,t){var n=Rt();return dc(n,e,t)},useTransition:function(){var e=cc(!1);return e=ph.bind(null,ye,e.queue,!0,!1),Rt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=ye,i=Rt();if(_e){if(n===void 0)throw Error(u(407));n=n()}else{if(n=t(),ke===null)throw Error(u(349));(xe&124)!==0||Vd(l,t,n)}i.memoizedState=n;var c={value:n,getSnapshot:t};return i.queue=c,ih(Qd.bind(null,l,c,e),[e]),l.flags|=2048,ml(9,Vi(),Xd.bind(null,l,c,n,t),null),n},useId:function(){var e=Rt(),t=ke.identifierPrefix;if(_e){var n=Rn,l=An;n=(l&~(1<<32-mt(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=$i++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Ny++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:mc,useFormState:th,useActionState:th,useOptimistic:function(e){var t=Rt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=pc.bind(null,ye,!0,n),n.dispatch=t,[e,t]},useMemoCache:ic,useCacheRefresh:function(){return Rt().memoizedState=Ly.bind(null,ye)}},Ch={readContext:vt,use:Yi,useCallback:dh,useContext:vt,useEffect:uh,useImperativeHandle:fh,useInsertionEffect:oh,useLayoutEffect:ch,useMemo:hh,useReducer:Gi,useRef:rh,useState:function(){return Gi(zn)},useDebugValue:fc,useDeferredValue:function(e,t){var n=Pe();return mh(n,we.memoizedState,e,t)},useTransition:function(){var e=Gi(zn)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:gr(e),t]},useSyncExternalStore:Gd,useId:vh,useHostTransitionStatus:mc,useFormState:nh,useActionState:nh,useOptimistic:function(e,t){var n=Pe();return Jd(n,we,e,t)},useMemoCache:ic,useCacheRefresh:bh},qy={readContext:vt,use:Yi,useCallback:dh,useContext:vt,useEffect:uh,useImperativeHandle:fh,useInsertionEffect:oh,useLayoutEffect:ch,useMemo:hh,useReducer:oc,useRef:rh,useState:function(){return oc(zn)},useDebugValue:fc,useDeferredValue:function(e,t){var n=Pe();return we===null?dc(n,e,t):mh(n,we.memoizedState,e,t)},useTransition:function(){var e=oc(zn)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:gr(e),t]},useSyncExternalStore:Gd,useId:vh,useHostTransitionStatus:mc,useFormState:lh,useActionState:lh,useOptimistic:function(e,t){var n=Pe();return we!==null?Jd(n,we,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:ic,useCacheRefresh:bh},pl=null,br=0;function Ki(e){var t=br;return br+=1,pl===null&&(pl=[]),Bd(pl,e,t)}function Sr(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ji(e,t){throw t.$$typeof===S?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function _h(e){var t=e._init;return t(e._payload)}function Ah(e){function t(M,R){if(e){var D=M.deletions;D===null?(M.deletions=[R],M.flags|=16):D.push(R)}}function n(M,R){if(!e)return null;for(;R!==null;)t(M,R),R=R.sibling;return null}function l(M){for(var R=new Map;M!==null;)M.key!==null?R.set(M.key,M):R.set(M.index,M),M=M.sibling;return R}function i(M,R){return M=_n(M,R),M.index=0,M.sibling=null,M}function c(M,R,D){return M.index=D,e?(D=M.alternate,D!==null?(D=D.index,D<R?(M.flags|=67108866,R):D):(M.flags|=67108866,R)):(M.flags|=1048576,R)}function h(M){return e&&M.alternate===null&&(M.flags|=67108866),M}function y(M,R,D,G){return R===null||R.tag!==6?(R=Uo(D,M.mode,G),R.return=M,R):(R=i(R,D),R.return=M,R)}function x(M,R,D,G){var ae=D.type;return ae===O?Y(M,R,D.props.children,G,D.key):R!==null&&(R.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===ee&&_h(ae)===R.type)?(R=i(R,D.props),Sr(R,D),R.return=M,R):(R=Di(D.type,D.key,D.props,null,M.mode,G),Sr(R,D),R.return=M,R)}function w(M,R,D,G){return R===null||R.tag!==4||R.stateNode.containerInfo!==D.containerInfo||R.stateNode.implementation!==D.implementation?(R=Ho(D,M.mode,G),R.return=M,R):(R=i(R,D.children||[]),R.return=M,R)}function Y(M,R,D,G,ae){return R===null||R.tag!==7?(R=_a(D,M.mode,G,ae),R.return=M,R):(R=i(R,D),R.return=M,R)}function Q(M,R,D){if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return R=Uo(""+R,M.mode,D),R.return=M,R;if(typeof R=="object"&&R!==null){switch(R.$$typeof){case T:return D=Di(R.type,R.key,R.props,null,M.mode,D),Sr(D,R),D.return=M,D;case N:return R=Ho(R,M.mode,D),R.return=M,R;case ee:var G=R._init;return R=G(R._payload),Q(M,R,D)}if(pe(R)||I(R))return R=_a(R,M.mode,D,null),R.return=M,R;if(typeof R.then=="function")return Q(M,Ki(R),D);if(R.$$typeof===L)return Q(M,Bi(M,R),D);Ji(M,R)}return null}function j(M,R,D,G){var ae=R!==null?R.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return ae!==null?null:y(M,R,""+D,G);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case T:return D.key===ae?x(M,R,D,G):null;case N:return D.key===ae?w(M,R,D,G):null;case ee:return ae=D._init,D=ae(D._payload),j(M,R,D,G)}if(pe(D)||I(D))return ae!==null?null:Y(M,R,D,G,null);if(typeof D.then=="function")return j(M,R,Ki(D),G);if(D.$$typeof===L)return j(M,R,Bi(M,D),G);Ji(M,D)}return null}function U(M,R,D,G,ae){if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return M=M.get(D)||null,y(R,M,""+G,ae);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case T:return M=M.get(G.key===null?D:G.key)||null,x(R,M,G,ae);case N:return M=M.get(G.key===null?D:G.key)||null,w(R,M,G,ae);case ee:var ve=G._init;return G=ve(G._payload),U(M,R,D,G,ae)}if(pe(G)||I(G))return M=M.get(D)||null,Y(R,M,G,ae,null);if(typeof G.then=="function")return U(M,R,D,Ki(G),ae);if(G.$$typeof===L)return U(M,R,D,Bi(R,G),ae);Ji(R,G)}return null}function he(M,R,D,G){for(var ae=null,ve=null,ue=R,de=R=0,ut=null;ue!==null&&de<D.length;de++){ue.index>de?(ut=ue,ue=null):ut=ue.sibling;var Te=j(M,ue,D[de],G);if(Te===null){ue===null&&(ue=ut);break}e&&ue&&Te.alternate===null&&t(M,ue),R=c(Te,R,de),ve===null?ae=Te:ve.sibling=Te,ve=Te,ue=ut}if(de===D.length)return n(M,ue),_e&&Ra(M,de),ae;if(ue===null){for(;de<D.length;de++)ue=Q(M,D[de],G),ue!==null&&(R=c(ue,R,de),ve===null?ae=ue:ve.sibling=ue,ve=ue);return _e&&Ra(M,de),ae}for(ue=l(ue);de<D.length;de++)ut=U(ue,M,de,D[de],G),ut!==null&&(e&&ut.alternate!==null&&ue.delete(ut.key===null?de:ut.key),R=c(ut,R,de),ve===null?ae=ut:ve.sibling=ut,ve=ut);return e&&ue.forEach(function(ma){return t(M,ma)}),_e&&Ra(M,de),ae}function fe(M,R,D,G){if(D==null)throw Error(u(151));for(var ae=null,ve=null,ue=R,de=R=0,ut=null,Te=D.next();ue!==null&&!Te.done;de++,Te=D.next()){ue.index>de?(ut=ue,ue=null):ut=ue.sibling;var ma=j(M,ue,Te.value,G);if(ma===null){ue===null&&(ue=ut);break}e&&ue&&ma.alternate===null&&t(M,ue),R=c(ma,R,de),ve===null?ae=ma:ve.sibling=ma,ve=ma,ue=ut}if(Te.done)return n(M,ue),_e&&Ra(M,de),ae;if(ue===null){for(;!Te.done;de++,Te=D.next())Te=Q(M,Te.value,G),Te!==null&&(R=c(Te,R,de),ve===null?ae=Te:ve.sibling=Te,ve=Te);return _e&&Ra(M,de),ae}for(ue=l(ue);!Te.done;de++,Te=D.next())Te=U(ue,M,de,Te.value,G),Te!==null&&(e&&Te.alternate!==null&&ue.delete(Te.key===null?de:Te.key),R=c(Te,R,de),ve===null?ae=Te:ve.sibling=Te,ve=Te);return e&&ue.forEach(function($v){return t(M,$v)}),_e&&Ra(M,de),ae}function je(M,R,D,G){if(typeof D=="object"&&D!==null&&D.type===O&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case T:e:{for(var ae=D.key;R!==null;){if(R.key===ae){if(ae=D.type,ae===O){if(R.tag===7){n(M,R.sibling),G=i(R,D.props.children),G.return=M,M=G;break e}}else if(R.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===ee&&_h(ae)===R.type){n(M,R.sibling),G=i(R,D.props),Sr(G,D),G.return=M,M=G;break e}n(M,R);break}else t(M,R);R=R.sibling}D.type===O?(G=_a(D.props.children,M.mode,G,D.key),G.return=M,M=G):(G=Di(D.type,D.key,D.props,null,M.mode,G),Sr(G,D),G.return=M,M=G)}return h(M);case N:e:{for(ae=D.key;R!==null;){if(R.key===ae)if(R.tag===4&&R.stateNode.containerInfo===D.containerInfo&&R.stateNode.implementation===D.implementation){n(M,R.sibling),G=i(R,D.children||[]),G.return=M,M=G;break e}else{n(M,R);break}else t(M,R);R=R.sibling}G=Ho(D,M.mode,G),G.return=M,M=G}return h(M);case ee:return ae=D._init,D=ae(D._payload),je(M,R,D,G)}if(pe(D))return he(M,R,D,G);if(I(D)){if(ae=I(D),typeof ae!="function")throw Error(u(150));return D=ae.call(D),fe(M,R,D,G)}if(typeof D.then=="function")return je(M,R,Ki(D),G);if(D.$$typeof===L)return je(M,R,Bi(M,D),G);Ji(M,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,R!==null&&R.tag===6?(n(M,R.sibling),G=i(R,D),G.return=M,M=G):(n(M,R),G=Uo(D,M.mode,G),G.return=M,M=G),h(M)):n(M,R)}return function(M,R,D,G){try{br=0;var ae=je(M,R,D,G);return pl=null,ae}catch(ue){if(ue===sr||ue===Hi)throw ue;var ve=Nt(29,ue,null,M.mode);return ve.lanes=G,ve.return=M,ve}finally{}}}var gl=Ah(!0),Rh=Ah(!1),Wt=X(null),fn=null;function In(e){var t=e.alternate;F(nt,nt.current&1),F(Wt,e),fn===null&&(t===null||fl.current!==null||t.memoizedState!==null)&&(fn=e)}function Oh(e){if(e.tag===22){if(F(nt,nt.current),F(Wt,e),fn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(fn=e)}}else ea()}function ea(){F(nt,nt.current),F(Wt,Wt.current)}function Dn(e){J(Wt),fn===e&&(fn=null),J(nt)}var nt=X(0);function Wi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||rs(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function gc(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:v({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var yc={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=Ht(),i=Wn(l);i.payload=t,n!=null&&(i.callback=n),t=Fn(e,i,l),t!==null&&(Lt(t,e,l),dr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=Ht(),i=Wn(l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Fn(e,i,l),t!==null&&(Lt(t,e,l),dr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ht(),l=Wn(n);l.tag=2,t!=null&&(l.callback=t),t=Fn(e,l,n),t!==null&&(Lt(t,e,n),dr(t,e,n))}};function Mh(e,t,n,l,i,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,h):t.prototype&&t.prototype.isPureReactComponent?!nr(n,l)||!nr(i,c):!0}function zh(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&yc.enqueueReplaceState(t,t.state,null)}function ja(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=v({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Fi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Dh(e){Fi(e)}function wh(e){console.error(e)}function Nh(e){Fi(e)}function Pi(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function jh(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function vc(e,t,n){return n=Wn(n),n.tag=3,n.payload={element:null},n.callback=function(){Pi(e,t)},n}function Bh(e){return e=Wn(e),e.tag=3,e}function Uh(e,t,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var c=l.value;e.payload=function(){return i(c)},e.callback=function(){jh(t,n,l)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){jh(t,n,l),typeof i!="function"&&(ia===null?ia=new Set([this]):ia.add(this));var y=l.stack;this.componentDidCatch(l.value,{componentStack:y!==null?y:""})})}function $y(e,t,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&ur(t,n,i,!0),n=Wt.current,n!==null){switch(n.tag){case 13:return fn===null?Yc():n.alternate===null&&Xe===0&&(Xe=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Zo?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),Vc(e,l,i)),!1;case 22:return n.flags|=65536,l===Zo?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),Vc(e,l,i)),!1}throw Error(u(435,n.tag))}return Vc(e,l,i),Yc(),!1}if(_e)return t=Wt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==qo&&(e=Error(u(422),{cause:l}),ir(Qt(e,n)))):(l!==qo&&(t=Error(u(423),{cause:l}),ir(Qt(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Qt(l,n),i=vc(e.stateNode,l,i),Wo(e,i),Xe!==4&&(Xe=2)),!1;var c=Error(u(520),{cause:l});if(c=Qt(c,n),Rr===null?Rr=[c]:Rr.push(c),Xe!==4&&(Xe=2),t===null)return!0;l=Qt(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=vc(n.stateNode,l,e),Wo(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(ia===null||!ia.has(c))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Bh(i),Uh(i,e,n,l),Wo(n,i),!1}n=n.return}while(n!==null);return!1}var Hh=Error(u(461)),rt=!1;function ot(e,t,n,l){t.child=e===null?Rh(t,null,n,l):gl(t,e.child,n,l)}function Lh(e,t,n,l,i){n=n.render;var c=t.ref;if("ref"in l){var h={};for(var y in l)y!=="ref"&&(h[y]=l[y])}else h=l;return Da(t),l=tc(e,t,n,h,c,i),y=nc(),e!==null&&!rt?(ac(e,t,i),wn(e,t,i)):(_e&&y&&Lo(t),t.flags|=1,ot(e,t,l,i),t.child)}function kh(e,t,n,l,i){if(e===null){var c=n.type;return typeof c=="function"&&!Bo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,qh(e,t,c,l,i)):(e=Di(n.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!Ac(e,i)){var h=c.memoizedProps;if(n=n.compare,n=n!==null?n:nr,n(h,l)&&e.ref===t.ref)return wn(e,t,i)}return t.flags|=1,e=_n(c,l),e.ref=t.ref,e.return=t,t.child=e}function qh(e,t,n,l,i){if(e!==null){var c=e.memoizedProps;if(nr(c,l)&&e.ref===t.ref)if(rt=!1,t.pendingProps=l=c,Ac(e,i))(e.flags&131072)!==0&&(rt=!0);else return t.lanes=e.lanes,wn(e,t,i)}return bc(e,t,n,l,i)}function $h(e,t,n){var l=t.pendingProps,i=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,e!==null){for(i=t.child=e.child,c=0;i!==null;)c=c|i.lanes|i.childLanes,i=i.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return Yh(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ui(t,c!==null?c.cachePool:null),c!==null?qd(t,c):Po(),Oh(t);else return t.lanes=t.childLanes=536870912,Yh(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Ui(t,c.cachePool),qd(t,c),ea(),t.memoizedState=null):(e!==null&&Ui(t,null),Po(),ea());return ot(e,t,i,n),t.child}function Yh(e,t,n,l){var i=Qo();return i=i===null?null:{parent:tt._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&Ui(t,null),Po(),Oh(t),e!==null&&ur(e,t,l,!0),null}function Ii(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(u(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function bc(e,t,n,l,i){return Da(t),n=tc(e,t,n,l,void 0,i),l=nc(),e!==null&&!rt?(ac(e,t,i),wn(e,t,i)):(_e&&l&&Lo(t),t.flags|=1,ot(e,t,n,i),t.child)}function Gh(e,t,n,l,i,c){return Da(t),t.updateQueue=null,n=Yd(t,l,n,i),$d(e),l=nc(),e!==null&&!rt?(ac(e,t,c),wn(e,t,c)):(_e&&l&&Lo(t),t.flags|=1,ot(e,t,n,c),t.child)}function Vh(e,t,n,l,i){if(Da(t),t.stateNode===null){var c=il,h=n.contextType;typeof h=="object"&&h!==null&&(c=vt(h)),c=new n(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=yc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},Ko(t),h=n.contextType,c.context=typeof h=="object"&&h!==null?vt(h):il,c.state=t.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(gc(t,n,h,l),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(h=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),h!==c.state&&yc.enqueueReplaceState(c,c.state,null),mr(t,l,c,i),hr(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var y=t.memoizedProps,x=ja(n,y);c.props=x;var w=c.context,Y=n.contextType;h=il,typeof Y=="object"&&Y!==null&&(h=vt(Y));var Q=n.getDerivedStateFromProps;Y=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,Y||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y||w!==h)&&zh(t,c,l,h),Jn=!1;var j=t.memoizedState;c.state=j,mr(t,l,c,i),hr(),w=t.memoizedState,y||j!==w||Jn?(typeof Q=="function"&&(gc(t,n,Q,l),w=t.memoizedState),(x=Jn||Mh(t,n,x,l,j,w,h))?(Y||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=w),c.props=l,c.state=w,c.context=h,l=x):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,Jo(e,t),h=t.memoizedProps,Y=ja(n,h),c.props=Y,Q=t.pendingProps,j=c.context,w=n.contextType,x=il,typeof w=="object"&&w!==null&&(x=vt(w)),y=n.getDerivedStateFromProps,(w=typeof y=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h!==Q||j!==x)&&zh(t,c,l,x),Jn=!1,j=t.memoizedState,c.state=j,mr(t,l,c,i),hr();var U=t.memoizedState;h!==Q||j!==U||Jn||e!==null&&e.dependencies!==null&&ji(e.dependencies)?(typeof y=="function"&&(gc(t,n,y,l),U=t.memoizedState),(Y=Jn||Mh(t,n,Y,l,j,U,x)||e!==null&&e.dependencies!==null&&ji(e.dependencies))?(w||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,U,x),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,U,x)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=U),c.props=l,c.state=U,c.context=x,l=Y):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,Ii(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=gl(t,e.child,null,i),t.child=gl(t,null,n,i)):ot(e,t,n,i),t.memoizedState=c.state,e=t.child):e=wn(e,t,i),e}function Xh(e,t,n,l){return rr(),t.flags|=256,ot(e,t,n,l),t.child}var Sc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function xc(e){return{baseLanes:e,cachePool:wd()}}function Ec(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Ft),e}function Qh(e,t,n){var l=t.pendingProps,i=!1,c=(t.flags&128)!==0,h;if((h=c)||(h=e!==null&&e.memoizedState===null?!1:(nt.current&2)!==0),h&&(i=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(_e){if(i?In(t):ea(),_e){var y=Ve,x;if(x=y){e:{for(x=y,y=sn;x.nodeType!==8;){if(!y){y=null;break e}if(x=un(x.nextSibling),x===null){y=null;break e}}y=x}y!==null?(t.memoizedState={dehydrated:y,treeContext:Aa!==null?{id:An,overflow:Rn}:null,retryLane:536870912,hydrationErrors:null},x=Nt(18,null,null,0),x.stateNode=y,x.return=t,t.child=x,Tt=t,Ve=null,x=!0):x=!1}x||Ma(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return rs(y)?t.lanes=32:t.lanes=536870912,null;Dn(t)}return y=l.children,l=l.fallback,i?(ea(),i=t.mode,y=eu({mode:"hidden",children:y},i),l=_a(l,i,n,null),y.return=t,l.return=t,y.sibling=l,t.child=y,i=t.child,i.memoizedState=xc(n),i.childLanes=Ec(e,h,n),t.memoizedState=Sc,l):(In(t),Tc(t,y))}if(x=e.memoizedState,x!==null&&(y=x.dehydrated,y!==null)){if(c)t.flags&256?(In(t),t.flags&=-257,t=Cc(e,t,n)):t.memoizedState!==null?(ea(),t.child=e.child,t.flags|=128,t=null):(ea(),i=l.fallback,y=t.mode,l=eu({mode:"visible",children:l.children},y),i=_a(i,y,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,gl(t,e.child,null,n),l=t.child,l.memoizedState=xc(n),l.childLanes=Ec(e,h,n),t.memoizedState=Sc,t=i);else if(In(t),rs(y)){if(h=y.nextSibling&&y.nextSibling.dataset,h)var w=h.dgst;h=w,l=Error(u(419)),l.stack="",l.digest=h,ir({value:l,source:null,stack:null}),t=Cc(e,t,n)}else if(rt||ur(e,t,n,!1),h=(n&e.childLanes)!==0,rt||h){if(h=ke,h!==null&&(l=n&-n,l=(l&42)!==0?1:ro(l),l=(l&(h.suspendedLanes|n))!==0?0:l,l!==0&&l!==x.retryLane))throw x.retryLane=l,rl(e,l),Lt(h,e,l),Hh;y.data==="$?"||Yc(),t=Cc(e,t,n)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=x.treeContext,Ve=un(y.nextSibling),Tt=t,_e=!0,Oa=null,sn=!1,e!==null&&(Kt[Jt++]=An,Kt[Jt++]=Rn,Kt[Jt++]=Aa,An=e.id,Rn=e.overflow,Aa=t),t=Tc(t,l.children),t.flags|=4096);return t}return i?(ea(),i=l.fallback,y=t.mode,x=e.child,w=x.sibling,l=_n(x,{mode:"hidden",children:l.children}),l.subtreeFlags=x.subtreeFlags&65011712,w!==null?i=_n(w,i):(i=_a(i,y,n,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,y=e.child.memoizedState,y===null?y=xc(n):(x=y.cachePool,x!==null?(w=tt._currentValue,x=x.parent!==w?{parent:w,pool:w}:x):x=wd(),y={baseLanes:y.baseLanes|n,cachePool:x}),i.memoizedState=y,i.childLanes=Ec(e,h,n),t.memoizedState=Sc,l):(In(t),n=e.child,e=n.sibling,n=_n(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=n,t.memoizedState=null,n)}function Tc(e,t){return t=eu({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function eu(e,t){return e=Nt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Cc(e,t,n){return gl(t,e.child,null,n),e=Tc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Zh(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Yo(e.return,t,n)}function _c(e,t,n,l,i){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=i)}function Kh(e,t,n){var l=t.pendingProps,i=l.revealOrder,c=l.tail;if(ot(e,t,l.children,n),l=nt.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zh(e,n,t);else if(e.tag===19)Zh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(F(nt,l),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Wi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),_c(t,!1,i,n,c);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Wi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}_c(t,!0,n,null,c);break;case"together":_c(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function wn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ra|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(ur(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,n=_n(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=_n(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ac(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ji(e)))}function Yy(e,t,n){switch(t.tag){case 3:Me(t,t.stateNode.containerInfo),Kn(t,tt,e.memoizedState.cache),rr();break;case 27:case 5:Sa(t);break;case 4:Me(t,t.stateNode.containerInfo);break;case 10:Kn(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(In(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Qh(e,t,n):(In(t),e=wn(e,t,n),e!==null?e.sibling:null);In(t);break;case 19:var i=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(ur(e,t,n,!1),l=(n&t.childLanes)!==0),i){if(l)return Kh(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),F(nt,nt.current),l)break;return null;case 22:case 23:return t.lanes=0,$h(e,t,n);case 24:Kn(t,tt,e.memoizedState.cache)}return wn(e,t,n)}function Jh(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)rt=!0;else{if(!Ac(e,n)&&(t.flags&128)===0)return rt=!1,Yy(e,t,n);rt=(e.flags&131072)!==0}else rt=!1,_e&&(t.flags&1048576)!==0&&_d(t,Ni,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")Bo(l)?(e=ja(l,e),t.tag=1,t=Vh(null,t,l,e,n)):(t.tag=0,t=bc(null,t,l,e,n));else{if(l!=null){if(i=l.$$typeof,i===q){t.tag=11,t=Lh(null,t,l,e,n);break e}else if(i===P){t.tag=14,t=kh(null,t,l,e,n);break e}}throw t=Ee(l)||l,Error(u(306,t,""))}}return t;case 0:return bc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,i=ja(l,t.pendingProps),Vh(e,t,l,i,n);case 3:e:{if(Me(t,t.stateNode.containerInfo),e===null)throw Error(u(387));l=t.pendingProps;var c=t.memoizedState;i=c.element,Jo(e,t),mr(t,l,null,n);var h=t.memoizedState;if(l=h.cache,Kn(t,tt,l),l!==c.cache&&Go(t,[tt],n,!0),hr(),l=h.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Xh(e,t,l,n);break e}else if(l!==i){i=Qt(Error(u(424)),t),ir(i),t=Xh(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ve=un(e.firstChild),Tt=t,_e=!0,Oa=null,sn=!0,n=Rh(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(rr(),l===i){t=wn(e,t,n);break e}ot(e,t,l,n)}t=t.child}return t;case 26:return Ii(e,t),e===null?(n=Im(t.type,null,t.pendingProps,null))?t.memoizedState=n:_e||(n=t.type,e=t.pendingProps,l=mu(re.current).createElement(n),l[yt]=t,l[_t]=e,st(l,n,e),lt(l),t.stateNode=l):t.memoizedState=Im(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Sa(t),e===null&&_e&&(l=t.stateNode=Wm(t.type,t.pendingProps,re.current),Tt=t,sn=!0,i=Ve,ca(t.type)?(is=i,Ve=un(l.firstChild)):Ve=i),ot(e,t,t.pendingProps.children,n),Ii(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&_e&&((i=l=Ve)&&(l=gv(l,t.type,t.pendingProps,sn),l!==null?(t.stateNode=l,Tt=t,Ve=un(l.firstChild),sn=!1,i=!0):i=!1),i||Ma(t)),Sa(t),i=t.type,c=t.pendingProps,h=e!==null?e.memoizedProps:null,l=c.children,ns(i,c)?l=null:h!==null&&ns(i,h)&&(t.flags|=32),t.memoizedState!==null&&(i=tc(e,t,jy,null,null,n),Ur._currentValue=i),Ii(e,t),ot(e,t,l,n),t.child;case 6:return e===null&&_e&&((e=n=Ve)&&(n=yv(n,t.pendingProps,sn),n!==null?(t.stateNode=n,Tt=t,Ve=null,e=!0):e=!1),e||Ma(t)),null;case 13:return Qh(e,t,n);case 4:return Me(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=gl(t,null,l,n):ot(e,t,l,n),t.child;case 11:return Lh(e,t,t.type,t.pendingProps,n);case 7:return ot(e,t,t.pendingProps,n),t.child;case 8:return ot(e,t,t.pendingProps.children,n),t.child;case 12:return ot(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,Kn(t,t.type,l.value),ot(e,t,l.children,n),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,Da(t),i=vt(i),l=l(i),t.flags|=1,ot(e,t,l,n),t.child;case 14:return kh(e,t,t.type,t.pendingProps,n);case 15:return qh(e,t,t.type,t.pendingProps,n);case 19:return Kh(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=eu(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=_n(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return $h(e,t,n);case 24:return Da(t),l=vt(tt),e===null?(i=Qo(),i===null&&(i=ke,c=Vo(),i.pooledCache=c,c.refCount++,c!==null&&(i.pooledCacheLanes|=n),i=c),t.memoizedState={parent:l,cache:i},Ko(t),Kn(t,tt,i)):((e.lanes&n)!==0&&(Jo(e,t),mr(t,null,null,n),hr()),i=e.memoizedState,c=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),Kn(t,tt,l)):(l=c.cache,Kn(t,tt,l),l!==i.cache&&Go(t,[tt],n,!0))),ot(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function Nn(e){e.flags|=4}function Wh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!lp(t)){if(t=Wt.current,t!==null&&((xe&4194048)===xe?fn!==null:(xe&62914560)!==xe&&(xe&536870912)===0||t!==fn))throw fr=Zo,Nd;e.flags|=8192}}function tu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Of():536870912,e.lanes|=t,Sl|=t)}function xr(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ge(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function Gy(e,t,n){var l=t.pendingProps;switch(ko(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ge(t),null;case 1:return Ge(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Mn(tt),Dt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(lr(t)?Nn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Od())),Ge(t),null;case 26:return n=t.memoizedState,e===null?(Nn(t),n!==null?(Ge(t),Wh(t,n)):(Ge(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Nn(t),Ge(t),Wh(t,n)):(Ge(t),t.flags&=-16777217):(e.memoizedProps!==l&&Nn(t),Ge(t),t.flags&=-16777217),null;case 27:Yn(t),n=re.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Nn(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Ge(t),null}e=ie.current,lr(t)?Ad(t):(e=Wm(i,l,n),t.stateNode=e,Nn(t))}return Ge(t),null;case 5:if(Yn(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Nn(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Ge(t),null}if(e=ie.current,lr(t))Ad(t);else{switch(i=mu(re.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}e[yt]=t,e[_t]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(st(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Nn(t)}}return Ge(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Nn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(u(166));if(e=re.current,lr(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,i=Tt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[yt]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Gm(e.nodeValue,n)),e||Ma(t)}else e=mu(e).createTextNode(l),e[yt]=t,t.stateNode=e}return Ge(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=lr(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(u(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(u(317));i[yt]=t}else rr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ge(t),i=!1}else i=Od(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(Dn(t),t):(Dn(t),null)}if(Dn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==i&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),tu(t,t.updateQueue),Ge(t),null;case 4:return Dt(),e===null&&Fc(t.stateNode.containerInfo),Ge(t),null;case 10:return Mn(t.type),Ge(t),null;case 19:if(J(nt),i=t.memoizedState,i===null)return Ge(t),null;if(l=(t.flags&128)!==0,c=i.rendering,c===null)if(l)xr(i,!1);else{if(Xe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Wi(e),c!==null){for(t.flags|=128,xr(i,!1),e=c.updateQueue,t.updateQueue=e,tu(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Cd(n,e),n=n.sibling;return F(nt,nt.current&1|2),t.child}e=e.sibling}i.tail!==null&&at()>lu&&(t.flags|=128,l=!0,xr(i,!1),t.lanes=4194304)}else{if(!l)if(e=Wi(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,tu(t,e),xr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!c.alternate&&!_e)return Ge(t),null}else 2*at()-i.renderingStartTime>lu&&n!==536870912&&(t.flags|=128,l=!0,xr(i,!1),t.lanes=4194304);i.isBackwards?(c.sibling=t.child,t.child=c):(e=i.last,e!==null?e.sibling=c:t.child=c,i.last=c)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=at(),t.sibling=null,e=nt.current,F(nt,l?e&1|2:e&1),t):(Ge(t),null);case 22:case 23:return Dn(t),Io(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(Ge(t),t.subtreeFlags&6&&(t.flags|=8192)):Ge(t),n=t.updateQueue,n!==null&&tu(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&J(wa),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Mn(tt),Ge(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function Vy(e,t){switch(ko(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Mn(tt),Dt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Yn(t),null;case 13:if(Dn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));rr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(nt),null;case 4:return Dt(),null;case 10:return Mn(t.type),null;case 22:case 23:return Dn(t),Io(),e!==null&&J(wa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Mn(tt),null;case 25:return null;default:return null}}function Fh(e,t){switch(ko(t),t.tag){case 3:Mn(tt),Dt();break;case 26:case 27:case 5:Yn(t);break;case 4:Dt();break;case 13:Dn(t);break;case 19:J(nt);break;case 10:Mn(t.type);break;case 22:case 23:Dn(t),Io(),e!==null&&J(wa);break;case 24:Mn(tt)}}function Er(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){l=void 0;var c=n.create,h=n.inst;l=c(),h.destroy=l}n=n.next}while(n!==i)}}catch(y){Ue(t,t.return,y)}}function ta(e,t,n){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var c=i.next;l=c;do{if((l.tag&e)===e){var h=l.inst,y=h.destroy;if(y!==void 0){h.destroy=void 0,i=t;var x=n,w=y;try{w()}catch(Y){Ue(i,x,Y)}}}l=l.next}while(l!==c)}}catch(Y){Ue(t,t.return,Y)}}function Ph(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{kd(t,n)}catch(l){Ue(e,e.return,l)}}}function Ih(e,t,n){n.props=ja(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){Ue(e,t,l)}}function Tr(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(i){Ue(e,t,i)}}function dn(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){Ue(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){Ue(e,t,i)}else n.current=null}function em(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){Ue(e,e.return,i)}}function Rc(e,t,n){try{var l=e.stateNode;fv(l,e.type,n,t),l[_t]=t}catch(i){Ue(e,e.return,i)}}function tm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ca(e.type)||e.tag===4}function Oc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||tm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ca(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Mc(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=hu));else if(l!==4&&(l===27&&ca(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Mc(e,t,n),e=e.sibling;e!==null;)Mc(e,t,n),e=e.sibling}function nu(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&ca(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(nu(e,t,n),e=e.sibling;e!==null;)nu(e,t,n),e=e.sibling}function nm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);st(t,l,n),t[yt]=e,t[_t]=n}catch(c){Ue(e,e.return,c)}}var jn=!1,Je=!1,zc=!1,am=typeof WeakSet=="function"?WeakSet:Set,it=null;function Xy(e,t){if(e=e.containerInfo,es=Su,e=md(e),Oo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var h=0,y=-1,x=-1,w=0,Y=0,Q=e,j=null;t:for(;;){for(var U;Q!==n||i!==0&&Q.nodeType!==3||(y=h+i),Q!==c||l!==0&&Q.nodeType!==3||(x=h+l),Q.nodeType===3&&(h+=Q.nodeValue.length),(U=Q.firstChild)!==null;)j=Q,Q=U;for(;;){if(Q===e)break t;if(j===n&&++w===i&&(y=h),j===c&&++Y===l&&(x=h),(U=Q.nextSibling)!==null)break;Q=j,j=Q.parentNode}Q=U}n=y===-1||x===-1?null:{start:y,end:x}}else n=null}n=n||{start:0,end:0}}else n=null;for(ts={focusedElem:e,selectionRange:n},Su=!1,it=t;it!==null;)if(t=it,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,it=e;else for(;it!==null;){switch(t=it,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,i=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var he=ja(n.type,i,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(he,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(fe){Ue(n,n.return,fe)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)ls(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ls(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,it=e;break}it=t.return}}function lm(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:na(e,n),l&4&&Er(5,n);break;case 1:if(na(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(h){Ue(n,n.return,h)}else{var i=ja(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Ue(n,n.return,h)}}l&64&&Ph(n),l&512&&Tr(n,n.return);break;case 3:if(na(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{kd(e,t)}catch(h){Ue(n,n.return,h)}}break;case 27:t===null&&l&4&&nm(n);case 26:case 5:na(e,n),t===null&&l&4&&em(n),l&512&&Tr(n,n.return);break;case 12:na(e,n);break;case 13:na(e,n),l&4&&um(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=ev.bind(null,n),vv(e,n))));break;case 22:if(l=n.memoizedState!==null||jn,!l){t=t!==null&&t.memoizedState!==null||Je,i=jn;var c=Je;jn=l,(Je=t)&&!c?aa(e,n,(n.subtreeFlags&8772)!==0):na(e,n),jn=i,Je=c}break;case 30:break;default:na(e,n)}}function rm(e){var t=e.alternate;t!==null&&(e.alternate=null,rm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&oo(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var qe=null,Ot=!1;function Bn(e,t,n){for(n=n.child;n!==null;)im(e,t,n),n=n.sibling}function im(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(Xn,n)}catch{}switch(n.tag){case 26:Je||dn(n,t),Bn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Je||dn(n,t);var l=qe,i=Ot;ca(n.type)&&(qe=n.stateNode,Ot=!1),Bn(e,t,n),wr(n.stateNode),qe=l,Ot=i;break;case 5:Je||dn(n,t);case 6:if(l=qe,i=Ot,qe=null,Bn(e,t,n),qe=l,Ot=i,qe!==null)if(Ot)try{(qe.nodeType===9?qe.body:qe.nodeName==="HTML"?qe.ownerDocument.body:qe).removeChild(n.stateNode)}catch(c){Ue(n,t,c)}else try{qe.removeChild(n.stateNode)}catch(c){Ue(n,t,c)}break;case 18:qe!==null&&(Ot?(e=qe,Km(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),qr(e)):Km(qe,n.stateNode));break;case 4:l=qe,i=Ot,qe=n.stateNode.containerInfo,Ot=!0,Bn(e,t,n),qe=l,Ot=i;break;case 0:case 11:case 14:case 15:Je||ta(2,n,t),Je||ta(4,n,t),Bn(e,t,n);break;case 1:Je||(dn(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Ih(n,t,l)),Bn(e,t,n);break;case 21:Bn(e,t,n);break;case 22:Je=(l=Je)||n.memoizedState!==null,Bn(e,t,n),Je=l;break;default:Bn(e,t,n)}}function um(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{qr(e)}catch(n){Ue(t,t.return,n)}}function Qy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new am),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new am),t;default:throw Error(u(435,e.tag))}}function Dc(e,t){var n=Qy(e);t.forEach(function(l){var i=tv.bind(null,e,l);n.has(l)||(n.add(l),l.then(i,i))})}function jt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],c=e,h=t,y=h;e:for(;y!==null;){switch(y.tag){case 27:if(ca(y.type)){qe=y.stateNode,Ot=!1;break e}break;case 5:qe=y.stateNode,Ot=!1;break e;case 3:case 4:qe=y.stateNode.containerInfo,Ot=!0;break e}y=y.return}if(qe===null)throw Error(u(160));im(c,h,i),qe=null,Ot=!1,c=i.alternate,c!==null&&(c.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)om(t,e),t=t.sibling}var rn=null;function om(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:jt(t,e),Bt(e),l&4&&(ta(3,e,e.return),Er(3,e),ta(5,e,e.return));break;case 1:jt(t,e),Bt(e),l&512&&(Je||n===null||dn(n,n.return)),l&64&&jn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=rn;if(jt(t,e),Bt(e),l&512&&(Je||n===null||dn(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":c=i.getElementsByTagName("title")[0],(!c||c[Zl]||c[yt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=i.createElement(l),i.head.insertBefore(c,i.querySelector("head > title"))),st(c,l,n),c[yt]=e,lt(c),l=c;break e;case"link":var h=np("link","href",i).get(l+(n.href||""));if(h){for(var y=0;y<h.length;y++)if(c=h[y],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(y,1);break t}}c=i.createElement(l),st(c,l,n),i.head.appendChild(c);break;case"meta":if(h=np("meta","content",i).get(l+(n.content||""))){for(y=0;y<h.length;y++)if(c=h[y],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(y,1);break t}}c=i.createElement(l),st(c,l,n),i.head.appendChild(c);break;default:throw Error(u(468,l))}c[yt]=e,lt(c),l=c}e.stateNode=l}else ap(i,e.type,e.stateNode);else e.stateNode=tp(i,l,e.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?ap(i,e.type,e.stateNode):tp(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Rc(e,e.memoizedProps,n.memoizedProps)}break;case 27:jt(t,e),Bt(e),l&512&&(Je||n===null||dn(n,n.return)),n!==null&&l&4&&Rc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(jt(t,e),Bt(e),l&512&&(Je||n===null||dn(n,n.return)),e.flags&32){i=e.stateNode;try{Pa(i,"")}catch(U){Ue(e,e.return,U)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,Rc(e,i,n!==null?n.memoizedProps:i)),l&1024&&(zc=!0);break;case 6:if(jt(t,e),Bt(e),l&4){if(e.stateNode===null)throw Error(u(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(U){Ue(e,e.return,U)}}break;case 3:if(yu=null,i=rn,rn=pu(t.containerInfo),jt(t,e),rn=i,Bt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{qr(t.containerInfo)}catch(U){Ue(e,e.return,U)}zc&&(zc=!1,cm(e));break;case 4:l=rn,rn=pu(e.stateNode.containerInfo),jt(t,e),Bt(e),rn=l;break;case 12:jt(t,e),Bt(e);break;case 13:jt(t,e),Bt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Hc=at()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Dc(e,l)));break;case 22:i=e.memoizedState!==null;var x=n!==null&&n.memoizedState!==null,w=jn,Y=Je;if(jn=w||i,Je=Y||x,jt(t,e),Je=Y,jn=w,Bt(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||x||jn||Je||Ba(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){x=n=t;try{if(c=x.stateNode,i)h=c.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{y=x.stateNode;var Q=x.memoizedProps.style,j=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;y.style.display=j==null||typeof j=="boolean"?"":(""+j).trim()}}catch(U){Ue(x,x.return,U)}}}else if(t.tag===6){if(n===null){x=t;try{x.stateNode.nodeValue=i?"":x.memoizedProps}catch(U){Ue(x,x.return,U)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,Dc(e,n))));break;case 19:jt(t,e),Bt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Dc(e,l)));break;case 30:break;case 21:break;default:jt(t,e),Bt(e)}}function Bt(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(tm(l)){n=l;break}l=l.return}if(n==null)throw Error(u(160));switch(n.tag){case 27:var i=n.stateNode,c=Oc(e);nu(e,c,i);break;case 5:var h=n.stateNode;n.flags&32&&(Pa(h,""),n.flags&=-33);var y=Oc(e);nu(e,y,h);break;case 3:case 4:var x=n.stateNode.containerInfo,w=Oc(e);Mc(e,w,x);break;default:throw Error(u(161))}}catch(Y){Ue(e,e.return,Y)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function cm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;cm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function na(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)lm(e,t.alternate,t),t=t.sibling}function Ba(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ta(4,t,t.return),Ba(t);break;case 1:dn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Ih(t,t.return,n),Ba(t);break;case 27:wr(t.stateNode);case 26:case 5:dn(t,t.return),Ba(t);break;case 22:t.memoizedState===null&&Ba(t);break;case 30:Ba(t);break;default:Ba(t)}e=e.sibling}}function aa(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,c=t,h=c.flags;switch(c.tag){case 0:case 11:case 15:aa(i,c,n),Er(4,c);break;case 1:if(aa(i,c,n),l=c,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(w){Ue(l,l.return,w)}if(l=c,i=l.updateQueue,i!==null){var y=l.stateNode;try{var x=i.shared.hiddenCallbacks;if(x!==null)for(i.shared.hiddenCallbacks=null,i=0;i<x.length;i++)Ld(x[i],y)}catch(w){Ue(l,l.return,w)}}n&&h&64&&Ph(c),Tr(c,c.return);break;case 27:nm(c);case 26:case 5:aa(i,c,n),n&&l===null&&h&4&&em(c),Tr(c,c.return);break;case 12:aa(i,c,n);break;case 13:aa(i,c,n),n&&h&4&&um(i,c);break;case 22:c.memoizedState===null&&aa(i,c,n),Tr(c,c.return);break;case 30:break;default:aa(i,c,n)}t=t.sibling}}function wc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&or(n))}function Nc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&or(e))}function hn(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)sm(e,t,n,l),t=t.sibling}function sm(e,t,n,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:hn(e,t,n,l),i&2048&&Er(9,t);break;case 1:hn(e,t,n,l);break;case 3:hn(e,t,n,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&or(e)));break;case 12:if(i&2048){hn(e,t,n,l),e=t.stateNode;try{var c=t.memoizedProps,h=c.id,y=c.onPostCommit;typeof y=="function"&&y(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(x){Ue(t,t.return,x)}}else hn(e,t,n,l);break;case 13:hn(e,t,n,l);break;case 23:break;case 22:c=t.stateNode,h=t.alternate,t.memoizedState!==null?c._visibility&2?hn(e,t,n,l):Cr(e,t):c._visibility&2?hn(e,t,n,l):(c._visibility|=2,yl(e,t,n,l,(t.subtreeFlags&10256)!==0)),i&2048&&wc(h,t);break;case 24:hn(e,t,n,l),i&2048&&Nc(t.alternate,t);break;default:hn(e,t,n,l)}}function yl(e,t,n,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,h=t,y=n,x=l,w=h.flags;switch(h.tag){case 0:case 11:case 15:yl(c,h,y,x,i),Er(8,h);break;case 23:break;case 22:var Y=h.stateNode;h.memoizedState!==null?Y._visibility&2?yl(c,h,y,x,i):Cr(c,h):(Y._visibility|=2,yl(c,h,y,x,i)),i&&w&2048&&wc(h.alternate,h);break;case 24:yl(c,h,y,x,i),i&&w&2048&&Nc(h.alternate,h);break;default:yl(c,h,y,x,i)}t=t.sibling}}function Cr(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,i=l.flags;switch(l.tag){case 22:Cr(n,l),i&2048&&wc(l.alternate,l);break;case 24:Cr(n,l),i&2048&&Nc(l.alternate,l);break;default:Cr(n,l)}t=t.sibling}}var _r=8192;function vl(e){if(e.subtreeFlags&_r)for(e=e.child;e!==null;)fm(e),e=e.sibling}function fm(e){switch(e.tag){case 26:vl(e),e.flags&_r&&e.memoizedState!==null&&Dv(rn,e.memoizedState,e.memoizedProps);break;case 5:vl(e);break;case 3:case 4:var t=rn;rn=pu(e.stateNode.containerInfo),vl(e),rn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=_r,_r=16777216,vl(e),_r=t):vl(e));break;default:vl(e)}}function dm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ar(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];it=l,mm(l,e)}dm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)hm(e),e=e.sibling}function hm(e){switch(e.tag){case 0:case 11:case 15:Ar(e),e.flags&2048&&ta(9,e,e.return);break;case 3:Ar(e);break;case 12:Ar(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,au(e)):Ar(e);break;default:Ar(e)}}function au(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];it=l,mm(l,e)}dm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ta(8,t,t.return),au(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,au(t));break;default:au(t)}e=e.sibling}}function mm(e,t){for(;it!==null;){var n=it;switch(n.tag){case 0:case 11:case 15:ta(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:or(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,it=l;else e:for(n=e;it!==null;){l=it;var i=l.sibling,c=l.return;if(rm(l),l===n){it=null;break e}if(i!==null){i.return=c,it=i;break e}it=c}}}var Zy={getCacheForType:function(e){var t=vt(tt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Ky=typeof WeakMap=="function"?WeakMap:Map,ze=0,ke=null,be=null,xe=0,De=0,Ut=null,la=!1,bl=!1,jc=!1,Un=0,Xe=0,ra=0,Ua=0,Bc=0,Ft=0,Sl=0,Rr=null,Mt=null,Uc=!1,Hc=0,lu=1/0,ru=null,ia=null,ct=0,ua=null,xl=null,El=0,Lc=0,kc=null,pm=null,Or=0,qc=null;function Ht(){if((ze&2)!==0&&xe!==0)return xe&-xe;if(H.T!==null){var e=cl;return e!==0?e:Zc()}return Df()}function gm(){Ft===0&&(Ft=(xe&536870912)===0||_e?Rf():536870912);var e=Wt.current;return e!==null&&(e.flags|=32),Ft}function Lt(e,t,n){(e===ke&&(De===2||De===9)||e.cancelPendingCommit!==null)&&(Tl(e,0),oa(e,xe,Ft,!1)),Ql(e,n),((ze&2)===0||e!==ke)&&(e===ke&&((ze&2)===0&&(Ua|=n),Xe===4&&oa(e,xe,Ft,!1)),mn(e))}function ym(e,t,n){if((ze&6)!==0)throw Error(u(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Xl(e,t),i=l?Fy(e,t):Gc(e,t,!0),c=l;do{if(i===0){bl&&!l&&oa(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Jy(n)){i=Gc(e,t,!1),c=!1;continue}if(i===2){if(c=t,e.errorRecoveryDisabledLanes&c)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var y=e;i=Rr;var x=y.current.memoizedState.isDehydrated;if(x&&(Tl(y,h).flags|=256),h=Gc(y,h,!1),h!==2){if(jc&&!x){y.errorRecoveryDisabledLanes|=c,Ua|=c,i=4;break e}c=Mt,Mt=i,c!==null&&(Mt===null?Mt=c:Mt.push.apply(Mt,c))}i=h}if(c=!1,i!==2)continue}}if(i===1){Tl(e,0),oa(e,t,0,!0);break}e:{switch(l=e,c=i,c){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:oa(l,t,Ft,!la);break e;case 2:Mt=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(i=Hc+300-at(),10<i)){if(oa(l,t,Ft,!la),gi(l,0,!0)!==0)break e;l.timeoutHandle=Qm(vm.bind(null,l,n,Mt,ru,Uc,t,Ft,Ua,Sl,la,c,2,-0,0),i);break e}vm(l,n,Mt,ru,Uc,t,Ft,Ua,Sl,la,c,0,-0,0)}}break}while(!0);mn(e)}function vm(e,t,n,l,i,c,h,y,x,w,Y,Q,j,U){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Br={stylesheets:null,count:0,unsuspend:zv},fm(t),Q=wv(),Q!==null)){e.cancelPendingCommit=Q(_m.bind(null,e,t,c,n,l,i,h,y,x,Y,1,j,U)),oa(e,c,h,!w);return}_m(e,t,c,n,l,i,h,y,x)}function Jy(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],c=i.getSnapshot;i=i.value;try{if(!wt(c(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function oa(e,t,n,l){t&=~Bc,t&=~Ua,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var c=31-mt(i),h=1<<c;l[c]=-1,i&=~h}n!==0&&Mf(e,n,t)}function iu(){return(ze&6)===0?(Mr(0),!1):!0}function $c(){if(be!==null){if(De===0)var e=be.return;else e=be,On=za=null,lc(e),pl=null,br=0,e=be;for(;e!==null;)Fh(e.alternate,e),e=e.return;be=null}}function Tl(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,hv(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),$c(),ke=e,be=n=_n(e.current,null),xe=t,De=0,Ut=null,la=!1,bl=Xl(e,t),jc=!1,Sl=Ft=Bc=Ua=ra=Xe=0,Mt=Rr=null,Uc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-mt(l),c=1<<i;t|=e[i],l&=~c}return Un=t,Oi(),n}function bm(e,t){ye=null,H.H=Zi,t===sr||t===Hi?(t=Ud(),De=3):t===Nd?(t=Ud(),De=4):De=t===Hh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Ut=t,be===null&&(Xe=1,Pi(e,Qt(t,e.current)))}function Sm(){var e=H.H;return H.H=Zi,e===null?Zi:e}function xm(){var e=H.A;return H.A=Zy,e}function Yc(){Xe=4,la||(xe&4194048)!==xe&&Wt.current!==null||(bl=!0),(ra&134217727)===0&&(Ua&134217727)===0||ke===null||oa(ke,xe,Ft,!1)}function Gc(e,t,n){var l=ze;ze|=2;var i=Sm(),c=xm();(ke!==e||xe!==t)&&(ru=null,Tl(e,t)),t=!1;var h=Xe;e:do try{if(De!==0&&be!==null){var y=be,x=Ut;switch(De){case 8:$c(),h=6;break e;case 3:case 2:case 9:case 6:Wt.current===null&&(t=!0);var w=De;if(De=0,Ut=null,Cl(e,y,x,w),n&&bl){h=0;break e}break;default:w=De,De=0,Ut=null,Cl(e,y,x,w)}}Wy(),h=Xe;break}catch(Y){bm(e,Y)}while(!0);return t&&e.shellSuspendCounter++,On=za=null,ze=l,H.H=i,H.A=c,be===null&&(ke=null,xe=0,Oi()),h}function Wy(){for(;be!==null;)Em(be)}function Fy(e,t){var n=ze;ze|=2;var l=Sm(),i=xm();ke!==e||xe!==t?(ru=null,lu=at()+500,Tl(e,t)):bl=Xl(e,t);e:do try{if(De!==0&&be!==null){t=be;var c=Ut;t:switch(De){case 1:De=0,Ut=null,Cl(e,t,c,1);break;case 2:case 9:if(jd(c)){De=0,Ut=null,Tm(t);break}t=function(){De!==2&&De!==9||ke!==e||(De=7),mn(e)},c.then(t,t);break e;case 3:De=7;break e;case 4:De=5;break e;case 7:jd(c)?(De=0,Ut=null,Tm(t)):(De=0,Ut=null,Cl(e,t,c,7));break;case 5:var h=null;switch(be.tag){case 26:h=be.memoizedState;case 5:case 27:var y=be;if(!h||lp(h)){De=0,Ut=null;var x=y.sibling;if(x!==null)be=x;else{var w=y.return;w!==null?(be=w,uu(w)):be=null}break t}}De=0,Ut=null,Cl(e,t,c,5);break;case 6:De=0,Ut=null,Cl(e,t,c,6);break;case 8:$c(),Xe=6;break e;default:throw Error(u(462))}}Py();break}catch(Y){bm(e,Y)}while(!0);return On=za=null,H.H=l,H.A=i,ze=n,be!==null?0:(ke=null,xe=0,Oi(),Xe)}function Py(){for(;be!==null&&!Va();)Em(be)}function Em(e){var t=Jh(e.alternate,e,Un);e.memoizedProps=e.pendingProps,t===null?uu(e):be=t}function Tm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Gh(n,t,t.pendingProps,t.type,void 0,xe);break;case 11:t=Gh(n,t,t.pendingProps,t.type.render,t.ref,xe);break;case 5:lc(t);default:Fh(n,t),t=be=Cd(t,Un),t=Jh(n,t,Un)}e.memoizedProps=e.pendingProps,t===null?uu(e):be=t}function Cl(e,t,n,l){On=za=null,lc(t),pl=null,br=0;var i=t.return;try{if($y(e,i,t,n,xe)){Xe=1,Pi(e,Qt(n,e.current)),be=null;return}}catch(c){if(i!==null)throw be=i,c;Xe=1,Pi(e,Qt(n,e.current)),be=null;return}t.flags&32768?(_e||l===1?e=!0:bl||(xe&536870912)!==0?e=!1:(la=e=!0,(l===2||l===9||l===3||l===6)&&(l=Wt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Cm(t,e)):uu(t)}function uu(e){var t=e;do{if((t.flags&32768)!==0){Cm(t,la);return}e=t.return;var n=Gy(t.alternate,t,Un);if(n!==null){be=n;return}if(t=t.sibling,t!==null){be=t;return}be=t=e}while(t!==null);Xe===0&&(Xe=5)}function Cm(e,t){do{var n=Vy(e.alternate,e);if(n!==null){n.flags&=32767,be=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){be=e;return}be=e=n}while(e!==null);Xe=6,be=null}function _m(e,t,n,l,i,c,h,y,x){e.cancelPendingCommit=null;do ou();while(ct!==0);if((ze&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(c=t.lanes|t.childLanes,c|=No,zg(e,n,c,h,y,x),e===ke&&(be=ke=null,xe=0),xl=t,ua=e,El=n,Lc=c,kc=i,pm=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,nv(Gt,function(){return zm(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=H.T,H.T=null,i=K.p,K.p=2,h=ze,ze|=4;try{Xy(e,t,n)}finally{ze=h,K.p=i,H.T=l}}ct=1,Am(),Rm(),Om()}}function Am(){if(ct===1){ct=0;var e=ua,t=xl,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=H.T,H.T=null;var l=K.p;K.p=2;var i=ze;ze|=4;try{om(t,e);var c=ts,h=md(e.containerInfo),y=c.focusedElem,x=c.selectionRange;if(h!==y&&y&&y.ownerDocument&&hd(y.ownerDocument.documentElement,y)){if(x!==null&&Oo(y)){var w=x.start,Y=x.end;if(Y===void 0&&(Y=w),"selectionStart"in y)y.selectionStart=w,y.selectionEnd=Math.min(Y,y.value.length);else{var Q=y.ownerDocument||document,j=Q&&Q.defaultView||window;if(j.getSelection){var U=j.getSelection(),he=y.textContent.length,fe=Math.min(x.start,he),je=x.end===void 0?fe:Math.min(x.end,he);!U.extend&&fe>je&&(h=je,je=fe,fe=h);var M=dd(y,fe),R=dd(y,je);if(M&&R&&(U.rangeCount!==1||U.anchorNode!==M.node||U.anchorOffset!==M.offset||U.focusNode!==R.node||U.focusOffset!==R.offset)){var D=Q.createRange();D.setStart(M.node,M.offset),U.removeAllRanges(),fe>je?(U.addRange(D),U.extend(R.node,R.offset)):(D.setEnd(R.node,R.offset),U.addRange(D))}}}}for(Q=[],U=y;U=U.parentNode;)U.nodeType===1&&Q.push({element:U,left:U.scrollLeft,top:U.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<Q.length;y++){var G=Q[y];G.element.scrollLeft=G.left,G.element.scrollTop=G.top}}Su=!!es,ts=es=null}finally{ze=i,K.p=l,H.T=n}}e.current=t,ct=2}}function Rm(){if(ct===2){ct=0;var e=ua,t=xl,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=H.T,H.T=null;var l=K.p;K.p=2;var i=ze;ze|=4;try{lm(e,t.alternate,t)}finally{ze=i,K.p=l,H.T=n}}ct=3}}function Om(){if(ct===4||ct===3){ct=0,Vl();var e=ua,t=xl,n=El,l=pm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ct=5:(ct=0,xl=ua=null,Mm(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(ia=null),io(n),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(Xn,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=H.T,i=K.p,K.p=2,H.T=null;try{for(var c=e.onRecoverableError,h=0;h<l.length;h++){var y=l[h];c(y.value,{componentStack:y.stack})}}finally{H.T=t,K.p=i}}(El&3)!==0&&ou(),mn(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===qc?Or++:(Or=0,qc=e):Or=0,Mr(0)}}function Mm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,or(t)))}function ou(e){return Am(),Rm(),Om(),zm()}function zm(){if(ct!==5)return!1;var e=ua,t=Lc;Lc=0;var n=io(El),l=H.T,i=K.p;try{K.p=32>n?32:n,H.T=null,n=kc,kc=null;var c=ua,h=El;if(ct=0,xl=ua=null,El=0,(ze&6)!==0)throw Error(u(331));var y=ze;if(ze|=4,hm(c.current),sm(c,c.current,h,n),ze=y,Mr(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(Xn,c)}catch{}return!0}finally{K.p=i,H.T=l,Mm(e,t)}}function Dm(e,t,n){t=Qt(n,t),t=vc(e.stateNode,t,2),e=Fn(e,t,2),e!==null&&(Ql(e,2),mn(e))}function Ue(e,t,n){if(e.tag===3)Dm(e,e,n);else for(;t!==null;){if(t.tag===3){Dm(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(ia===null||!ia.has(l))){e=Qt(n,e),n=Bh(2),l=Fn(t,n,2),l!==null&&(Uh(n,l,t,e),Ql(l,2),mn(l));break}}t=t.return}}function Vc(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new Ky;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(n)||(jc=!0,i.add(n),e=Iy.bind(null,e,t,n),t.then(e,e))}function Iy(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ke===e&&(xe&n)===n&&(Xe===4||Xe===3&&(xe&62914560)===xe&&300>at()-Hc?(ze&2)===0&&Tl(e,0):Bc|=n,Sl===xe&&(Sl=0)),mn(e)}function wm(e,t){t===0&&(t=Of()),e=rl(e,t),e!==null&&(Ql(e,t),mn(e))}function ev(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),wm(e,n)}function tv(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(t),wm(e,n)}function nv(e,t){return Vn(e,t)}var cu=null,_l=null,Xc=!1,su=!1,Qc=!1,Ha=0;function mn(e){e!==_l&&e.next===null&&(_l===null?cu=_l=e:_l=_l.next=e),su=!0,Xc||(Xc=!0,lv())}function Mr(e,t){if(!Qc&&su){Qc=!0;do for(var n=!1,l=cu;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var c=0;else{var h=l.suspendedLanes,y=l.pingedLanes;c=(1<<31-mt(42|e)+1)-1,c&=i&~(h&~y),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,Um(l,c))}else c=xe,c=gi(l,l===ke?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Xl(l,c)||(n=!0,Um(l,c));l=l.next}while(n);Qc=!1}}function av(){Nm()}function Nm(){su=Xc=!1;var e=0;Ha!==0&&(dv()&&(e=Ha),Ha=0);for(var t=at(),n=null,l=cu;l!==null;){var i=l.next,c=jm(l,t);c===0?(l.next=null,n===null?cu=i:n.next=i,i===null&&(_l=n)):(n=l,(e!==0||(c&3)!==0)&&(su=!0)),l=i}Mr(e)}function jm(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var h=31-mt(c),y=1<<h,x=i[h];x===-1?((y&n)===0||(y&l)!==0)&&(i[h]=Mg(y,t)):x<=t&&(e.expiredLanes|=y),c&=~y}if(t=ke,n=xe,n=gi(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(De===2||De===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&on(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Xl(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&on(l),io(n)){case 2:case 8:n=cn;break;case 32:n=Gt;break;case 268435456:n=hi;break;default:n=Gt}return l=Bm.bind(null,e),n=Vn(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&on(l),e.callbackPriority=2,e.callbackNode=null,2}function Bm(e,t){if(ct!==0&&ct!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ou()&&e.callbackNode!==n)return null;var l=xe;return l=gi(e,e===ke?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(ym(e,l,t),jm(e,at()),e.callbackNode!=null&&e.callbackNode===n?Bm.bind(null,e):null)}function Um(e,t){if(ou())return null;ym(e,t,!0)}function lv(){mv(function(){(ze&6)!==0?Vn(dt,av):Nm()})}function Zc(){return Ha===0&&(Ha=Rf()),Ha}function Hm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:xi(""+e)}function Lm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function rv(e,t,n,l,i){if(t==="submit"&&n&&n.stateNode===i){var c=Hm((i[_t]||null).action),h=l.submitter;h&&(t=(t=h[_t]||null)?Hm(t.formAction):h.getAttribute("formAction"),t!==null&&(c=t,h=null));var y=new _i("action","action",null,l,i);e.push({event:y,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Ha!==0){var x=h?Lm(i,h):new FormData(i);hc(n,{pending:!0,data:x,method:i.method,action:c},null,x)}}else typeof c=="function"&&(y.preventDefault(),x=h?Lm(i,h):new FormData(i),hc(n,{pending:!0,data:x,method:i.method,action:c},c,x))},currentTarget:i}]})}}for(var Kc=0;Kc<wo.length;Kc++){var Jc=wo[Kc],iv=Jc.toLowerCase(),uv=Jc[0].toUpperCase()+Jc.slice(1);ln(iv,"on"+uv)}ln(yd,"onAnimationEnd"),ln(vd,"onAnimationIteration"),ln(bd,"onAnimationStart"),ln("dblclick","onDoubleClick"),ln("focusin","onFocus"),ln("focusout","onBlur"),ln(Cy,"onTransitionRun"),ln(_y,"onTransitionStart"),ln(Ay,"onTransitionCancel"),ln(Sd,"onTransitionEnd"),Ja("onMouseEnter",["mouseout","mouseover"]),Ja("onMouseLeave",["mouseout","mouseover"]),Ja("onPointerEnter",["pointerout","pointerover"]),Ja("onPointerLeave",["pointerout","pointerover"]),xa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),xa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),xa("onBeforeInput",["compositionend","keypress","textInput","paste"]),xa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),xa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),xa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ov=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zr));function km(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],i=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var h=l.length-1;0<=h;h--){var y=l[h],x=y.instance,w=y.currentTarget;if(y=y.listener,x!==c&&i.isPropagationStopped())break e;c=y,i.currentTarget=w;try{c(i)}catch(Y){Fi(Y)}i.currentTarget=null,c=x}else for(h=0;h<l.length;h++){if(y=l[h],x=y.instance,w=y.currentTarget,y=y.listener,x!==c&&i.isPropagationStopped())break e;c=y,i.currentTarget=w;try{c(i)}catch(Y){Fi(Y)}i.currentTarget=null,c=x}}}}function Se(e,t){var n=t[uo];n===void 0&&(n=t[uo]=new Set);var l=e+"__bubble";n.has(l)||(qm(t,e,2,!1),n.add(l))}function Wc(e,t,n){var l=0;t&&(l|=4),qm(n,e,l,t)}var fu="_reactListening"+Math.random().toString(36).slice(2);function Fc(e){if(!e[fu]){e[fu]=!0,Nf.forEach(function(n){n!=="selectionchange"&&(ov.has(n)||Wc(n,!1,e),Wc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[fu]||(t[fu]=!0,Wc("selectionchange",!1,t))}}function qm(e,t,n,l){switch(sp(t)){case 2:var i=Bv;break;case 8:i=Uv;break;default:i=fs}n=i.bind(null,t,n,e),i=void 0,!bo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Pc(e,t,n,l,i){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var y=l.stateNode.containerInfo;if(y===i)break;if(h===4)for(h=l.return;h!==null;){var x=h.tag;if((x===3||x===4)&&h.stateNode.containerInfo===i)return;h=h.return}for(;y!==null;){if(h=Qa(y),h===null)return;if(x=h.tag,x===5||x===6||x===26||x===27){l=c=h;continue e}y=y.parentNode}}l=l.return}Zf(function(){var w=c,Y=yo(n),Q=[];e:{var j=xd.get(e);if(j!==void 0){var U=_i,he=e;switch(e){case"keypress":if(Ti(n)===0)break e;case"keydown":case"keyup":U=ny;break;case"focusin":he="focus",U=To;break;case"focusout":he="blur",U=To;break;case"beforeblur":case"afterblur":U=To;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=Wf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=Vg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=ry;break;case yd:case vd:case bd:U=Zg;break;case Sd:U=uy;break;case"scroll":case"scrollend":U=Yg;break;case"wheel":U=cy;break;case"copy":case"cut":case"paste":U=Jg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=Pf;break;case"toggle":case"beforetoggle":U=fy}var fe=(t&4)!==0,je=!fe&&(e==="scroll"||e==="scrollend"),M=fe?j!==null?j+"Capture":null:j;fe=[];for(var R=w,D;R!==null;){var G=R;if(D=G.stateNode,G=G.tag,G!==5&&G!==26&&G!==27||D===null||M===null||(G=Jl(R,M),G!=null&&fe.push(Dr(R,G,D))),je)break;R=R.return}0<fe.length&&(j=new U(j,he,null,n,Y),Q.push({event:j,listeners:fe}))}}if((t&7)===0){e:{if(j=e==="mouseover"||e==="pointerover",U=e==="mouseout"||e==="pointerout",j&&n!==go&&(he=n.relatedTarget||n.fromElement)&&(Qa(he)||he[Xa]))break e;if((U||j)&&(j=Y.window===Y?Y:(j=Y.ownerDocument)?j.defaultView||j.parentWindow:window,U?(he=n.relatedTarget||n.toElement,U=w,he=he?Qa(he):null,he!==null&&(je=f(he),fe=he.tag,he!==je||fe!==5&&fe!==27&&fe!==6)&&(he=null)):(U=null,he=w),U!==he)){if(fe=Wf,G="onMouseLeave",M="onMouseEnter",R="mouse",(e==="pointerout"||e==="pointerover")&&(fe=Pf,G="onPointerLeave",M="onPointerEnter",R="pointer"),je=U==null?j:Kl(U),D=he==null?j:Kl(he),j=new fe(G,R+"leave",U,n,Y),j.target=je,j.relatedTarget=D,G=null,Qa(Y)===w&&(fe=new fe(M,R+"enter",he,n,Y),fe.target=D,fe.relatedTarget=je,G=fe),je=G,U&&he)t:{for(fe=U,M=he,R=0,D=fe;D;D=Al(D))R++;for(D=0,G=M;G;G=Al(G))D++;for(;0<R-D;)fe=Al(fe),R--;for(;0<D-R;)M=Al(M),D--;for(;R--;){if(fe===M||M!==null&&fe===M.alternate)break t;fe=Al(fe),M=Al(M)}fe=null}else fe=null;U!==null&&$m(Q,j,U,fe,!1),he!==null&&je!==null&&$m(Q,je,he,fe,!0)}}e:{if(j=w?Kl(w):window,U=j.nodeName&&j.nodeName.toLowerCase(),U==="select"||U==="input"&&j.type==="file")var ae=id;else if(ld(j))if(ud)ae=xy;else{ae=by;var ve=vy}else U=j.nodeName,!U||U.toLowerCase()!=="input"||j.type!=="checkbox"&&j.type!=="radio"?w&&po(w.elementType)&&(ae=id):ae=Sy;if(ae&&(ae=ae(e,w))){rd(Q,ae,n,Y);break e}ve&&ve(e,j,w),e==="focusout"&&w&&j.type==="number"&&w.memoizedProps.value!=null&&mo(j,"number",j.value)}switch(ve=w?Kl(w):window,e){case"focusin":(ld(ve)||ve.contentEditable==="true")&&(nl=ve,Mo=w,ar=null);break;case"focusout":ar=Mo=nl=null;break;case"mousedown":zo=!0;break;case"contextmenu":case"mouseup":case"dragend":zo=!1,pd(Q,n,Y);break;case"selectionchange":if(Ty)break;case"keydown":case"keyup":pd(Q,n,Y)}var ue;if(_o)e:{switch(e){case"compositionstart":var de="onCompositionStart";break e;case"compositionend":de="onCompositionEnd";break e;case"compositionupdate":de="onCompositionUpdate";break e}de=void 0}else tl?nd(e,n)&&(de="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(de="onCompositionStart");de&&(If&&n.locale!=="ko"&&(tl||de!=="onCompositionStart"?de==="onCompositionEnd"&&tl&&(ue=Kf()):(Zn=Y,So="value"in Zn?Zn.value:Zn.textContent,tl=!0)),ve=du(w,de),0<ve.length&&(de=new Ff(de,e,null,n,Y),Q.push({event:de,listeners:ve}),ue?de.data=ue:(ue=ad(n),ue!==null&&(de.data=ue)))),(ue=hy?my(e,n):py(e,n))&&(de=du(w,"onBeforeInput"),0<de.length&&(ve=new Ff("onBeforeInput","beforeinput",null,n,Y),Q.push({event:ve,listeners:de}),ve.data=ue)),rv(Q,e,w,n,Y)}km(Q,t)})}function Dr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function du(e,t){for(var n=t+"Capture",l=[];e!==null;){var i=e,c=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||c===null||(i=Jl(e,n),i!=null&&l.unshift(Dr(e,i,c)),i=Jl(e,t),i!=null&&l.push(Dr(e,i,c))),e.tag===3)return l;e=e.return}return[]}function Al(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function $m(e,t,n,l,i){for(var c=t._reactName,h=[];n!==null&&n!==l;){var y=n,x=y.alternate,w=y.stateNode;if(y=y.tag,x!==null&&x===l)break;y!==5&&y!==26&&y!==27||w===null||(x=w,i?(w=Jl(n,c),w!=null&&h.unshift(Dr(n,w,x))):i||(w=Jl(n,c),w!=null&&h.push(Dr(n,w,x)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var cv=/\r\n?/g,sv=/\u0000|\uFFFD/g;function Ym(e){return(typeof e=="string"?e:""+e).replace(cv,`
`).replace(sv,"")}function Gm(e,t){return t=Ym(t),Ym(e)===t}function hu(){}function Ne(e,t,n,l,i,c){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Pa(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Pa(e,""+l);break;case"className":vi(e,"class",l);break;case"tabIndex":vi(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":vi(e,n,l);break;case"style":Xf(e,l,c);break;case"data":if(t!=="object"){vi(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=xi(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Ne(e,t,"name",i.name,i,null),Ne(e,t,"formEncType",i.formEncType,i,null),Ne(e,t,"formMethod",i.formMethod,i,null),Ne(e,t,"formTarget",i.formTarget,i,null)):(Ne(e,t,"encType",i.encType,i,null),Ne(e,t,"method",i.method,i,null),Ne(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=xi(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=hu);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(u(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=xi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Se("beforetoggle",e),Se("toggle",e),yi(e,"popover",l);break;case"xlinkActuate":Tn(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Tn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Tn(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Tn(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Tn(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Tn(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Tn(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Tn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Tn(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":yi(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=qg.get(n)||n,yi(e,n,l))}}function Ic(e,t,n,l,i,c){switch(n){case"style":Xf(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(u(60));e.innerHTML=n}}break;case"children":typeof l=="string"?Pa(e,l):(typeof l=="number"||typeof l=="bigint")&&Pa(e,""+l);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"onClick":l!=null&&(e.onclick=hu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!jf.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),c=e[_t]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,i),typeof l=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,i);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):yi(e,n,l)}}}function st(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Se("error",e),Se("load",e);var l=!1,i=!1,c;for(c in n)if(n.hasOwnProperty(c)){var h=n[c];if(h!=null)switch(c){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ne(e,t,c,h,n,null)}}i&&Ne(e,t,"srcSet",n.srcSet,n,null),l&&Ne(e,t,"src",n.src,n,null);return;case"input":Se("invalid",e);var y=c=h=i=null,x=null,w=null;for(l in n)if(n.hasOwnProperty(l)){var Y=n[l];if(Y!=null)switch(l){case"name":i=Y;break;case"type":h=Y;break;case"checked":x=Y;break;case"defaultChecked":w=Y;break;case"value":c=Y;break;case"defaultValue":y=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(u(137,t));break;default:Ne(e,t,l,Y,n,null)}}$f(e,c,y,x,w,h,i,!1),bi(e);return;case"select":Se("invalid",e),l=h=c=null;for(i in n)if(n.hasOwnProperty(i)&&(y=n[i],y!=null))switch(i){case"value":c=y;break;case"defaultValue":h=y;break;case"multiple":l=y;default:Ne(e,t,i,y,n,null)}t=c,n=h,e.multiple=!!l,t!=null?Fa(e,!!l,t,!1):n!=null&&Fa(e,!!l,n,!0);return;case"textarea":Se("invalid",e),c=i=l=null;for(h in n)if(n.hasOwnProperty(h)&&(y=n[h],y!=null))switch(h){case"value":l=y;break;case"defaultValue":i=y;break;case"children":c=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(u(91));break;default:Ne(e,t,h,y,n,null)}Gf(e,l,i,c),bi(e);return;case"option":for(x in n)if(n.hasOwnProperty(x)&&(l=n[x],l!=null))switch(x){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ne(e,t,x,l,n,null)}return;case"dialog":Se("beforetoggle",e),Se("toggle",e),Se("cancel",e),Se("close",e);break;case"iframe":case"object":Se("load",e);break;case"video":case"audio":for(l=0;l<zr.length;l++)Se(zr[l],e);break;case"image":Se("error",e),Se("load",e);break;case"details":Se("toggle",e);break;case"embed":case"source":case"link":Se("error",e),Se("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(w in n)if(n.hasOwnProperty(w)&&(l=n[w],l!=null))switch(w){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ne(e,t,w,l,n,null)}return;default:if(po(t)){for(Y in n)n.hasOwnProperty(Y)&&(l=n[Y],l!==void 0&&Ic(e,t,Y,l,n,void 0));return}}for(y in n)n.hasOwnProperty(y)&&(l=n[y],l!=null&&Ne(e,t,y,l,n,null))}function fv(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,c=null,h=null,y=null,x=null,w=null,Y=null;for(U in n){var Q=n[U];if(n.hasOwnProperty(U)&&Q!=null)switch(U){case"checked":break;case"value":break;case"defaultValue":x=Q;default:l.hasOwnProperty(U)||Ne(e,t,U,null,l,Q)}}for(var j in l){var U=l[j];if(Q=n[j],l.hasOwnProperty(j)&&(U!=null||Q!=null))switch(j){case"type":c=U;break;case"name":i=U;break;case"checked":w=U;break;case"defaultChecked":Y=U;break;case"value":h=U;break;case"defaultValue":y=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(u(137,t));break;default:U!==Q&&Ne(e,t,j,U,l,Q)}}ho(e,h,y,x,w,Y,c,i);return;case"select":U=h=y=j=null;for(c in n)if(x=n[c],n.hasOwnProperty(c)&&x!=null)switch(c){case"value":break;case"multiple":U=x;default:l.hasOwnProperty(c)||Ne(e,t,c,null,l,x)}for(i in l)if(c=l[i],x=n[i],l.hasOwnProperty(i)&&(c!=null||x!=null))switch(i){case"value":j=c;break;case"defaultValue":y=c;break;case"multiple":h=c;default:c!==x&&Ne(e,t,i,c,l,x)}t=y,n=h,l=U,j!=null?Fa(e,!!n,j,!1):!!l!=!!n&&(t!=null?Fa(e,!!n,t,!0):Fa(e,!!n,n?[]:"",!1));return;case"textarea":U=j=null;for(y in n)if(i=n[y],n.hasOwnProperty(y)&&i!=null&&!l.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:Ne(e,t,y,null,l,i)}for(h in l)if(i=l[h],c=n[h],l.hasOwnProperty(h)&&(i!=null||c!=null))switch(h){case"value":j=i;break;case"defaultValue":U=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(u(91));break;default:i!==c&&Ne(e,t,h,i,l,c)}Yf(e,j,U);return;case"option":for(var he in n)if(j=n[he],n.hasOwnProperty(he)&&j!=null&&!l.hasOwnProperty(he))switch(he){case"selected":e.selected=!1;break;default:Ne(e,t,he,null,l,j)}for(x in l)if(j=l[x],U=n[x],l.hasOwnProperty(x)&&j!==U&&(j!=null||U!=null))switch(x){case"selected":e.selected=j&&typeof j!="function"&&typeof j!="symbol";break;default:Ne(e,t,x,j,l,U)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var fe in n)j=n[fe],n.hasOwnProperty(fe)&&j!=null&&!l.hasOwnProperty(fe)&&Ne(e,t,fe,null,l,j);for(w in l)if(j=l[w],U=n[w],l.hasOwnProperty(w)&&j!==U&&(j!=null||U!=null))switch(w){case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(u(137,t));break;default:Ne(e,t,w,j,l,U)}return;default:if(po(t)){for(var je in n)j=n[je],n.hasOwnProperty(je)&&j!==void 0&&!l.hasOwnProperty(je)&&Ic(e,t,je,void 0,l,j);for(Y in l)j=l[Y],U=n[Y],!l.hasOwnProperty(Y)||j===U||j===void 0&&U===void 0||Ic(e,t,Y,j,l,U);return}}for(var M in n)j=n[M],n.hasOwnProperty(M)&&j!=null&&!l.hasOwnProperty(M)&&Ne(e,t,M,null,l,j);for(Q in l)j=l[Q],U=n[Q],!l.hasOwnProperty(Q)||j===U||j==null&&U==null||Ne(e,t,Q,j,l,U)}var es=null,ts=null;function mu(e){return e.nodeType===9?e:e.ownerDocument}function Vm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Xm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function ns(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var as=null;function dv(){var e=window.event;return e&&e.type==="popstate"?e===as?!1:(as=e,!0):(as=null,!1)}var Qm=typeof setTimeout=="function"?setTimeout:void 0,hv=typeof clearTimeout=="function"?clearTimeout:void 0,Zm=typeof Promise=="function"?Promise:void 0,mv=typeof queueMicrotask=="function"?queueMicrotask:typeof Zm<"u"?function(e){return Zm.resolve(null).then(e).catch(pv)}:Qm;function pv(e){setTimeout(function(){throw e})}function ca(e){return e==="head"}function Km(e,t){var n=t,l=0,i=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var h=e.ownerDocument;if(n&1&&wr(h.documentElement),n&2&&wr(h.body),n&4)for(n=h.head,wr(n),h=n.firstChild;h;){var y=h.nextSibling,x=h.nodeName;h[Zl]||x==="SCRIPT"||x==="STYLE"||x==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=y}}if(i===0){e.removeChild(c),qr(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);qr(t)}function ls(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ls(n),oo(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function gv(e,t,n,l){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Zl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=un(e.nextSibling),e===null)break}return null}function yv(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=un(e.nextSibling),e===null))return null;return e}function rs(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function vv(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function un(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var is=null;function Jm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Wm(e,t,n){switch(t=mu(n),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function wr(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);oo(e)}var Pt=new Map,Fm=new Set;function pu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Hn=K.d;K.d={f:bv,r:Sv,D:xv,C:Ev,L:Tv,m:Cv,X:Av,S:_v,M:Rv};function bv(){var e=Hn.f(),t=iu();return e||t}function Sv(e){var t=Za(e);t!==null&&t.tag===5&&t.type==="form"?yh(t):Hn.r(e)}var Rl=typeof document>"u"?null:document;function Pm(e,t,n){var l=Rl;if(l&&typeof t=="string"&&t){var i=Xt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Fm.has(i)||(Fm.add(i),e={rel:e,crossOrigin:n,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),st(t,"link",e),lt(t),l.head.appendChild(t)))}}function xv(e){Hn.D(e),Pm("dns-prefetch",e,null)}function Ev(e,t){Hn.C(e,t),Pm("preconnect",e,t)}function Tv(e,t,n){Hn.L(e,t,n);var l=Rl;if(l&&e&&t){var i='link[rel="preload"][as="'+Xt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Xt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Xt(n.imageSizes)+'"]')):i+='[href="'+Xt(e)+'"]';var c=i;switch(t){case"style":c=Ol(e);break;case"script":c=Ml(e)}Pt.has(c)||(e=v({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Pt.set(c,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(Nr(c))||t==="script"&&l.querySelector(jr(c))||(t=l.createElement("link"),st(t,"link",e),lt(t),l.head.appendChild(t)))}}function Cv(e,t){Hn.m(e,t);var n=Rl;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Xt(l)+'"][href="'+Xt(e)+'"]',c=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Ml(e)}if(!Pt.has(c)&&(e=v({rel:"modulepreload",href:e},t),Pt.set(c,e),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(jr(c)))return}l=n.createElement("link"),st(l,"link",e),lt(l),n.head.appendChild(l)}}}function _v(e,t,n){Hn.S(e,t,n);var l=Rl;if(l&&e){var i=Ka(l).hoistableStyles,c=Ol(e);t=t||"default";var h=i.get(c);if(!h){var y={loading:0,preload:null};if(h=l.querySelector(Nr(c)))y.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Pt.get(c))&&us(e,n);var x=h=l.createElement("link");lt(x),st(x,"link",e),x._p=new Promise(function(w,Y){x.onload=w,x.onerror=Y}),x.addEventListener("load",function(){y.loading|=1}),x.addEventListener("error",function(){y.loading|=2}),y.loading|=4,gu(h,t,l)}h={type:"stylesheet",instance:h,count:1,state:y},i.set(c,h)}}}function Av(e,t){Hn.X(e,t);var n=Rl;if(n&&e){var l=Ka(n).hoistableScripts,i=Ml(e),c=l.get(i);c||(c=n.querySelector(jr(i)),c||(e=v({src:e,async:!0},t),(t=Pt.get(i))&&os(e,t),c=n.createElement("script"),lt(c),st(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function Rv(e,t){Hn.M(e,t);var n=Rl;if(n&&e){var l=Ka(n).hoistableScripts,i=Ml(e),c=l.get(i);c||(c=n.querySelector(jr(i)),c||(e=v({src:e,async:!0,type:"module"},t),(t=Pt.get(i))&&os(e,t),c=n.createElement("script"),lt(c),st(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function Im(e,t,n,l){var i=(i=re.current)?pu(i):null;if(!i)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=Ol(n.href),n=Ka(i).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=Ol(n.href);var c=Ka(i).hoistableStyles,h=c.get(e);if(h||(i=i.ownerDocument||i,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,h),(c=i.querySelector(Nr(e)))&&!c._p&&(h.instance=c,h.state.loading=5),Pt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Pt.set(e,n),c||Ov(i,e,n,h.state))),t&&l===null)throw Error(u(528,""));return h}if(t&&l!==null)throw Error(u(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ml(n),n=Ka(i).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Ol(e){return'href="'+Xt(e)+'"'}function Nr(e){return'link[rel="stylesheet"]['+e+"]"}function ep(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function Ov(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),st(t,"link",n),lt(t),e.head.appendChild(t))}function Ml(e){return'[src="'+Xt(e)+'"]'}function jr(e){return"script[async]"+e}function tp(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Xt(n.href)+'"]');if(l)return t.instance=l,lt(l),l;var i=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),lt(l),st(l,"style",i),gu(l,n.precedence,e),t.instance=l;case"stylesheet":i=Ol(n.href);var c=e.querySelector(Nr(i));if(c)return t.state.loading|=4,t.instance=c,lt(c),c;l=ep(n),(i=Pt.get(i))&&us(l,i),c=(e.ownerDocument||e).createElement("link"),lt(c);var h=c;return h._p=new Promise(function(y,x){h.onload=y,h.onerror=x}),st(c,"link",l),t.state.loading|=4,gu(c,n.precedence,e),t.instance=c;case"script":return c=Ml(n.src),(i=e.querySelector(jr(c)))?(t.instance=i,lt(i),i):(l=n,(i=Pt.get(c))&&(l=v({},n),os(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),lt(i),st(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,gu(l,n.precedence,e));return t.instance}function gu(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,c=i,h=0;h<l.length;h++){var y=l[h];if(y.dataset.precedence===t)c=y;else if(c!==i)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function us(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function os(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var yu=null;function np(e,t,n){if(yu===null){var l=new Map,i=yu=new Map;i.set(n,l)}else i=yu,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var c=n[i];if(!(c[Zl]||c[yt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var h=c.getAttribute(t)||"";h=e+h;var y=l.get(h);y?y.push(c):l.set(h,[c])}}return l}function ap(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Mv(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function lp(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Br=null;function zv(){}function Dv(e,t,n){if(Br===null)throw Error(u(475));var l=Br;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=Ol(n.href),c=e.querySelector(Nr(i));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=vu.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,lt(c);return}c=e.ownerDocument||e,n=ep(n),(i=Pt.get(i))&&us(n,i),c=c.createElement("link"),lt(c);var h=c;h._p=new Promise(function(y,x){h.onload=y,h.onerror=x}),st(c,"link",n),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=vu.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function wv(){if(Br===null)throw Error(u(475));var e=Br;return e.stylesheets&&e.count===0&&cs(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&cs(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function vu(){if(this.count--,this.count===0){if(this.stylesheets)cs(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var bu=null;function cs(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,bu=new Map,t.forEach(Nv,e),bu=null,vu.call(e))}function Nv(e,t){if(!(t.state.loading&4)){var n=bu.get(e);if(n)var l=n.get(null);else{n=new Map,bu.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<i.length;c++){var h=i[c];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),l=h)}l&&n.set(null,l)}i=t.instance,h=i.getAttribute("data-precedence"),c=n.get(h)||l,c===l&&n.set(null,i),n.set(h,i),this.count++,l=vu.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),c?c.parentNode.insertBefore(i,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ur={$$typeof:L,Provider:null,Consumer:null,_currentValue:ne,_currentValue2:ne,_threadCount:0};function jv(e,t,n,l,i,c,h,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=lo(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lo(0),this.hiddenUpdates=lo(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=c,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function rp(e,t,n,l,i,c,h,y,x,w,Y,Q){return e=new jv(e,t,n,h,y,x,w,Q),t=1,c===!0&&(t|=24),c=Nt(3,null,null,t),e.current=c,c.stateNode=e,t=Vo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:t},Ko(c),e}function ip(e){return e?(e=il,e):il}function up(e,t,n,l,i,c){i=ip(i),l.context===null?l.context=i:l.pendingContext=i,l=Wn(t),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=Fn(e,l,t),n!==null&&(Lt(n,e,t),dr(n,e,t))}function op(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ss(e,t){op(e,t),(e=e.alternate)&&op(e,t)}function cp(e){if(e.tag===13){var t=rl(e,67108864);t!==null&&Lt(t,e,67108864),ss(e,67108864)}}var Su=!0;function Bv(e,t,n,l){var i=H.T;H.T=null;var c=K.p;try{K.p=2,fs(e,t,n,l)}finally{K.p=c,H.T=i}}function Uv(e,t,n,l){var i=H.T;H.T=null;var c=K.p;try{K.p=8,fs(e,t,n,l)}finally{K.p=c,H.T=i}}function fs(e,t,n,l){if(Su){var i=ds(l);if(i===null)Pc(e,t,l,xu,n),fp(e,l);else if(Lv(i,e,t,n,l))l.stopPropagation();else if(fp(e,l),t&4&&-1<Hv.indexOf(e)){for(;i!==null;){var c=Za(i);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var h=En(c.pendingLanes);if(h!==0){var y=c;for(y.pendingLanes|=2,y.entangledLanes|=2;h;){var x=1<<31-mt(h);y.entanglements[1]|=x,h&=~x}mn(c),(ze&6)===0&&(lu=at()+500,Mr(0))}}break;case 13:y=rl(c,2),y!==null&&Lt(y,c,2),iu(),ss(c,2)}if(c=ds(l),c===null&&Pc(e,t,l,xu,n),c===i)break;i=c}i!==null&&l.stopPropagation()}else Pc(e,t,l,null,n)}}function ds(e){return e=yo(e),hs(e)}var xu=null;function hs(e){if(xu=null,e=Qa(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return xu=e,null}function sp(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(nn()){case dt:return 2;case cn:return 8;case Gt:case me:return 32;case hi:return 268435456;default:return 32}default:return 32}}var ms=!1,sa=null,fa=null,da=null,Hr=new Map,Lr=new Map,ha=[],Hv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function fp(e,t){switch(e){case"focusin":case"focusout":sa=null;break;case"dragenter":case"dragleave":fa=null;break;case"mouseover":case"mouseout":da=null;break;case"pointerover":case"pointerout":Hr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lr.delete(t.pointerId)}}function kr(e,t,n,l,i,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[i]},t!==null&&(t=Za(t),t!==null&&cp(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Lv(e,t,n,l,i){switch(t){case"focusin":return sa=kr(sa,e,t,n,l,i),!0;case"dragenter":return fa=kr(fa,e,t,n,l,i),!0;case"mouseover":return da=kr(da,e,t,n,l,i),!0;case"pointerover":var c=i.pointerId;return Hr.set(c,kr(Hr.get(c)||null,e,t,n,l,i)),!0;case"gotpointercapture":return c=i.pointerId,Lr.set(c,kr(Lr.get(c)||null,e,t,n,l,i)),!0}return!1}function dp(e){var t=Qa(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Dg(e.priority,function(){if(n.tag===13){var l=Ht();l=ro(l);var i=rl(n,l);i!==null&&Lt(i,n,l),ss(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Eu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ds(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);go=l,n.target.dispatchEvent(l),go=null}else return t=Za(n),t!==null&&cp(t),e.blockedOn=n,!1;t.shift()}return!0}function hp(e,t,n){Eu(e)&&n.delete(t)}function kv(){ms=!1,sa!==null&&Eu(sa)&&(sa=null),fa!==null&&Eu(fa)&&(fa=null),da!==null&&Eu(da)&&(da=null),Hr.forEach(hp),Lr.forEach(hp)}function Tu(e,t){e.blockedOn===t&&(e.blockedOn=null,ms||(ms=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,kv)))}var Cu=null;function mp(e){Cu!==e&&(Cu=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Cu===e&&(Cu=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(hs(l||n)===null)continue;break}var c=Za(n);c!==null&&(e.splice(t,3),t-=3,hc(c,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function qr(e){function t(x){return Tu(x,e)}sa!==null&&Tu(sa,e),fa!==null&&Tu(fa,e),da!==null&&Tu(da,e),Hr.forEach(t),Lr.forEach(t);for(var n=0;n<ha.length;n++){var l=ha[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<ha.length&&(n=ha[0],n.blockedOn===null);)dp(n),n.blockedOn===null&&ha.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],c=n[l+1],h=i[_t]||null;if(typeof c=="function")h||mp(n);else if(h){var y=null;if(c&&c.hasAttribute("formAction")){if(i=c,h=c[_t]||null)y=h.formAction;else if(hs(i)!==null)continue}else y=h.action;typeof y=="function"?n[l+1]=y:(n.splice(l,3),l-=3),mp(n)}}}function ps(e){this._internalRoot=e}_u.prototype.render=ps.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var n=t.current,l=Ht();up(n,l,e,t,null,null)},_u.prototype.unmount=ps.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;up(e.current,2,null,e,null,null),iu(),t[Xa]=null}};function _u(e){this._internalRoot=e}_u.prototype.unstable_scheduleHydration=function(e){if(e){var t=Df();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ha.length&&t!==0&&t<ha[n].priority;n++);ha.splice(n,0,e),n===0&&dp(e)}};var pp=r.version;if(pp!=="19.1.0")throw Error(u(527,pp,"19.1.0"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=g(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var qv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:H,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Au=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Au.isDisabled&&Au.supportsFiber)try{Xn=Au.inject(qv),ht=Au}catch{}}return Gr.createRoot=function(e,t){if(!s(e))throw Error(u(299));var n=!1,l="",i=Dh,c=wh,h=Nh,y=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=rp(e,1,!1,null,null,n,l,i,c,h,y,null),e[Xa]=t.current,Fc(e),new ps(t)},Gr.hydrateRoot=function(e,t,n){if(!s(e))throw Error(u(299));var l=!1,i="",c=Dh,h=wh,y=Nh,x=null,w=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(y=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(x=n.unstable_transitionCallbacks),n.formState!==void 0&&(w=n.formState)),t=rp(e,1,!0,t,n??null,l,i,c,h,y,x,w),t.context=ip(null),n=t.current,l=Ht(),l=ro(l),i=Wn(l),i.callback=null,Fn(n,i,l),n=l,t.current.lanes=n,Ql(t,n),mn(t),e[Xa]=t.current,Fc(e),new _u(t)},Gr.version="19.1.0",Gr}var Ap;function n1(){if(Ap)return vs.exports;Ap=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),vs.exports=t1(),vs.exports}var a1=n1();S0();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Wr(){return Wr=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var u in o)Object.prototype.hasOwnProperty.call(o,u)&&(a[u]=o[u])}return a},Wr.apply(this,arguments)}var pa;(function(a){a.Pop="POP",a.Push="PUSH",a.Replace="REPLACE"})(pa||(pa={}));const Rp="popstate";function l1(a){a===void 0&&(a={});function r(u,s){let{pathname:f,search:d,hash:p}=u.location;return js("",{pathname:f,search:d,hash:p},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function o(u,s){return typeof s=="string"?s:Lu(s)}return i1(r,o,null,a)}function et(a,r){if(a===!1||a===null||typeof a>"u")throw new Error(r)}function x0(a,r){if(!a){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function r1(){return Math.random().toString(36).substr(2,8)}function Op(a,r){return{usr:a.state,key:a.key,idx:r}}function js(a,r,o,u){return o===void 0&&(o=null),Wr({pathname:typeof a=="string"?a:a.pathname,search:"",hash:""},typeof r=="string"?ql(r):r,{state:o,key:r&&r.key||u||r1()})}function Lu(a){let{pathname:r="/",search:o="",hash:u=""}=a;return o&&o!=="?"&&(r+=o.charAt(0)==="?"?o:"?"+o),u&&u!=="#"&&(r+=u.charAt(0)==="#"?u:"#"+u),r}function ql(a){let r={};if(a){let o=a.indexOf("#");o>=0&&(r.hash=a.substr(o),a=a.substr(0,o));let u=a.indexOf("?");u>=0&&(r.search=a.substr(u),a=a.substr(0,u)),a&&(r.pathname=a)}return r}function i1(a,r,o,u){u===void 0&&(u={});let{window:s=document.defaultView,v5Compat:f=!1}=u,d=s.history,p=pa.Pop,g=null,m=v();m==null&&(m=0,d.replaceState(Wr({},d.state,{idx:m}),""));function v(){return(d.state||{idx:null}).idx}function S(){p=pa.Pop;let B=v(),k=B==null?null:B-m;m=B,g&&g({action:p,location:C.location,delta:k})}function T(B,k){p=pa.Push;let V=js(C.location,B,k);m=v()+1;let L=Op(V,m),q=C.createHref(V);try{d.pushState(L,"",q)}catch(z){if(z instanceof DOMException&&z.name==="DataCloneError")throw z;s.location.assign(q)}f&&g&&g({action:p,location:C.location,delta:1})}function N(B,k){p=pa.Replace;let V=js(C.location,B,k);m=v();let L=Op(V,m),q=C.createHref(V);d.replaceState(L,"",q),f&&g&&g({action:p,location:C.location,delta:0})}function O(B){let k=s.location.origin!=="null"?s.location.origin:s.location.href,V=typeof B=="string"?B:Lu(B);return V=V.replace(/ $/,"%20"),et(k,"No window.location.(origin|href) available to create URL for href: "+V),new URL(V,k)}let C={get action(){return p},get location(){return a(s,d)},listen(B){if(g)throw new Error("A history only accepts one active listener");return s.addEventListener(Rp,S),g=B,()=>{s.removeEventListener(Rp,S),g=null}},createHref(B){return r(s,B)},createURL:O,encodeLocation(B){let k=O(B);return{pathname:k.pathname,search:k.search,hash:k.hash}},push:T,replace:N,go(B){return d.go(B)}};return C}var Mp;(function(a){a.data="data",a.deferred="deferred",a.redirect="redirect",a.error="error"})(Mp||(Mp={}));function u1(a,r,o){return o===void 0&&(o="/"),o1(a,r,o)}function o1(a,r,o,u){let s=typeof r=="string"?ql(r):r,f=Is(s.pathname||"/",o);if(f==null)return null;let d=E0(a);c1(d);let p=null;for(let g=0;p==null&&g<d.length;++g){let m=x1(f);p=v1(d[g],m)}return p}function E0(a,r,o,u){r===void 0&&(r=[]),o===void 0&&(o=[]),u===void 0&&(u="");let s=(f,d,p)=>{let g={relativePath:p===void 0?f.path||"":p,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};g.relativePath.startsWith("/")&&(et(g.relativePath.startsWith(u),'Absolute route path "'+g.relativePath+'" nested under path '+('"'+u+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),g.relativePath=g.relativePath.slice(u.length));let m=ga([u,g.relativePath]),v=o.concat(g);f.children&&f.children.length>0&&(et(f.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+m+'".')),E0(f.children,r,v,m)),!(f.path==null&&!f.index)&&r.push({path:m,score:g1(m,f.index),routesMeta:v})};return a.forEach((f,d)=>{var p;if(f.path===""||!((p=f.path)!=null&&p.includes("?")))s(f,d);else for(let g of T0(f.path))s(f,d,g)}),r}function T0(a){let r=a.split("/");if(r.length===0)return[];let[o,...u]=r,s=o.endsWith("?"),f=o.replace(/\?$/,"");if(u.length===0)return s?[f,""]:[f];let d=T0(u.join("/")),p=[];return p.push(...d.map(g=>g===""?f:[f,g].join("/"))),s&&p.push(...d),p.map(g=>a.startsWith("/")&&g===""?"/":g)}function c1(a){a.sort((r,o)=>r.score!==o.score?o.score-r.score:y1(r.routesMeta.map(u=>u.childrenIndex),o.routesMeta.map(u=>u.childrenIndex)))}const s1=/^:[\w-]+$/,f1=3,d1=2,h1=1,m1=10,p1=-2,zp=a=>a==="*";function g1(a,r){let o=a.split("/"),u=o.length;return o.some(zp)&&(u+=p1),r&&(u+=d1),o.filter(s=>!zp(s)).reduce((s,f)=>s+(s1.test(f)?f1:f===""?h1:m1),u)}function y1(a,r){return a.length===r.length&&a.slice(0,-1).every((u,s)=>u===r[s])?a[a.length-1]-r[r.length-1]:0}function v1(a,r,o){let{routesMeta:u}=a,s={},f="/",d=[];for(let p=0;p<u.length;++p){let g=u[p],m=p===u.length-1,v=f==="/"?r:r.slice(f.length)||"/",S=b1({path:g.relativePath,caseSensitive:g.caseSensitive,end:m},v),T=g.route;if(!S)return null;Object.assign(s,S.params),d.push({params:s,pathname:ga([f,S.pathname]),pathnameBase:_1(ga([f,S.pathnameBase])),route:T}),S.pathnameBase!=="/"&&(f=ga([f,S.pathnameBase]))}return d}function b1(a,r){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[o,u]=S1(a.path,a.caseSensitive,a.end),s=r.match(o);if(!s)return null;let f=s[0],d=f.replace(/(.)\/+$/,"$1"),p=s.slice(1);return{params:u.reduce((m,v,S)=>{let{paramName:T,isOptional:N}=v;if(T==="*"){let C=p[S]||"";d=f.slice(0,f.length-C.length).replace(/(.)\/+$/,"$1")}const O=p[S];return N&&!O?m[T]=void 0:m[T]=(O||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:a}}function S1(a,r,o){r===void 0&&(r=!1),o===void 0&&(o=!0),x0(a==="*"||!a.endsWith("*")||a.endsWith("/*"),'Route path "'+a+'" will be treated as if it were '+('"'+a.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+a.replace(/\*$/,"/*")+'".'));let u=[],s="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,p,g)=>(u.push({paramName:p,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(u.push({paramName:"*"}),s+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?s+="\\/*$":a!==""&&a!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,r?void 0:"i"),u]}function x1(a){try{return a.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return x0(!1,'The URL path "'+a+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),a}}function Is(a,r){if(r==="/")return a;if(!a.toLowerCase().startsWith(r.toLowerCase()))return null;let o=r.endsWith("/")?r.length-1:r.length,u=a.charAt(o);return u&&u!=="/"?null:a.slice(o)||"/"}function E1(a,r){r===void 0&&(r="/");let{pathname:o,search:u="",hash:s=""}=typeof a=="string"?ql(a):a;return{pathname:o?o.startsWith("/")?o:T1(o,r):r,search:A1(u),hash:R1(s)}}function T1(a,r){let o=r.replace(/\/+$/,"").split("/");return a.split("/").forEach(s=>{s===".."?o.length>1&&o.pop():s!=="."&&o.push(s)}),o.length>1?o.join("/"):"/"}function Es(a,r,o,u){return"Cannot include a '"+a+"' character in a manually specified "+("`to."+r+"` field ["+JSON.stringify(u)+"].  Please separate it out to the ")+("`to."+o+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function C1(a){return a.filter((r,o)=>o===0||r.route.path&&r.route.path.length>0)}function C0(a,r){let o=C1(a);return r?o.map((u,s)=>s===o.length-1?u.pathname:u.pathnameBase):o.map(u=>u.pathnameBase)}function _0(a,r,o,u){u===void 0&&(u=!1);let s;typeof a=="string"?s=ql(a):(s=Wr({},a),et(!s.pathname||!s.pathname.includes("?"),Es("?","pathname","search",s)),et(!s.pathname||!s.pathname.includes("#"),Es("#","pathname","hash",s)),et(!s.search||!s.search.includes("#"),Es("#","search","hash",s)));let f=a===""||s.pathname==="",d=f?"/":s.pathname,p;if(d==null)p=o;else{let S=r.length-1;if(!u&&d.startsWith("..")){let T=d.split("/");for(;T[0]==="..";)T.shift(),S-=1;s.pathname=T.join("/")}p=S>=0?r[S]:"/"}let g=E1(s,p),m=d&&d!=="/"&&d.endsWith("/"),v=(f||d===".")&&o.endsWith("/");return!g.pathname.endsWith("/")&&(m||v)&&(g.pathname+="/"),g}const ga=a=>a.join("/").replace(/\/\/+/g,"/"),_1=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),A1=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,R1=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function O1(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}const A0=["post","put","patch","delete"];new Set(A0);const M1=["get",...A0];new Set(M1);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Fr(){return Fr=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var u in o)Object.prototype.hasOwnProperty.call(o,u)&&(a[u]=o[u])}return a},Fr.apply(this,arguments)}const ef=A.createContext(null),z1=A.createContext(null),Ya=A.createContext(null),Gu=A.createContext(null),Ga=A.createContext({outlet:null,matches:[],isDataRoute:!1}),R0=A.createContext(null);function D1(a,r){let{relative:o}=r===void 0?{}:r;ai()||et(!1);let{basename:u,navigator:s}=A.useContext(Ya),{hash:f,pathname:d,search:p}=M0(a,{relative:o}),g=d;return u!=="/"&&(g=d==="/"?u:ga([u,d])),s.createHref({pathname:g,search:p,hash:f})}function ai(){return A.useContext(Gu)!=null}function $l(){return ai()||et(!1),A.useContext(Gu).location}function O0(a){A.useContext(Ya).static||A.useLayoutEffect(a)}function tf(){let{isDataRoute:a}=A.useContext(Ga);return a?V1():w1()}function w1(){ai()||et(!1);let a=A.useContext(ef),{basename:r,future:o,navigator:u}=A.useContext(Ya),{matches:s}=A.useContext(Ga),{pathname:f}=$l(),d=JSON.stringify(C0(s,o.v7_relativeSplatPath)),p=A.useRef(!1);return O0(()=>{p.current=!0}),A.useCallback(function(m,v){if(v===void 0&&(v={}),!p.current)return;if(typeof m=="number"){u.go(m);return}let S=_0(m,JSON.parse(d),f,v.relative==="path");a==null&&r!=="/"&&(S.pathname=S.pathname==="/"?r:ga([r,S.pathname])),(v.replace?u.replace:u.push)(S,v.state,v)},[r,u,d,f,a])}function M0(a,r){let{relative:o}=r===void 0?{}:r,{future:u}=A.useContext(Ya),{matches:s}=A.useContext(Ga),{pathname:f}=$l(),d=JSON.stringify(C0(s,u.v7_relativeSplatPath));return A.useMemo(()=>_0(a,JSON.parse(d),f,o==="path"),[a,d,f,o])}function N1(a,r){return j1(a,r)}function j1(a,r,o,u){ai()||et(!1);let{navigator:s}=A.useContext(Ya),{matches:f}=A.useContext(Ga),d=f[f.length-1],p=d?d.params:{};d&&d.pathname;let g=d?d.pathnameBase:"/";d&&d.route;let m=$l(),v;if(r){var S;let B=typeof r=="string"?ql(r):r;g==="/"||(S=B.pathname)!=null&&S.startsWith(g)||et(!1),v=B}else v=m;let T=v.pathname||"/",N=T;if(g!=="/"){let B=g.replace(/^\//,"").split("/");N="/"+T.replace(/^\//,"").split("/").slice(B.length).join("/")}let O=u1(a,{pathname:N}),C=k1(O&&O.map(B=>Object.assign({},B,{params:Object.assign({},p,B.params),pathname:ga([g,s.encodeLocation?s.encodeLocation(B.pathname).pathname:B.pathname]),pathnameBase:B.pathnameBase==="/"?g:ga([g,s.encodeLocation?s.encodeLocation(B.pathnameBase).pathname:B.pathnameBase])})),f,o,u);return r&&C?A.createElement(Gu.Provider,{value:{location:Fr({pathname:"/",search:"",hash:"",state:null,key:"default"},v),navigationType:pa.Pop}},C):C}function B1(){let a=G1(),r=O1(a)?a.status+" "+a.statusText:a instanceof Error?a.message:JSON.stringify(a),o=a instanceof Error?a.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return A.createElement(A.Fragment,null,A.createElement("h2",null,"Unexpected Application Error!"),A.createElement("h3",{style:{fontStyle:"italic"}},r),o?A.createElement("pre",{style:s},o):null,null)}const U1=A.createElement(B1,null);class H1 extends A.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,o){return o.location!==r.location||o.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:o.error,location:o.location,revalidation:r.revalidation||o.revalidation}}componentDidCatch(r,o){console.error("React Router caught the following error during render",r,o)}render(){return this.state.error!==void 0?A.createElement(Ga.Provider,{value:this.props.routeContext},A.createElement(R0.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function L1(a){let{routeContext:r,match:o,children:u}=a,s=A.useContext(ef);return s&&s.static&&s.staticContext&&(o.route.errorElement||o.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=o.route.id),A.createElement(Ga.Provider,{value:r},u)}function k1(a,r,o,u){var s;if(r===void 0&&(r=[]),o===void 0&&(o=null),u===void 0&&(u=null),a==null){var f;if(!o)return null;if(o.errors)a=o.matches;else if((f=u)!=null&&f.v7_partialHydration&&r.length===0&&!o.initialized&&o.matches.length>0)a=o.matches;else return null}let d=a,p=(s=o)==null?void 0:s.errors;if(p!=null){let v=d.findIndex(S=>S.route.id&&(p==null?void 0:p[S.route.id])!==void 0);v>=0||et(!1),d=d.slice(0,Math.min(d.length,v+1))}let g=!1,m=-1;if(o&&u&&u.v7_partialHydration)for(let v=0;v<d.length;v++){let S=d[v];if((S.route.HydrateFallback||S.route.hydrateFallbackElement)&&(m=v),S.route.id){let{loaderData:T,errors:N}=o,O=S.route.loader&&T[S.route.id]===void 0&&(!N||N[S.route.id]===void 0);if(S.route.lazy||O){g=!0,m>=0?d=d.slice(0,m+1):d=[d[0]];break}}}return d.reduceRight((v,S,T)=>{let N,O=!1,C=null,B=null;o&&(N=p&&S.route.id?p[S.route.id]:void 0,C=S.route.errorElement||U1,g&&(m<0&&T===0?(X1("route-fallback"),O=!0,B=null):m===T&&(O=!0,B=S.route.hydrateFallbackElement||null)));let k=r.concat(d.slice(0,T+1)),V=()=>{let L;return N?L=C:O?L=B:S.route.Component?L=A.createElement(S.route.Component,null):S.route.element?L=S.route.element:L=v,A.createElement(L1,{match:S,routeContext:{outlet:v,matches:k,isDataRoute:o!=null},children:L})};return o&&(S.route.ErrorBoundary||S.route.errorElement||T===0)?A.createElement(H1,{location:o.location,revalidation:o.revalidation,component:C,error:N,children:V(),routeContext:{outlet:null,matches:k,isDataRoute:!0}}):V()},null)}var z0=function(a){return a.UseBlocker="useBlocker",a.UseRevalidator="useRevalidator",a.UseNavigateStable="useNavigate",a}(z0||{}),D0=function(a){return a.UseBlocker="useBlocker",a.UseLoaderData="useLoaderData",a.UseActionData="useActionData",a.UseRouteError="useRouteError",a.UseNavigation="useNavigation",a.UseRouteLoaderData="useRouteLoaderData",a.UseMatches="useMatches",a.UseRevalidator="useRevalidator",a.UseNavigateStable="useNavigate",a.UseRouteId="useRouteId",a}(D0||{});function q1(a){let r=A.useContext(ef);return r||et(!1),r}function $1(a){let r=A.useContext(z1);return r||et(!1),r}function Y1(a){let r=A.useContext(Ga);return r||et(!1),r}function w0(a){let r=Y1(),o=r.matches[r.matches.length-1];return o.route.id||et(!1),o.route.id}function G1(){var a;let r=A.useContext(R0),o=$1(),u=w0();return r!==void 0?r:(a=o.errors)==null?void 0:a[u]}function V1(){let{router:a}=q1(z0.UseNavigateStable),r=w0(D0.UseNavigateStable),o=A.useRef(!1);return O0(()=>{o.current=!0}),A.useCallback(function(s,f){f===void 0&&(f={}),o.current&&(typeof s=="number"?a.navigate(s):a.navigate(s,Fr({fromRouteId:r},f)))},[a,r])}const Dp={};function X1(a,r,o){Dp[a]||(Dp[a]=!0)}function Q1(a,r){a==null||a.v7_startTransition,a==null||a.v7_relativeSplatPath}function La(a){et(!1)}function Z1(a){let{basename:r="/",children:o=null,location:u,navigationType:s=pa.Pop,navigator:f,static:d=!1,future:p}=a;ai()&&et(!1);let g=r.replace(/^\/*/,"/"),m=A.useMemo(()=>({basename:g,navigator:f,static:d,future:Fr({v7_relativeSplatPath:!1},p)}),[g,p,f,d]);typeof u=="string"&&(u=ql(u));let{pathname:v="/",search:S="",hash:T="",state:N=null,key:O="default"}=u,C=A.useMemo(()=>{let B=Is(v,g);return B==null?null:{location:{pathname:B,search:S,hash:T,state:N,key:O},navigationType:s}},[g,v,S,T,N,O,s]);return C==null?null:A.createElement(Ya.Provider,{value:m},A.createElement(Gu.Provider,{children:o,value:C}))}function K1(a){let{children:r,location:o}=a;return N1(Bs(r),o)}new Promise(()=>{});function Bs(a,r){r===void 0&&(r=[]);let o=[];return A.Children.forEach(a,(u,s)=>{if(!A.isValidElement(u))return;let f=[...r,s];if(u.type===A.Fragment){o.push.apply(o,Bs(u.props.children,f));return}u.type!==La&&et(!1),!u.props.index||!u.props.children||et(!1);let d={id:u.props.id||f.join("-"),caseSensitive:u.props.caseSensitive,element:u.props.element,Component:u.props.Component,index:u.props.index,path:u.props.path,loader:u.props.loader,action:u.props.action,errorElement:u.props.errorElement,ErrorBoundary:u.props.ErrorBoundary,hasErrorBoundary:u.props.ErrorBoundary!=null||u.props.errorElement!=null,shouldRevalidate:u.props.shouldRevalidate,handle:u.props.handle,lazy:u.props.lazy};u.props.children&&(d.children=Bs(u.props.children,f)),o.push(d)}),o}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Us(){return Us=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var u in o)Object.prototype.hasOwnProperty.call(o,u)&&(a[u]=o[u])}return a},Us.apply(this,arguments)}function J1(a,r){if(a==null)return{};var o={},u=Object.keys(a),s,f;for(f=0;f<u.length;f++)s=u[f],!(r.indexOf(s)>=0)&&(o[s]=a[s]);return o}function W1(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function F1(a,r){return a.button===0&&(!r||r==="_self")&&!W1(a)}function Hs(a){return a===void 0&&(a=""),new URLSearchParams(typeof a=="string"||Array.isArray(a)||a instanceof URLSearchParams?a:Object.keys(a).reduce((r,o)=>{let u=a[o];return r.concat(Array.isArray(u)?u.map(s=>[o,s]):[[o,u]])},[]))}function P1(a,r){let o=Hs(a);return r&&r.forEach((u,s)=>{o.has(s)||r.getAll(s).forEach(f=>{o.append(s,f)})}),o}const I1=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],eb="6";try{window.__reactRouterVersion=eb}catch{}const tb="startTransition",wp=Hu[tb];function nb(a){let{basename:r,children:o,future:u,window:s}=a,f=A.useRef();f.current==null&&(f.current=l1({window:s,v5Compat:!0}));let d=f.current,[p,g]=A.useState({action:d.action,location:d.location}),{v7_startTransition:m}=u||{},v=A.useCallback(S=>{m&&wp?wp(()=>g(S)):g(S)},[g,m]);return A.useLayoutEffect(()=>d.listen(v),[d,v]),A.useEffect(()=>Q1(u),[u]),A.createElement(Z1,{basename:r,children:o,location:p.location,navigationType:p.action,navigator:d,future:u})}const ab=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",lb=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,N0=A.forwardRef(function(r,o){let{onClick:u,relative:s,reloadDocument:f,replace:d,state:p,target:g,to:m,preventScrollReset:v,viewTransition:S}=r,T=J1(r,I1),{basename:N}=A.useContext(Ya),O,C=!1;if(typeof m=="string"&&lb.test(m)&&(O=m,ab))try{let L=new URL(window.location.href),q=m.startsWith("//")?new URL(L.protocol+m):new URL(m),z=Is(q.pathname,N);q.origin===L.origin&&z!=null?m=z+q.search+q.hash:C=!0}catch{}let B=D1(m,{relative:s}),k=rb(m,{replace:d,state:p,target:g,preventScrollReset:v,relative:s,viewTransition:S});function V(L){u&&u(L),L.defaultPrevented||k(L)}return A.createElement("a",Us({},T,{href:O||B,onClick:C||f?u:V,ref:o,target:g}))});var Np;(function(a){a.UseScrollRestoration="useScrollRestoration",a.UseSubmit="useSubmit",a.UseSubmitFetcher="useSubmitFetcher",a.UseFetcher="useFetcher",a.useViewTransitionState="useViewTransitionState"})(Np||(Np={}));var jp;(function(a){a.UseFetcher="useFetcher",a.UseFetchers="useFetchers",a.UseScrollRestoration="useScrollRestoration"})(jp||(jp={}));function rb(a,r){let{target:o,replace:u,state:s,preventScrollReset:f,relative:d,viewTransition:p}=r===void 0?{}:r,g=tf(),m=$l(),v=M0(a,{relative:d});return A.useCallback(S=>{if(F1(S,o)){S.preventDefault();let T=u!==void 0?u:Lu(m)===Lu(v);g(a,{replace:T,state:s,preventScrollReset:f,relative:d,viewTransition:p})}},[m,g,v,u,s,o,a,f,d,p])}function ib(a){let r=A.useRef(Hs(a)),o=A.useRef(!1),u=$l(),s=A.useMemo(()=>P1(u.search,o.current?null:r.current),[u.search]),f=tf(),d=A.useCallback((p,g)=>{const m=Hs(typeof p=="function"?p(s):p);o.current=!0,f("?"+m,g)},[f,s]);return[s,d]}const ub="_nav_1bw4i_1",ob="_link_1bw4i_17",cb="_linkActive_1bw4i_29",Ts={nav:ub,link:ob,linkActive:cb},Pr={black:"#000",white:"#fff"},zl={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Dl={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},wl={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Nl={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},jl={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},Vr={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},sb={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function $a(a,...r){const o=new URL(`https://mui.com/production-error/?code=${a}`);return r.forEach(u=>o.searchParams.append("args[]",u)),`Minified MUI error #${a}; visit ${o} for the full message.`}const qn="$$material";function ku(){return ku=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var u in o)({}).hasOwnProperty.call(o,u)&&(a[u]=o[u])}return a},ku.apply(null,arguments)}function fb(a){if(a.sheet)return a.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===a)return document.styleSheets[r]}function db(a){var r=document.createElement("style");return r.setAttribute("data-emotion",a.key),a.nonce!==void 0&&r.setAttribute("nonce",a.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var hb=function(){function a(o){var u=this;this._insertTag=function(s){var f;u.tags.length===0?u.insertionPoint?f=u.insertionPoint.nextSibling:u.prepend?f=u.container.firstChild:f=u.before:f=u.tags[u.tags.length-1].nextSibling,u.container.insertBefore(s,f),u.tags.push(s)},this.isSpeedy=o.speedy===void 0?!0:o.speedy,this.tags=[],this.ctr=0,this.nonce=o.nonce,this.key=o.key,this.container=o.container,this.prepend=o.prepend,this.insertionPoint=o.insertionPoint,this.before=null}var r=a.prototype;return r.hydrate=function(u){u.forEach(this._insertTag)},r.insert=function(u){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(db(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var f=fb(s);try{f.insertRule(u,f.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(u));this.ctr++},r.flush=function(){this.tags.forEach(function(u){var s;return(s=u.parentNode)==null?void 0:s.removeChild(u)}),this.tags=[],this.ctr=0},a}(),St="-ms-",qu="-moz-",Ae="-webkit-",j0="comm",nf="rule",af="decl",mb="@import",B0="@keyframes",pb="@layer",gb=Math.abs,Vu=String.fromCharCode,yb=Object.assign;function vb(a,r){return ft(a,0)^45?(((r<<2^ft(a,0))<<2^ft(a,1))<<2^ft(a,2))<<2^ft(a,3):0}function U0(a){return a.trim()}function bb(a,r){return(a=r.exec(a))?a[0]:a}function Re(a,r,o){return a.replace(r,o)}function Ls(a,r){return a.indexOf(r)}function ft(a,r){return a.charCodeAt(r)|0}function Ir(a,r,o){return a.slice(r,o)}function gn(a){return a.length}function lf(a){return a.length}function Ru(a,r){return r.push(a),a}function Sb(a,r){return a.map(r).join("")}var Xu=1,kl=1,H0=0,zt=0,Ie=0,Yl="";function Qu(a,r,o,u,s,f,d){return{value:a,root:r,parent:o,type:u,props:s,children:f,line:Xu,column:kl,length:d,return:""}}function Xr(a,r){return yb(Qu("",null,null,"",null,null,0),a,{length:-a.length},r)}function xb(){return Ie}function Eb(){return Ie=zt>0?ft(Yl,--zt):0,kl--,Ie===10&&(kl=1,Xu--),Ie}function qt(){return Ie=zt<H0?ft(Yl,zt++):0,kl++,Ie===10&&(kl=1,Xu++),Ie}function bn(){return ft(Yl,zt)}function Du(){return zt}function li(a,r){return Ir(Yl,a,r)}function ei(a){switch(a){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function L0(a){return Xu=kl=1,H0=gn(Yl=a),zt=0,[]}function k0(a){return Yl="",a}function wu(a){return U0(li(zt-1,ks(a===91?a+2:a===40?a+1:a)))}function Tb(a){for(;(Ie=bn())&&Ie<33;)qt();return ei(a)>2||ei(Ie)>3?"":" "}function Cb(a,r){for(;--r&&qt()&&!(Ie<48||Ie>102||Ie>57&&Ie<65||Ie>70&&Ie<97););return li(a,Du()+(r<6&&bn()==32&&qt()==32))}function ks(a){for(;qt();)switch(Ie){case a:return zt;case 34:case 39:a!==34&&a!==39&&ks(Ie);break;case 40:a===41&&ks(a);break;case 92:qt();break}return zt}function _b(a,r){for(;qt()&&a+Ie!==57;)if(a+Ie===84&&bn()===47)break;return"/*"+li(r,zt-1)+"*"+Vu(a===47?a:qt())}function Ab(a){for(;!ei(bn());)qt();return li(a,zt)}function Rb(a){return k0(Nu("",null,null,null,[""],a=L0(a),0,[0],a))}function Nu(a,r,o,u,s,f,d,p,g){for(var m=0,v=0,S=d,T=0,N=0,O=0,C=1,B=1,k=1,V=0,L="",q=s,z=f,W=u,P=L;B;)switch(O=V,V=qt()){case 40:if(O!=108&&ft(P,S-1)==58){Ls(P+=Re(wu(V),"&","&\f"),"&\f")!=-1&&(k=-1);break}case 34:case 39:case 91:P+=wu(V);break;case 9:case 10:case 13:case 32:P+=Tb(O);break;case 92:P+=Cb(Du()-1,7);continue;case 47:switch(bn()){case 42:case 47:Ru(Ob(_b(qt(),Du()),r,o),g);break;default:P+="/"}break;case 123*C:p[m++]=gn(P)*k;case 125*C:case 59:case 0:switch(V){case 0:case 125:B=0;case 59+v:k==-1&&(P=Re(P,/\f/g,"")),N>0&&gn(P)-S&&Ru(N>32?Up(P+";",u,o,S-1):Up(Re(P," ","")+";",u,o,S-2),g);break;case 59:P+=";";default:if(Ru(W=Bp(P,r,o,m,v,s,p,L,q=[],z=[],S),f),V===123)if(v===0)Nu(P,r,W,W,q,f,S,p,z);else switch(T===99&&ft(P,3)===110?100:T){case 100:case 108:case 109:case 115:Nu(a,W,W,u&&Ru(Bp(a,W,W,0,0,s,p,L,s,q=[],S),z),s,z,S,p,u?q:z);break;default:Nu(P,W,W,W,[""],z,0,p,z)}}m=v=N=0,C=k=1,L=P="",S=d;break;case 58:S=1+gn(P),N=O;default:if(C<1){if(V==123)--C;else if(V==125&&C++==0&&Eb()==125)continue}switch(P+=Vu(V),V*C){case 38:k=v>0?1:(P+="\f",-1);break;case 44:p[m++]=(gn(P)-1)*k,k=1;break;case 64:bn()===45&&(P+=wu(qt())),T=bn(),v=S=gn(L=P+=Ab(Du())),V++;break;case 45:O===45&&gn(P)==2&&(C=0)}}return f}function Bp(a,r,o,u,s,f,d,p,g,m,v){for(var S=s-1,T=s===0?f:[""],N=lf(T),O=0,C=0,B=0;O<u;++O)for(var k=0,V=Ir(a,S+1,S=gb(C=d[O])),L=a;k<N;++k)(L=U0(C>0?T[k]+" "+V:Re(V,/&\f/g,T[k])))&&(g[B++]=L);return Qu(a,r,o,s===0?nf:p,g,m,v)}function Ob(a,r,o){return Qu(a,r,o,j0,Vu(xb()),Ir(a,2,-2),0)}function Up(a,r,o,u){return Qu(a,r,o,af,Ir(a,0,u),Ir(a,u+1,-1),u)}function Ul(a,r){for(var o="",u=lf(a),s=0;s<u;s++)o+=r(a[s],s,a,r)||"";return o}function Mb(a,r,o,u){switch(a.type){case pb:if(a.children.length)break;case mb:case af:return a.return=a.return||a.value;case j0:return"";case B0:return a.return=a.value+"{"+Ul(a.children,u)+"}";case nf:a.value=a.props.join(",")}return gn(o=Ul(a.children,u))?a.return=a.value+"{"+o+"}":""}function zb(a){var r=lf(a);return function(o,u,s,f){for(var d="",p=0;p<r;p++)d+=a[p](o,u,s,f)||"";return d}}function Db(a){return function(r){r.root||(r=r.return)&&a(r)}}function q0(a){var r=Object.create(null);return function(o){return r[o]===void 0&&(r[o]=a(o)),r[o]}}var wb=function(r,o,u){for(var s=0,f=0;s=f,f=bn(),s===38&&f===12&&(o[u]=1),!ei(f);)qt();return li(r,zt)},Nb=function(r,o){var u=-1,s=44;do switch(ei(s)){case 0:s===38&&bn()===12&&(o[u]=1),r[u]+=wb(zt-1,o,u);break;case 2:r[u]+=wu(s);break;case 4:if(s===44){r[++u]=bn()===58?"&\f":"",o[u]=r[u].length;break}default:r[u]+=Vu(s)}while(s=qt());return r},jb=function(r,o){return k0(Nb(L0(r),o))},Hp=new WeakMap,Bb=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var o=r.value,u=r.parent,s=r.column===u.column&&r.line===u.line;u.type!=="rule";)if(u=u.parent,!u)return;if(!(r.props.length===1&&o.charCodeAt(0)!==58&&!Hp.get(u))&&!s){Hp.set(r,!0);for(var f=[],d=jb(o,f),p=u.props,g=0,m=0;g<d.length;g++)for(var v=0;v<p.length;v++,m++)r.props[m]=f[g]?d[g].replace(/&\f/g,p[v]):p[v]+" "+d[g]}}},Ub=function(r){if(r.type==="decl"){var o=r.value;o.charCodeAt(0)===108&&o.charCodeAt(2)===98&&(r.return="",r.value="")}};function $0(a,r){switch(vb(a,r)){case 5103:return Ae+"print-"+a+a;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ae+a+a;case 5349:case 4246:case 4810:case 6968:case 2756:return Ae+a+qu+a+St+a+a;case 6828:case 4268:return Ae+a+St+a+a;case 6165:return Ae+a+St+"flex-"+a+a;case 5187:return Ae+a+Re(a,/(\w+).+(:[^]+)/,Ae+"box-$1$2"+St+"flex-$1$2")+a;case 5443:return Ae+a+St+"flex-item-"+Re(a,/flex-|-self/,"")+a;case 4675:return Ae+a+St+"flex-line-pack"+Re(a,/align-content|flex-|-self/,"")+a;case 5548:return Ae+a+St+Re(a,"shrink","negative")+a;case 5292:return Ae+a+St+Re(a,"basis","preferred-size")+a;case 6060:return Ae+"box-"+Re(a,"-grow","")+Ae+a+St+Re(a,"grow","positive")+a;case 4554:return Ae+Re(a,/([^-])(transform)/g,"$1"+Ae+"$2")+a;case 6187:return Re(Re(Re(a,/(zoom-|grab)/,Ae+"$1"),/(image-set)/,Ae+"$1"),a,"")+a;case 5495:case 3959:return Re(a,/(image-set\([^]*)/,Ae+"$1$`$1");case 4968:return Re(Re(a,/(.+:)(flex-)?(.*)/,Ae+"box-pack:$3"+St+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ae+a+a;case 4095:case 3583:case 4068:case 2532:return Re(a,/(.+)-inline(.+)/,Ae+"$1$2")+a;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(gn(a)-1-r>6)switch(ft(a,r+1)){case 109:if(ft(a,r+4)!==45)break;case 102:return Re(a,/(.+:)(.+)-([^]+)/,"$1"+Ae+"$2-$3$1"+qu+(ft(a,r+3)==108?"$3":"$2-$3"))+a;case 115:return~Ls(a,"stretch")?$0(Re(a,"stretch","fill-available"),r)+a:a}break;case 4949:if(ft(a,r+1)!==115)break;case 6444:switch(ft(a,gn(a)-3-(~Ls(a,"!important")&&10))){case 107:return Re(a,":",":"+Ae)+a;case 101:return Re(a,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ae+(ft(a,14)===45?"inline-":"")+"box$3$1"+Ae+"$2$3$1"+St+"$2box$3")+a}break;case 5936:switch(ft(a,r+11)){case 114:return Ae+a+St+Re(a,/[svh]\w+-[tblr]{2}/,"tb")+a;case 108:return Ae+a+St+Re(a,/[svh]\w+-[tblr]{2}/,"tb-rl")+a;case 45:return Ae+a+St+Re(a,/[svh]\w+-[tblr]{2}/,"lr")+a}return Ae+a+St+a+a}return a}var Hb=function(r,o,u,s){if(r.length>-1&&!r.return)switch(r.type){case af:r.return=$0(r.value,r.length);break;case B0:return Ul([Xr(r,{value:Re(r.value,"@","@"+Ae)})],s);case nf:if(r.length)return Sb(r.props,function(f){switch(bb(f,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Ul([Xr(r,{props:[Re(f,/:(read-\w+)/,":"+qu+"$1")]})],s);case"::placeholder":return Ul([Xr(r,{props:[Re(f,/:(plac\w+)/,":"+Ae+"input-$1")]}),Xr(r,{props:[Re(f,/:(plac\w+)/,":"+qu+"$1")]}),Xr(r,{props:[Re(f,/:(plac\w+)/,St+"input-$1")]})],s)}return""})}},Lb=[Hb],kb=function(r){var o=r.key;if(o==="css"){var u=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(u,function(C){var B=C.getAttribute("data-emotion");B.indexOf(" ")!==-1&&(document.head.appendChild(C),C.setAttribute("data-s",""))})}var s=r.stylisPlugins||Lb,f={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+o+' "]'),function(C){for(var B=C.getAttribute("data-emotion").split(" "),k=1;k<B.length;k++)f[B[k]]=!0;p.push(C)});var g,m=[Bb,Ub];{var v,S=[Mb,Db(function(C){v.insert(C)})],T=zb(m.concat(s,S)),N=function(B){return Ul(Rb(B),T)};g=function(B,k,V,L){v=V,N(B?B+"{"+k.styles+"}":k.styles),L&&(O.inserted[k.name]=!0)}}var O={key:o,sheet:new hb({key:o,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:f,registered:{},insert:g};return O.sheet.hydrate(p),O},Cs={exports:{}},Oe={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lp;function qb(){if(Lp)return Oe;Lp=1;var a=typeof Symbol=="function"&&Symbol.for,r=a?Symbol.for("react.element"):60103,o=a?Symbol.for("react.portal"):60106,u=a?Symbol.for("react.fragment"):60107,s=a?Symbol.for("react.strict_mode"):60108,f=a?Symbol.for("react.profiler"):60114,d=a?Symbol.for("react.provider"):60109,p=a?Symbol.for("react.context"):60110,g=a?Symbol.for("react.async_mode"):60111,m=a?Symbol.for("react.concurrent_mode"):60111,v=a?Symbol.for("react.forward_ref"):60112,S=a?Symbol.for("react.suspense"):60113,T=a?Symbol.for("react.suspense_list"):60120,N=a?Symbol.for("react.memo"):60115,O=a?Symbol.for("react.lazy"):60116,C=a?Symbol.for("react.block"):60121,B=a?Symbol.for("react.fundamental"):60117,k=a?Symbol.for("react.responder"):60118,V=a?Symbol.for("react.scope"):60119;function L(z){if(typeof z=="object"&&z!==null){var W=z.$$typeof;switch(W){case r:switch(z=z.type,z){case g:case m:case u:case f:case s:case S:return z;default:switch(z=z&&z.$$typeof,z){case p:case v:case O:case N:case d:return z;default:return W}}case o:return W}}}function q(z){return L(z)===m}return Oe.AsyncMode=g,Oe.ConcurrentMode=m,Oe.ContextConsumer=p,Oe.ContextProvider=d,Oe.Element=r,Oe.ForwardRef=v,Oe.Fragment=u,Oe.Lazy=O,Oe.Memo=N,Oe.Portal=o,Oe.Profiler=f,Oe.StrictMode=s,Oe.Suspense=S,Oe.isAsyncMode=function(z){return q(z)||L(z)===g},Oe.isConcurrentMode=q,Oe.isContextConsumer=function(z){return L(z)===p},Oe.isContextProvider=function(z){return L(z)===d},Oe.isElement=function(z){return typeof z=="object"&&z!==null&&z.$$typeof===r},Oe.isForwardRef=function(z){return L(z)===v},Oe.isFragment=function(z){return L(z)===u},Oe.isLazy=function(z){return L(z)===O},Oe.isMemo=function(z){return L(z)===N},Oe.isPortal=function(z){return L(z)===o},Oe.isProfiler=function(z){return L(z)===f},Oe.isStrictMode=function(z){return L(z)===s},Oe.isSuspense=function(z){return L(z)===S},Oe.isValidElementType=function(z){return typeof z=="string"||typeof z=="function"||z===u||z===m||z===f||z===s||z===S||z===T||typeof z=="object"&&z!==null&&(z.$$typeof===O||z.$$typeof===N||z.$$typeof===d||z.$$typeof===p||z.$$typeof===v||z.$$typeof===B||z.$$typeof===k||z.$$typeof===V||z.$$typeof===C)},Oe.typeOf=L,Oe}var kp;function $b(){return kp||(kp=1,Cs.exports=qb()),Cs.exports}var _s,qp;function Yb(){if(qp)return _s;qp=1;var a=$b(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},u={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},f={};f[a.ForwardRef]=u,f[a.Memo]=s;function d(O){return a.isMemo(O)?s:f[O.$$typeof]||r}var p=Object.defineProperty,g=Object.getOwnPropertyNames,m=Object.getOwnPropertySymbols,v=Object.getOwnPropertyDescriptor,S=Object.getPrototypeOf,T=Object.prototype;function N(O,C,B){if(typeof C!="string"){if(T){var k=S(C);k&&k!==T&&N(O,k,B)}var V=g(C);m&&(V=V.concat(m(C)));for(var L=d(O),q=d(C),z=0;z<V.length;++z){var W=V[z];if(!o[W]&&!(B&&B[W])&&!(q&&q[W])&&!(L&&L[W])){var P=v(C,W);try{p(O,W,P)}catch{}}}}return O}return _s=N,_s}Yb();var Gb=!0;function Y0(a,r,o){var u="";return o.split(" ").forEach(function(s){a[s]!==void 0?r.push(a[s]+";"):s&&(u+=s+" ")}),u}var rf=function(r,o,u){var s=r.key+"-"+o.name;(u===!1||Gb===!1)&&r.registered[s]===void 0&&(r.registered[s]=o.styles)},uf=function(r,o,u){rf(r,o,u);var s=r.key+"-"+o.name;if(r.inserted[o.name]===void 0){var f=o;do r.insert(o===f?"."+s:"",f,r.sheet,!0),f=f.next;while(f!==void 0)}};function Vb(a){for(var r=0,o,u=0,s=a.length;s>=4;++u,s-=4)o=a.charCodeAt(u)&255|(a.charCodeAt(++u)&255)<<8|(a.charCodeAt(++u)&255)<<16|(a.charCodeAt(++u)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,r=(o&65535)*1540483477+((o>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(s){case 3:r^=(a.charCodeAt(u+2)&255)<<16;case 2:r^=(a.charCodeAt(u+1)&255)<<8;case 1:r^=a.charCodeAt(u)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var Xb={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Qb=/[A-Z]|^ms/g,Zb=/_EMO_([^_]+?)_([^]*?)_EMO_/g,G0=function(r){return r.charCodeAt(1)===45},$p=function(r){return r!=null&&typeof r!="boolean"},As=q0(function(a){return G0(a)?a:a.replace(Qb,"-$&").toLowerCase()}),Yp=function(r,o){switch(r){case"animation":case"animationName":if(typeof o=="string")return o.replace(Zb,function(u,s,f){return yn={name:s,styles:f,next:yn},s})}return Xb[r]!==1&&!G0(r)&&typeof o=="number"&&o!==0?o+"px":o};function ti(a,r,o){if(o==null)return"";var u=o;if(u.__emotion_styles!==void 0)return u;switch(typeof o){case"boolean":return"";case"object":{var s=o;if(s.anim===1)return yn={name:s.name,styles:s.styles,next:yn},s.name;var f=o;if(f.styles!==void 0){var d=f.next;if(d!==void 0)for(;d!==void 0;)yn={name:d.name,styles:d.styles,next:yn},d=d.next;var p=f.styles+";";return p}return Kb(a,r,o)}case"function":{if(a!==void 0){var g=yn,m=o(a);return yn=g,ti(a,r,m)}break}}var v=o;if(r==null)return v;var S=r[v];return S!==void 0?S:v}function Kb(a,r,o){var u="";if(Array.isArray(o))for(var s=0;s<o.length;s++)u+=ti(a,r,o[s])+";";else for(var f in o){var d=o[f];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?u+=f+"{"+r[p]+"}":$p(p)&&(u+=As(f)+":"+Yp(f,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var g=0;g<d.length;g++)$p(d[g])&&(u+=As(f)+":"+Yp(f,d[g])+";");else{var m=ti(a,r,d);switch(f){case"animation":case"animationName":{u+=As(f)+":"+m+";";break}default:u+=f+"{"+m+"}"}}}return u}var Gp=/label:\s*([^\s;{]+)\s*(;|$)/g,yn;function ri(a,r,o){if(a.length===1&&typeof a[0]=="object"&&a[0]!==null&&a[0].styles!==void 0)return a[0];var u=!0,s="";yn=void 0;var f=a[0];if(f==null||f.raw===void 0)u=!1,s+=ti(o,r,f);else{var d=f;s+=d[0]}for(var p=1;p<a.length;p++)if(s+=ti(o,r,a[p]),u){var g=f;s+=g[p]}Gp.lastIndex=0;for(var m="",v;(v=Gp.exec(s))!==null;)m+="-"+v[1];var S=Vb(s)+m;return{name:S,styles:s,next:yn}}var Jb=function(r){return r()},V0=Hu.useInsertionEffect?Hu.useInsertionEffect:!1,X0=V0||Jb,Vp=V0||A.useLayoutEffect,Q0=A.createContext(typeof HTMLElement<"u"?kb({key:"css"}):null);Q0.Provider;var of=function(r){return A.forwardRef(function(o,u){var s=A.useContext(Q0);return r(o,s,u)})},ii=A.createContext({}),cf={}.hasOwnProperty,qs="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",Wb=function(r,o){var u={};for(var s in o)cf.call(o,s)&&(u[s]=o[s]);return u[qs]=r,u},Fb=function(r){var o=r.cache,u=r.serialized,s=r.isStringTag;return rf(o,u,s),X0(function(){return uf(o,u,s)}),null},Pb=of(function(a,r,o){var u=a.css;typeof u=="string"&&r.registered[u]!==void 0&&(u=r.registered[u]);var s=a[qs],f=[u],d="";typeof a.className=="string"?d=Y0(r.registered,f,a.className):a.className!=null&&(d=a.className+" ");var p=ri(f,void 0,A.useContext(ii));d+=r.key+"-"+p.name;var g={};for(var m in a)cf.call(a,m)&&m!=="css"&&m!==qs&&(g[m]=a[m]);return g.className=d,o&&(g.ref=o),A.createElement(A.Fragment,null,A.createElement(Fb,{cache:r,serialized:p,isStringTag:typeof s=="string"}),A.createElement(s,g))}),Ib=Pb,Xp=function(r,o){var u=arguments;if(o==null||!cf.call(o,"css"))return A.createElement.apply(void 0,u);var s=u.length,f=new Array(s);f[0]=Ib,f[1]=Wb(r,o);for(var d=2;d<s;d++)f[d]=u[d];return A.createElement.apply(null,f)};(function(a){var r;r||(r=a.JSX||(a.JSX={}))})(Xp||(Xp={}));var eS=of(function(a,r){var o=a.styles,u=ri([o],void 0,A.useContext(ii)),s=A.useRef();return Vp(function(){var f=r.key+"-global",d=new r.sheet.constructor({key:f,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,g=document.querySelector('style[data-emotion="'+f+" "+u.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),g!==null&&(p=!0,g.setAttribute("data-emotion",f),d.hydrate([g])),s.current=[d,p],function(){d.flush()}},[r]),Vp(function(){var f=s.current,d=f[0],p=f[1];if(p){f[1]=!1;return}if(u.next!==void 0&&uf(r,u.next,!0),d.tags.length){var g=d.tags[d.tags.length-1].nextElementSibling;d.before=g,d.flush()}r.insert("",u,d,!1)},[r,u.name]),null});function tS(){for(var a=arguments.length,r=new Array(a),o=0;o<a;o++)r[o]=arguments[o];return ri(r)}function sf(){var a=tS.apply(void 0,arguments),r="animation-"+a.name;return{name:r,styles:"@keyframes "+r+"{"+a.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var nS=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,aS=q0(function(a){return nS.test(a)||a.charCodeAt(0)===111&&a.charCodeAt(1)===110&&a.charCodeAt(2)<91}),lS=aS,rS=function(r){return r!=="theme"},Qp=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?lS:rS},Zp=function(r,o,u){var s;if(o){var f=o.shouldForwardProp;s=r.__emotion_forwardProp&&f?function(d){return r.__emotion_forwardProp(d)&&f(d)}:f}return typeof s!="function"&&u&&(s=r.__emotion_forwardProp),s},iS=function(r){var o=r.cache,u=r.serialized,s=r.isStringTag;return rf(o,u,s),X0(function(){return uf(o,u,s)}),null},uS=function a(r,o){var u=r.__emotion_real===r,s=u&&r.__emotion_base||r,f,d;o!==void 0&&(f=o.label,d=o.target);var p=Zp(r,o,u),g=p||Qp(s),m=!g("as");return function(){var v=arguments,S=u&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(f!==void 0&&S.push("label:"+f+";"),v[0]==null||v[0].raw===void 0)S.push.apply(S,v);else{var T=v[0];S.push(T[0]);for(var N=v.length,O=1;O<N;O++)S.push(v[O],T[O])}var C=of(function(B,k,V){var L=m&&B.as||s,q="",z=[],W=B;if(B.theme==null){W={};for(var P in B)W[P]=B[P];W.theme=A.useContext(ii)}typeof B.className=="string"?q=Y0(k.registered,z,B.className):B.className!=null&&(q=B.className+" ");var ee=ri(S.concat(z),k.registered,W);q+=k.key+"-"+ee.name,d!==void 0&&(q+=" "+d);var te=m&&p===void 0?Qp(L):g,b={};for(var Z in B)m&&Z==="as"||te(Z)&&(b[Z]=B[Z]);return b.className=q,V&&(b.ref=V),A.createElement(A.Fragment,null,A.createElement(iS,{cache:k,serialized:ee,isStringTag:typeof L=="string"}),A.createElement(L,b))});return C.displayName=f!==void 0?f:"Styled("+(typeof s=="string"?s:s.displayName||s.name||"Component")+")",C.defaultProps=r.defaultProps,C.__emotion_real=C,C.__emotion_base=s,C.__emotion_styles=S,C.__emotion_forwardProp=p,Object.defineProperty(C,"toString",{value:function(){return"."+d}}),C.withComponent=function(B,k){var V=a(B,ku({},o,k,{shouldForwardProp:Zp(C,k,!0)}));return V.apply(void 0,S)},C}},oS=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],$s=uS.bind(null);oS.forEach(function(a){$s[a]=$s(a)});function cS(a){return a==null||Object.keys(a).length===0}function Z0(a){const{styles:r,defaultTheme:o={}}=a,u=typeof r=="function"?s=>r(cS(s)?o:s):r;return E.jsx(eS,{styles:u})}function K0(a,r){return $s(a,r)}function sS(a,r){Array.isArray(a.__emotion_styles)&&(a.__emotion_styles=r(a.__emotion_styles))}const Kp=[];function ya(a){return Kp[0]=a,ri(Kp)}var Rs={exports:{}},Be={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jp;function fS(){if(Jp)return Be;Jp=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),v=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),T=Symbol.for("react.view_transition"),N=Symbol.for("react.client.reference");function O(C){if(typeof C=="object"&&C!==null){var B=C.$$typeof;switch(B){case a:switch(C=C.type,C){case o:case s:case u:case g:case m:case T:return C;default:switch(C=C&&C.$$typeof,C){case d:case p:case S:case v:return C;case f:return C;default:return B}}case r:return B}}}return Be.ContextConsumer=f,Be.ContextProvider=d,Be.Element=a,Be.ForwardRef=p,Be.Fragment=o,Be.Lazy=S,Be.Memo=v,Be.Portal=r,Be.Profiler=s,Be.StrictMode=u,Be.Suspense=g,Be.SuspenseList=m,Be.isContextConsumer=function(C){return O(C)===f},Be.isContextProvider=function(C){return O(C)===d},Be.isElement=function(C){return typeof C=="object"&&C!==null&&C.$$typeof===a},Be.isForwardRef=function(C){return O(C)===p},Be.isFragment=function(C){return O(C)===o},Be.isLazy=function(C){return O(C)===S},Be.isMemo=function(C){return O(C)===v},Be.isPortal=function(C){return O(C)===r},Be.isProfiler=function(C){return O(C)===s},Be.isStrictMode=function(C){return O(C)===u},Be.isSuspense=function(C){return O(C)===g},Be.isSuspenseList=function(C){return O(C)===m},Be.isValidElementType=function(C){return typeof C=="string"||typeof C=="function"||C===o||C===s||C===u||C===g||C===m||typeof C=="object"&&C!==null&&(C.$$typeof===S||C.$$typeof===v||C.$$typeof===d||C.$$typeof===f||C.$$typeof===p||C.$$typeof===N||C.getModuleId!==void 0)},Be.typeOf=O,Be}var Wp;function dS(){return Wp||(Wp=1,Rs.exports=fS()),Rs.exports}var J0=dS();function vn(a){if(typeof a!="object"||a===null)return!1;const r=Object.getPrototypeOf(a);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in a)&&!(Symbol.iterator in a)}function W0(a){if(A.isValidElement(a)||J0.isValidElementType(a)||!vn(a))return a;const r={};return Object.keys(a).forEach(o=>{r[o]=W0(a[o])}),r}function $t(a,r,o={clone:!0}){const u=o.clone?{...a}:a;return vn(a)&&vn(r)&&Object.keys(r).forEach(s=>{A.isValidElement(r[s])||J0.isValidElementType(r[s])?u[s]=r[s]:vn(r[s])&&Object.prototype.hasOwnProperty.call(a,s)&&vn(a[s])?u[s]=$t(a[s],r[s],o):o.clone?u[s]=vn(r[s])?W0(r[s]):r[s]:u[s]=r[s]}),u}const hS=a=>{const r=Object.keys(a).map(o=>({key:o,val:a[o]}))||[];return r.sort((o,u)=>o.val-u.val),r.reduce((o,u)=>({...o,[u.key]:u.val}),{})};function mS(a){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:o="px",step:u=5,...s}=a,f=hS(r),d=Object.keys(f);function p(T){return`@media (min-width:${typeof r[T]=="number"?r[T]:T}${o})`}function g(T){return`@media (max-width:${(typeof r[T]=="number"?r[T]:T)-u/100}${o})`}function m(T,N){const O=d.indexOf(N);return`@media (min-width:${typeof r[T]=="number"?r[T]:T}${o}) and (max-width:${(O!==-1&&typeof r[d[O]]=="number"?r[d[O]]:N)-u/100}${o})`}function v(T){return d.indexOf(T)+1<d.length?m(T,d[d.indexOf(T)+1]):p(T)}function S(T){const N=d.indexOf(T);return N===0?p(d[1]):N===d.length-1?g(d[N]):m(T,d[d.indexOf(T)+1]).replace("@media","@media not all and")}return{keys:d,values:f,up:p,down:g,between:m,only:v,not:S,unit:o,...s}}function Fp(a,r){if(!a.containerQueries)return r;const o=Object.keys(r).filter(u=>u.startsWith("@container")).sort((u,s)=>{var d,p;const f=/min-width:\s*([0-9.]+)/;return+(((d=u.match(f))==null?void 0:d[1])||0)-+(((p=s.match(f))==null?void 0:p[1])||0)});return o.length?o.reduce((u,s)=>{const f=r[s];return delete u[s],u[s]=f,u},{...r}):r}function pS(a,r){return r==="@"||r.startsWith("@")&&(a.some(o=>r.startsWith(`@${o}`))||!!r.match(/^@\d/))}function gS(a,r){const o=r.match(/^@([^/]+)?\/?(.+)?$/);if(!o)return null;const[,u,s]=o,f=Number.isNaN(+u)?u||0:+u;return a.containerQueries(s).up(f)}function yS(a){const r=(f,d)=>f.replace("@media",d?`@container ${d}`:"@container");function o(f,d){f.up=(...p)=>r(a.breakpoints.up(...p),d),f.down=(...p)=>r(a.breakpoints.down(...p),d),f.between=(...p)=>r(a.breakpoints.between(...p),d),f.only=(...p)=>r(a.breakpoints.only(...p),d),f.not=(...p)=>{const g=r(a.breakpoints.not(...p),d);return g.includes("not all and")?g.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):g}}const u={},s=f=>(o(u,f),u);return o(s),{...a,containerQueries:s}}const vS={borderRadius:4};function Kr(a,r){return r?$t(a,r,{clone:!1}):a}const Zu={xs:0,sm:600,md:900,lg:1200,xl:1536},Pp={keys:["xs","sm","md","lg","xl"],up:a=>`@media (min-width:${Zu[a]}px)`},bS={containerQueries:a=>({up:r=>{let o=typeof r=="number"?r:Zu[r]||r;return typeof o=="number"&&(o=`${o}px`),a?`@container ${a} (min-width:${o})`:`@container (min-width:${o})`}})};function $n(a,r,o){const u=a.theme||{};if(Array.isArray(r)){const f=u.breakpoints||Pp;return r.reduce((d,p,g)=>(d[f.up(f.keys[g])]=o(r[g]),d),{})}if(typeof r=="object"){const f=u.breakpoints||Pp;return Object.keys(r).reduce((d,p)=>{if(pS(f.keys,p)){const g=gS(u.containerQueries?u:bS,p);g&&(d[g]=o(r[p],p))}else if(Object.keys(f.values||Zu).includes(p)){const g=f.up(p);d[g]=o(r[p],p)}else{const g=p;d[g]=r[g]}return d},{})}return o(r)}function SS(a={}){var o;return((o=a.keys)==null?void 0:o.reduce((u,s)=>{const f=a.up(s);return u[f]={},u},{}))||{}}function Ip(a,r){return a.reduce((o,u)=>{const s=o[u];return(!s||Object.keys(s).length===0)&&delete o[u],o},r)}function Yt(a){if(typeof a!="string")throw new Error($a(7));return a.charAt(0).toUpperCase()+a.slice(1)}function Ku(a,r,o=!0){if(!r||typeof r!="string")return null;if(a&&a.vars&&o){const u=`vars.${r}`.split(".").reduce((s,f)=>s&&s[f]?s[f]:null,a);if(u!=null)return u}return r.split(".").reduce((u,s)=>u&&u[s]!=null?u[s]:null,a)}function $u(a,r,o,u=o){let s;return typeof a=="function"?s=a(o):Array.isArray(a)?s=a[o]||u:s=Ku(a,o)||u,r&&(s=r(s,u,a)),s}function We(a){const{prop:r,cssProperty:o=a.prop,themeKey:u,transform:s}=a,f=d=>{if(d[r]==null)return null;const p=d[r],g=d.theme,m=Ku(g,u)||{};return $n(d,p,S=>{let T=$u(m,s,S);return S===T&&typeof S=="string"&&(T=$u(m,s,`${r}${S==="default"?"":Yt(S)}`,S)),o===!1?T:{[o]:T}})};return f.propTypes={},f.filterProps=[r],f}function xS(a){const r={};return o=>(r[o]===void 0&&(r[o]=a(o)),r[o])}const ES={m:"margin",p:"padding"},TS={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},e0={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},CS=xS(a=>{if(a.length>2)if(e0[a])a=e0[a];else return[a];const[r,o]=a.split(""),u=ES[r],s=TS[o]||"";return Array.isArray(s)?s.map(f=>u+f):[u+s]}),ff=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],df=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...ff,...df];function ui(a,r,o,u){const s=Ku(a,r,!0)??o;return typeof s=="number"||typeof s=="string"?f=>typeof f=="string"?f:typeof s=="string"?s.startsWith("var(")&&f===0?0:s.startsWith("var(")&&f===1?s:`calc(${f} * ${s})`:s*f:Array.isArray(s)?f=>{if(typeof f=="string")return f;const d=Math.abs(f),p=s[d];return f>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof s=="function"?s:()=>{}}function hf(a){return ui(a,"spacing",8)}function oi(a,r){return typeof r=="string"||r==null?r:a(r)}function _S(a,r){return o=>a.reduce((u,s)=>(u[s]=oi(r,o),u),{})}function AS(a,r,o,u){if(!r.includes(o))return null;const s=CS(o),f=_S(s,u),d=a[o];return $n(a,d,f)}function F0(a,r){const o=hf(a.theme);return Object.keys(a).map(u=>AS(a,r,u,o)).reduce(Kr,{})}function Qe(a){return F0(a,ff)}Qe.propTypes={};Qe.filterProps=ff;function Ze(a){return F0(a,df)}Ze.propTypes={};Ze.filterProps=df;function P0(a=8,r=hf({spacing:a})){if(a.mui)return a;const o=(...u)=>(u.length===0?[1]:u).map(f=>{const d=r(f);return typeof d=="number"?`${d}px`:d}).join(" ");return o.mui=!0,o}function Ju(...a){const r=a.reduce((u,s)=>(s.filterProps.forEach(f=>{u[f]=s}),u),{}),o=u=>Object.keys(u).reduce((s,f)=>r[f]?Kr(s,r[f](u)):s,{});return o.propTypes={},o.filterProps=a.reduce((u,s)=>u.concat(s.filterProps),[]),o}function en(a){return typeof a!="number"?a:`${a}px solid`}function tn(a,r){return We({prop:a,themeKey:"borders",transform:r})}const RS=tn("border",en),OS=tn("borderTop",en),MS=tn("borderRight",en),zS=tn("borderBottom",en),DS=tn("borderLeft",en),wS=tn("borderColor"),NS=tn("borderTopColor"),jS=tn("borderRightColor"),BS=tn("borderBottomColor"),US=tn("borderLeftColor"),HS=tn("outline",en),LS=tn("outlineColor"),Wu=a=>{if(a.borderRadius!==void 0&&a.borderRadius!==null){const r=ui(a.theme,"shape.borderRadius",4),o=u=>({borderRadius:oi(r,u)});return $n(a,a.borderRadius,o)}return null};Wu.propTypes={};Wu.filterProps=["borderRadius"];Ju(RS,OS,MS,zS,DS,wS,NS,jS,BS,US,Wu,HS,LS);const Fu=a=>{if(a.gap!==void 0&&a.gap!==null){const r=ui(a.theme,"spacing",8),o=u=>({gap:oi(r,u)});return $n(a,a.gap,o)}return null};Fu.propTypes={};Fu.filterProps=["gap"];const Pu=a=>{if(a.columnGap!==void 0&&a.columnGap!==null){const r=ui(a.theme,"spacing",8),o=u=>({columnGap:oi(r,u)});return $n(a,a.columnGap,o)}return null};Pu.propTypes={};Pu.filterProps=["columnGap"];const Iu=a=>{if(a.rowGap!==void 0&&a.rowGap!==null){const r=ui(a.theme,"spacing",8),o=u=>({rowGap:oi(r,u)});return $n(a,a.rowGap,o)}return null};Iu.propTypes={};Iu.filterProps=["rowGap"];const kS=We({prop:"gridColumn"}),qS=We({prop:"gridRow"}),$S=We({prop:"gridAutoFlow"}),YS=We({prop:"gridAutoColumns"}),GS=We({prop:"gridAutoRows"}),VS=We({prop:"gridTemplateColumns"}),XS=We({prop:"gridTemplateRows"}),QS=We({prop:"gridTemplateAreas"}),ZS=We({prop:"gridArea"});Ju(Fu,Pu,Iu,kS,qS,$S,YS,GS,VS,XS,QS,ZS);function Hl(a,r){return r==="grey"?r:a}const KS=We({prop:"color",themeKey:"palette",transform:Hl}),JS=We({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Hl}),WS=We({prop:"backgroundColor",themeKey:"palette",transform:Hl});Ju(KS,JS,WS);function kt(a){return a<=1&&a!==0?`${a*100}%`:a}const FS=We({prop:"width",transform:kt}),mf=a=>{if(a.maxWidth!==void 0&&a.maxWidth!==null){const r=o=>{var s,f,d,p,g;const u=((d=(f=(s=a.theme)==null?void 0:s.breakpoints)==null?void 0:f.values)==null?void 0:d[o])||Zu[o];return u?((g=(p=a.theme)==null?void 0:p.breakpoints)==null?void 0:g.unit)!=="px"?{maxWidth:`${u}${a.theme.breakpoints.unit}`}:{maxWidth:u}:{maxWidth:kt(o)}};return $n(a,a.maxWidth,r)}return null};mf.filterProps=["maxWidth"];const PS=We({prop:"minWidth",transform:kt}),IS=We({prop:"height",transform:kt}),e2=We({prop:"maxHeight",transform:kt}),t2=We({prop:"minHeight",transform:kt});We({prop:"size",cssProperty:"width",transform:kt});We({prop:"size",cssProperty:"height",transform:kt});const n2=We({prop:"boxSizing"});Ju(FS,mf,PS,IS,e2,t2,n2);const ci={border:{themeKey:"borders",transform:en},borderTop:{themeKey:"borders",transform:en},borderRight:{themeKey:"borders",transform:en},borderBottom:{themeKey:"borders",transform:en},borderLeft:{themeKey:"borders",transform:en},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:en},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Wu},color:{themeKey:"palette",transform:Hl},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Hl},backgroundColor:{themeKey:"palette",transform:Hl},p:{style:Ze},pt:{style:Ze},pr:{style:Ze},pb:{style:Ze},pl:{style:Ze},px:{style:Ze},py:{style:Ze},padding:{style:Ze},paddingTop:{style:Ze},paddingRight:{style:Ze},paddingBottom:{style:Ze},paddingLeft:{style:Ze},paddingX:{style:Ze},paddingY:{style:Ze},paddingInline:{style:Ze},paddingInlineStart:{style:Ze},paddingInlineEnd:{style:Ze},paddingBlock:{style:Ze},paddingBlockStart:{style:Ze},paddingBlockEnd:{style:Ze},m:{style:Qe},mt:{style:Qe},mr:{style:Qe},mb:{style:Qe},ml:{style:Qe},mx:{style:Qe},my:{style:Qe},margin:{style:Qe},marginTop:{style:Qe},marginRight:{style:Qe},marginBottom:{style:Qe},marginLeft:{style:Qe},marginX:{style:Qe},marginY:{style:Qe},marginInline:{style:Qe},marginInlineStart:{style:Qe},marginInlineEnd:{style:Qe},marginBlock:{style:Qe},marginBlockStart:{style:Qe},marginBlockEnd:{style:Qe},displayPrint:{cssProperty:!1,transform:a=>({"@media print":{display:a}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Fu},rowGap:{style:Iu},columnGap:{style:Pu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:kt},maxWidth:{style:mf},minWidth:{transform:kt},height:{transform:kt},maxHeight:{transform:kt},minHeight:{transform:kt},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function a2(...a){const r=a.reduce((u,s)=>u.concat(Object.keys(s)),[]),o=new Set(r);return a.every(u=>o.size===Object.keys(u).length)}function l2(a,r){return typeof a=="function"?a(r):a}function r2(){function a(o,u,s,f){const d={[o]:u,theme:s},p=f[o];if(!p)return{[o]:u};const{cssProperty:g=o,themeKey:m,transform:v,style:S}=p;if(u==null)return null;if(m==="typography"&&u==="inherit")return{[o]:u};const T=Ku(s,m)||{};return S?S(d):$n(d,u,O=>{let C=$u(T,v,O);return O===C&&typeof O=="string"&&(C=$u(T,v,`${o}${O==="default"?"":Yt(O)}`,O)),g===!1?C:{[g]:C}})}function r(o){const{sx:u,theme:s={},nested:f}=o||{};if(!u)return null;const d=s.unstable_sxConfig??ci;function p(g){let m=g;if(typeof g=="function")m=g(s);else if(typeof g!="object")return g;if(!m)return null;const v=SS(s.breakpoints),S=Object.keys(v);let T=v;return Object.keys(m).forEach(N=>{const O=l2(m[N],s);if(O!=null)if(typeof O=="object")if(d[N])T=Kr(T,a(N,O,s,d));else{const C=$n({theme:s},O,B=>({[N]:B}));a2(C,O)?T[N]=r({sx:O,theme:s,nested:!0}):T=Kr(T,C)}else T=Kr(T,a(N,O,s,d))}),!f&&s.modularCssLayers?{"@layer sx":Fp(s,Ip(S,T))}:Fp(s,Ip(S,T))}return Array.isArray(u)?u.map(p):p(u)}return r}const va=r2();va.filterProps=["sx"];function i2(a,r){var u;const o=this;if(o.vars){if(!((u=o.colorSchemes)!=null&&u[a])||typeof o.getColorSchemeSelector!="function")return{};let s=o.getColorSchemeSelector(a);return s==="&"?r:((s.includes("data-")||s.includes("."))&&(s=`*:where(${s.replace(/\s*&$/,"")}) &`),{[s]:r})}return o.palette.mode===a?r:{}}function pf(a={},...r){const{breakpoints:o={},palette:u={},spacing:s,shape:f={},...d}=a,p=mS(o),g=P0(s);let m=$t({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...u},spacing:g,shape:{...vS,...f}},d);return m=yS(m),m.applyStyles=i2,m=r.reduce((v,S)=>$t(v,S),m),m.unstable_sxConfig={...ci,...d==null?void 0:d.unstable_sxConfig},m.unstable_sx=function(S){return va({sx:S,theme:this})},m}function u2(a){return Object.keys(a).length===0}function gf(a=null){const r=A.useContext(ii);return!r||u2(r)?a:r}const o2=pf();function I0(a=o2){return gf(a)}function Os(a){const r=ya(a);return a!==r&&r.styles?(r.styles.match(/^@layer\s+[^{]*$/)||(r.styles=`@layer global{${r.styles}}`),r):a}function eg({styles:a,themeId:r,defaultTheme:o={}}){const u=I0(o),s=r&&u[r]||u;let f=typeof a=="function"?a(s):a;return s.modularCssLayers&&(Array.isArray(f)?f=f.map(d=>Os(typeof d=="function"?d(s):d)):f=Os(f)),E.jsx(Z0,{styles:f})}const c2=a=>{var u;const r={systemProps:{},otherProps:{}},o=((u=a==null?void 0:a.theme)==null?void 0:u.unstable_sxConfig)??ci;return Object.keys(a).forEach(s=>{o[s]?r.systemProps[s]=a[s]:r.otherProps[s]=a[s]}),r};function s2(a){const{sx:r,...o}=a,{systemProps:u,otherProps:s}=c2(o);let f;return Array.isArray(r)?f=[u,...r]:typeof r=="function"?f=(...d)=>{const p=r(...d);return vn(p)?{...u,...p}:u}:f={...u,...r},{...s,sx:f}}const t0=a=>a,f2=()=>{let a=t0;return{configure(r){a=r},generate(r){return a(r)},reset(){a=t0}}},tg=f2();function ng(a){var r,o,u="";if(typeof a=="string"||typeof a=="number")u+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(r=0;r<s;r++)a[r]&&(o=ng(a[r]))&&(u&&(u+=" "),u+=o)}else for(o in a)a[o]&&(u&&(u+=" "),u+=o);return u}function xt(){for(var a,r,o=0,u="",s=arguments.length;o<s;o++)(a=arguments[o])&&(r=ng(a))&&(u&&(u+=" "),u+=r);return u}function d2(a={}){const{themeId:r,defaultTheme:o,defaultClassName:u="MuiBox-root",generateClassName:s}=a,f=K0("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(va);return A.forwardRef(function(g,m){const v=I0(o),{className:S,component:T="div",...N}=s2(g);return E.jsx(f,{as:T,ref:m,className:xt(S,s?s(u):u),theme:r&&v[r]||v,...N})})}const h2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function si(a,r,o="Mui"){const u=h2[r];return u?`${o}-${u}`:`${tg.generate(a)}-${r}`}function Gl(a,r,o="Mui"){const u={};return r.forEach(s=>{u[s]=si(a,s,o)}),u}function ag(a){const{variants:r,...o}=a,u={variants:r,style:ya(o),isProcessed:!0};return u.style===o||r&&r.forEach(s=>{typeof s.style!="function"&&(s.style=ya(s.style))}),u}const m2=pf();function Ms(a){return a!=="ownerState"&&a!=="theme"&&a!=="sx"&&a!=="as"}function ka(a,r){return r&&a&&typeof a=="object"&&a.styles&&!a.styles.startsWith("@layer")&&(a.styles=`@layer ${r}{${String(a.styles)}}`),a}function p2(a){return a?(r,o)=>o[a]:null}function g2(a,r,o){a.theme=b2(a.theme)?o:a.theme[r]||a.theme}function ju(a,r,o){const u=typeof r=="function"?r(a):r;if(Array.isArray(u))return u.flatMap(s=>ju(a,s,o));if(Array.isArray(u==null?void 0:u.variants)){let s;if(u.isProcessed)s=o?ka(u.style,o):u.style;else{const{variants:f,...d}=u;s=o?ka(ya(d),o):d}return lg(a,u.variants,[s],o)}return u!=null&&u.isProcessed?o?ka(ya(u.style),o):u.style:o?ka(ya(u),o):u}function lg(a,r,o=[],u=void 0){var f;let s;e:for(let d=0;d<r.length;d+=1){const p=r[d];if(typeof p.props=="function"){if(s??(s={...a,...a.ownerState,ownerState:a.ownerState}),!p.props(s))continue}else for(const g in p.props)if(a[g]!==p.props[g]&&((f=a.ownerState)==null?void 0:f[g])!==p.props[g])continue e;typeof p.style=="function"?(s??(s={...a,...a.ownerState,ownerState:a.ownerState}),o.push(u?ka(ya(p.style(s)),u):p.style(s))):o.push(u?ka(ya(p.style),u):p.style)}return o}function y2(a={}){const{themeId:r,defaultTheme:o=m2,rootShouldForwardProp:u=Ms,slotShouldForwardProp:s=Ms}=a;function f(p){g2(p,r,o)}return(p,g={})=>{sS(p,W=>W.filter(P=>P!==va));const{name:m,slot:v,skipVariantsResolver:S,skipSx:T,overridesResolver:N=p2(x2(v)),...O}=g,C=m&&m.startsWith("Mui")||v?"components":"custom",B=S!==void 0?S:v&&v!=="Root"&&v!=="root"||!1,k=T||!1;let V=Ms;v==="Root"||v==="root"?V=u:v?V=s:S2(p)&&(V=void 0);const L=K0(p,{shouldForwardProp:V,label:v2(),...O}),q=W=>{if(W.__emotion_real===W)return W;if(typeof W=="function")return function(ee){return ju(ee,W,ee.theme.modularCssLayers?C:void 0)};if(vn(W)){const P=ag(W);return function(te){return P.variants?ju(te,P,te.theme.modularCssLayers?C:void 0):te.theme.modularCssLayers?ka(P.style,C):P.style}}return W},z=(...W)=>{const P=[],ee=W.map(q),te=[];if(P.push(f),m&&N&&te.push(function(se){var K,ne;const pe=(ne=(K=se.theme.components)==null?void 0:K[m])==null?void 0:ne.styleOverrides;if(!pe)return null;const H={};for(const ce in pe)H[ce]=ju(se,pe[ce],se.theme.modularCssLayers?"theme":void 0);return N(se,H)}),m&&!B&&te.push(function(se){var H,K;const Ee=se.theme,pe=(K=(H=Ee==null?void 0:Ee.components)==null?void 0:H[m])==null?void 0:K.variants;return pe?lg(se,pe,[],se.theme.modularCssLayers?"theme":void 0):null}),k||te.push(va),Array.isArray(ee[0])){const I=ee.shift(),se=new Array(P.length).fill(""),Ee=new Array(te.length).fill("");let pe;pe=[...se,...I,...Ee],pe.raw=[...se,...I.raw,...Ee],P.unshift(pe)}const b=[...P,...ee,...te],Z=L(...b);return p.muiName&&(Z.muiName=p.muiName),Z};return L.withConfig&&(z.withConfig=L.withConfig),z}}function v2(a,r){return void 0}function b2(a){for(const r in a)return!1;return!0}function S2(a){return typeof a=="string"&&a.charCodeAt(0)>96}function x2(a){return a&&a.charAt(0).toLowerCase()+a.slice(1)}function Ys(a,r,o=!1){const u={...r};for(const s in a)if(Object.prototype.hasOwnProperty.call(a,s)){const f=s;if(f==="components"||f==="slots")u[f]={...a[f],...u[f]};else if(f==="componentsProps"||f==="slotProps"){const d=a[f],p=r[f];if(!p)u[f]=d||{};else if(!d)u[f]=p;else{u[f]={...p};for(const g in d)if(Object.prototype.hasOwnProperty.call(d,g)){const m=g;u[f][m]=Ys(d[m],p[m],o)}}}else f==="className"&&o&&r.className?u.className=xt(a==null?void 0:a.className,r==null?void 0:r.className):f==="style"&&o&&r.style?u.style={...a==null?void 0:a.style,...r==null?void 0:r.style}:u[f]===void 0&&(u[f]=a[f])}return u}const yf=typeof window<"u"?A.useLayoutEffect:A.useEffect;function E2(a,r=Number.MIN_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(a,o))}function vf(a,r=0,o=1){return E2(a,r,o)}function T2(a){a=a.slice(1);const r=new RegExp(`.{1,${a.length>=6?2:1}}`,"g");let o=a.match(r);return o&&o[0].length===1&&(o=o.map(u=>u+u)),o?`rgb${o.length===4?"a":""}(${o.map((u,s)=>s<3?parseInt(u,16):Math.round(parseInt(u,16)/255*1e3)/1e3).join(", ")})`:""}function ba(a){if(a.type)return a;if(a.charAt(0)==="#")return ba(T2(a));const r=a.indexOf("("),o=a.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw new Error($a(9,a));let u=a.substring(r+1,a.length-1),s;if(o==="color"){if(u=u.split(" "),s=u.shift(),u.length===4&&u[3].charAt(0)==="/"&&(u[3]=u[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(s))throw new Error($a(10,s))}else u=u.split(",");return u=u.map(f=>parseFloat(f)),{type:o,values:u,colorSpace:s}}const C2=a=>{const r=ba(a);return r.values.slice(0,3).map((o,u)=>r.type.includes("hsl")&&u!==0?`${o}%`:o).join(" ")},Qr=(a,r)=>{try{return C2(a)}catch{return a}};function eo(a){const{type:r,colorSpace:o}=a;let{values:u}=a;return r.includes("rgb")?u=u.map((s,f)=>f<3?parseInt(s,10):s):r.includes("hsl")&&(u[1]=`${u[1]}%`,u[2]=`${u[2]}%`),r.includes("color")?u=`${o} ${u.join(" ")}`:u=`${u.join(", ")}`,`${r}(${u})`}function rg(a){a=ba(a);const{values:r}=a,o=r[0],u=r[1]/100,s=r[2]/100,f=u*Math.min(s,1-s),d=(m,v=(m+o/30)%12)=>s-f*Math.max(Math.min(v-3,9-v,1),-1);let p="rgb";const g=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return a.type==="hsla"&&(p+="a",g.push(r[3])),eo({type:p,values:g})}function Gs(a){a=ba(a);let r=a.type==="hsl"||a.type==="hsla"?ba(rg(a)).values:a.values;return r=r.map(o=>(a.type!=="color"&&(o/=255),o<=.03928?o/12.92:((o+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function _2(a,r){const o=Gs(a),u=Gs(r);return(Math.max(o,u)+.05)/(Math.min(o,u)+.05)}function Vs(a,r){return a=ba(a),r=vf(r),(a.type==="rgb"||a.type==="hsl")&&(a.type+="a"),a.type==="color"?a.values[3]=`/${r}`:a.values[3]=r,eo(a)}function Ou(a,r,o){try{return Vs(a,r)}catch{return a}}function to(a,r){if(a=ba(a),r=vf(r),a.type.includes("hsl"))a.values[2]*=1-r;else if(a.type.includes("rgb")||a.type.includes("color"))for(let o=0;o<3;o+=1)a.values[o]*=1-r;return eo(a)}function He(a,r,o){try{return to(a,r)}catch{return a}}function no(a,r){if(a=ba(a),r=vf(r),a.type.includes("hsl"))a.values[2]+=(100-a.values[2])*r;else if(a.type.includes("rgb"))for(let o=0;o<3;o+=1)a.values[o]+=(255-a.values[o])*r;else if(a.type.includes("color"))for(let o=0;o<3;o+=1)a.values[o]+=(1-a.values[o])*r;return eo(a)}function Le(a,r,o){try{return no(a,r)}catch{return a}}function A2(a,r=.15){return Gs(a)>.5?to(a,r):no(a,r)}function Mu(a,r,o){try{return A2(a,r)}catch{return a}}const ig=A.createContext(null);function bf(){return A.useContext(ig)}const R2=typeof Symbol=="function"&&Symbol.for,O2=R2?Symbol.for("mui.nested"):"__THEME_NESTED__";function M2(a,r){return typeof r=="function"?r(a):{...a,...r}}function z2(a){const{children:r,theme:o}=a,u=bf(),s=A.useMemo(()=>{const f=u===null?{...o}:M2(u,o);return f!=null&&(f[O2]=u!==null),f},[o,u]);return E.jsx(ig.Provider,{value:s,children:r})}const D2=A.createContext();function w2({value:a,...r}){return E.jsx(D2.Provider,{value:a??!0,...r})}const ug=A.createContext(void 0);function N2({value:a,children:r}){return E.jsx(ug.Provider,{value:a,children:r})}function j2(a){const{theme:r,name:o,props:u}=a;if(!r||!r.components||!r.components[o])return u;const s=r.components[o];return s.defaultProps?Ys(s.defaultProps,u,r.components.mergeClassNameAndStyle):!s.styleOverrides&&!s.variants?Ys(s,u,r.components.mergeClassNameAndStyle):u}function B2({props:a,name:r}){const o=A.useContext(ug);return j2({props:a,name:r,theme:{components:o}})}let n0=0;function U2(a){const[r,o]=A.useState(a),u=a||r;return A.useEffect(()=>{r==null&&(n0+=1,o(`mui-${n0}`))},[r]),u}const H2={...Hu},a0=H2.useId;function L2(a){return a0!==void 0?a0():U2(a)}function k2(a){const r=gf(),o=L2()||"",{modularCssLayers:u}=a;let s="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!u||r!==null?s="":typeof u=="string"?s=u.replace(/mui(?!\.)/g,s):s=`@layer ${s};`,yf(()=>{var p,g;const f=document.querySelector("head");if(!f)return;const d=f.firstChild;if(s){if(d&&((p=d.hasAttribute)!=null&&p.call(d,"data-mui-layer-order"))&&d.getAttribute("data-mui-layer-order")===o)return;const m=document.createElement("style");m.setAttribute("data-mui-layer-order",o),m.textContent=s,f.prepend(m)}else(g=f.querySelector(`style[data-mui-layer-order="${o}"]`))==null||g.remove()},[s,o]),s?E.jsx(eg,{styles:s}):null}const l0={};function r0(a,r,o,u=!1){return A.useMemo(()=>{const s=a&&r[a]||r;if(typeof o=="function"){const f=o(s),d=a?{...r,[a]:f}:f;return u?()=>d:d}return a?{...r,[a]:o}:{...r,...o}},[a,r,o,u])}function og(a){const{children:r,theme:o,themeId:u}=a,s=gf(l0),f=bf()||l0,d=r0(u,s,o),p=r0(u,f,o,!0),g=(u?d[u]:d).direction==="rtl",m=k2(d);return E.jsx(z2,{theme:p,children:E.jsx(ii.Provider,{value:d,children:E.jsx(w2,{value:g,children:E.jsxs(N2,{value:u?d[u].components:d.components,children:[m,r]})})})})}const i0={theme:void 0};function q2(a){let r,o;return function(s){let f=r;return(f===void 0||s.theme!==o)&&(i0.theme=s.theme,f=ag(a(i0)),r=f,o=s.theme),f}}const Sf="mode",xf="color-scheme",$2="data-color-scheme";function Y2(a){const{defaultMode:r="system",defaultLightColorScheme:o="light",defaultDarkColorScheme:u="dark",modeStorageKey:s=Sf,colorSchemeStorageKey:f=xf,attribute:d=$2,colorSchemeNode:p="document.documentElement",nonce:g}=a||{};let m="",v=d;if(d==="class"&&(v=".%s"),d==="data"&&(v="[data-%s]"),v.startsWith(".")){const T=v.substring(1);m+=`${p}.classList.remove('${T}'.replace('%s', light), '${T}'.replace('%s', dark));
      ${p}.classList.add('${T}'.replace('%s', colorScheme));`}const S=v.match(/\[([^\]]+)\]/);if(S){const[T,N]=S[1].split("=");N||(m+=`${p}.removeAttribute('${T}'.replace('%s', light));
      ${p}.removeAttribute('${T}'.replace('%s', dark));`),m+=`
      ${p}.setAttribute('${T}'.replace('%s', colorScheme), ${N?`${N}.replace('%s', colorScheme)`:'""'});`}else m+=`${p}.setAttribute('${v}', colorScheme);`;return E.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?g:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${s}') || '${r}';
  const dark = localStorage.getItem('${f}-dark') || '${u}';
  const light = localStorage.getItem('${f}-light') || '${o}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${m}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function G2(){}const V2=({key:a,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(o){if(typeof window>"u")return;if(!r)return o;let u;try{u=r.localStorage.getItem(a)}catch{}return u||o},set:o=>{if(r)try{r.localStorage.setItem(a,o)}catch{}},subscribe:o=>{if(!r)return G2;const u=s=>{const f=s.newValue;s.key===a&&o(f)};return r.addEventListener("storage",u),()=>{r.removeEventListener("storage",u)}}});function zs(){}function u0(a){if(typeof window<"u"&&typeof window.matchMedia=="function"&&a==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function cg(a,r){if(a.mode==="light"||a.mode==="system"&&a.systemMode==="light")return r("light");if(a.mode==="dark"||a.mode==="system"&&a.systemMode==="dark")return r("dark")}function X2(a){return cg(a,r=>{if(r==="light")return a.lightColorScheme;if(r==="dark")return a.darkColorScheme})}function Q2(a){const{defaultMode:r="light",defaultLightColorScheme:o,defaultDarkColorScheme:u,supportedColorSchemes:s=[],modeStorageKey:f=Sf,colorSchemeStorageKey:d=xf,storageWindow:p=typeof window>"u"?void 0:window,storageManager:g=V2,noSsr:m=!1}=a,v=s.join(","),S=s.length>1,T=A.useMemo(()=>g==null?void 0:g({key:f,storageWindow:p}),[g,f,p]),N=A.useMemo(()=>g==null?void 0:g({key:`${d}-light`,storageWindow:p}),[g,d,p]),O=A.useMemo(()=>g==null?void 0:g({key:`${d}-dark`,storageWindow:p}),[g,d,p]),[C,B]=A.useState(()=>{const ee=(T==null?void 0:T.get(r))||r,te=(N==null?void 0:N.get(o))||o,b=(O==null?void 0:O.get(u))||u;return{mode:ee,systemMode:u0(ee),lightColorScheme:te,darkColorScheme:b}}),[k,V]=A.useState(m||!S);A.useEffect(()=>{V(!0)},[]);const L=X2(C),q=A.useCallback(ee=>{B(te=>{if(ee===te.mode)return te;const b=ee??r;return T==null||T.set(b),{...te,mode:b,systemMode:u0(b)}})},[T,r]),z=A.useCallback(ee=>{ee?typeof ee=="string"?ee&&!v.includes(ee)?console.error(`\`${ee}\` does not exist in \`theme.colorSchemes\`.`):B(te=>{const b={...te};return cg(te,Z=>{Z==="light"&&(N==null||N.set(ee),b.lightColorScheme=ee),Z==="dark"&&(O==null||O.set(ee),b.darkColorScheme=ee)}),b}):B(te=>{const b={...te},Z=ee.light===null?o:ee.light,I=ee.dark===null?u:ee.dark;return Z&&(v.includes(Z)?(b.lightColorScheme=Z,N==null||N.set(Z)):console.error(`\`${Z}\` does not exist in \`theme.colorSchemes\`.`)),I&&(v.includes(I)?(b.darkColorScheme=I,O==null||O.set(I)):console.error(`\`${I}\` does not exist in \`theme.colorSchemes\`.`)),b}):B(te=>(N==null||N.set(o),O==null||O.set(u),{...te,lightColorScheme:o,darkColorScheme:u}))},[v,N,O,o,u]),W=A.useCallback(ee=>{C.mode==="system"&&B(te=>{const b=ee!=null&&ee.matches?"dark":"light";return te.systemMode===b?te:{...te,systemMode:b}})},[C.mode]),P=A.useRef(W);return P.current=W,A.useEffect(()=>{if(typeof window.matchMedia!="function"||!S)return;const ee=(...b)=>P.current(...b),te=window.matchMedia("(prefers-color-scheme: dark)");return te.addListener(ee),ee(te),()=>{te.removeListener(ee)}},[S]),A.useEffect(()=>{if(S){const ee=(T==null?void 0:T.subscribe(Z=>{(!Z||["light","dark","system"].includes(Z))&&q(Z||r)}))||zs,te=(N==null?void 0:N.subscribe(Z=>{(!Z||v.match(Z))&&z({light:Z})}))||zs,b=(O==null?void 0:O.subscribe(Z=>{(!Z||v.match(Z))&&z({dark:Z})}))||zs;return()=>{ee(),te(),b()}}},[z,q,v,r,p,S,T,N,O]),{...C,mode:k?C.mode:void 0,systemMode:k?C.systemMode:void 0,colorScheme:k?L:void 0,setMode:q,setColorScheme:z}}const Z2="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function K2(a){const{themeId:r,theme:o={},modeStorageKey:u=Sf,colorSchemeStorageKey:s=xf,disableTransitionOnChange:f=!1,defaultColorScheme:d,resolveTheme:p}=a,g={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},m=A.createContext(void 0),v=()=>A.useContext(m)||g,S={},T={};function N(k){var Xn,ht,an,mt;const{children:V,theme:L,modeStorageKey:q=u,colorSchemeStorageKey:z=s,disableTransitionOnChange:W=f,storageManager:P,storageWindow:ee=typeof window>"u"?void 0:window,documentNode:te=typeof document>"u"?void 0:document,colorSchemeNode:b=typeof document>"u"?void 0:document.documentElement,disableNestedContext:Z=!1,disableStyleSheetGeneration:I=!1,defaultMode:se="system",forceThemeRerender:Ee=!1,noSsr:pe}=k,H=A.useRef(!1),K=bf(),ne=A.useContext(m),ce=!!ne&&!Z,_=A.useMemo(()=>L||(typeof o=="function"?o():o),[L]),X=_[r],J=X||_,{colorSchemes:F=S,components:ie=T,cssVarPrefix:le}=J,re=Object.keys(F).filter(pt=>!!F[pt]).join(","),Ce=A.useMemo(()=>re.split(","),[re]),Me=typeof d=="string"?d:d.light,Dt=typeof d=="string"?d:d.dark,Sa=F[Me]&&F[Dt]?se:((ht=(Xn=F[J.defaultColorScheme])==null?void 0:Xn.palette)==null?void 0:ht.mode)||((an=J.palette)==null?void 0:an.mode),{mode:Yn,setMode:Gn,systemMode:Vn,lightColorScheme:on,darkColorScheme:Va,colorScheme:Vl,setColorScheme:at}=Q2({supportedColorSchemes:Ce,defaultLightColorScheme:Me,defaultDarkColorScheme:Dt,modeStorageKey:q,colorSchemeStorageKey:z,defaultMode:Sa,storageManager:P,storageWindow:ee,noSsr:pe});let nn=Yn,dt=Vl;ce&&(nn=ne.mode,dt=ne.colorScheme);let cn=dt||J.defaultColorScheme;J.vars&&!Ee&&(cn=J.defaultColorScheme);const Gt=A.useMemo(()=>{var xn;const pt=((xn=J.generateThemeVars)==null?void 0:xn.call(J))||J.vars,Ye={...J,components:ie,colorSchemes:F,cssVarPrefix:le,vars:pt};if(typeof Ye.generateSpacing=="function"&&(Ye.spacing=Ye.generateSpacing()),cn){const Et=F[cn];Et&&typeof Et=="object"&&Object.keys(Et).forEach(gt=>{Et[gt]&&typeof Et[gt]=="object"?Ye[gt]={...Ye[gt],...Et[gt]}:Ye[gt]=Et[gt]})}return p?p(Ye):Ye},[J,cn,ie,F,le]),me=J.colorSchemeSelector;yf(()=>{if(dt&&b&&me&&me!=="media"){const pt=me;let Ye=me;if(pt==="class"&&(Ye=".%s"),pt==="data"&&(Ye="[data-%s]"),pt!=null&&pt.startsWith("data-")&&!pt.includes("%s")&&(Ye=`[${pt}="%s"]`),Ye.startsWith("."))b.classList.remove(...Ce.map(xn=>Ye.substring(1).replace("%s",xn))),b.classList.add(Ye.substring(1).replace("%s",dt));else{const xn=Ye.replace("%s",dt).match(/\[([^\]]+)\]/);if(xn){const[Et,gt]=xn[1].split("=");gt||Ce.forEach(En=>{b.removeAttribute(Et.replace(dt,En))}),b.setAttribute(Et,gt?gt.replace(/"|'/g,""):"")}else b.setAttribute(Ye,dt)}}},[dt,me,b,Ce]),A.useEffect(()=>{let pt;if(W&&H.current&&te){const Ye=te.createElement("style");Ye.appendChild(te.createTextNode(Z2)),te.head.appendChild(Ye),window.getComputedStyle(te.body),pt=setTimeout(()=>{te.head.removeChild(Ye)},1)}return()=>{clearTimeout(pt)}},[dt,W,te]),A.useEffect(()=>(H.current=!0,()=>{H.current=!1}),[]);const hi=A.useMemo(()=>({allColorSchemes:Ce,colorScheme:dt,darkColorScheme:Va,lightColorScheme:on,mode:nn,setColorScheme:at,setMode:Gn,systemMode:Vn}),[Ce,dt,Va,on,nn,at,Gn,Vn,Gt.colorSchemeSelector]);let mi=!0;(I||J.cssVariables===!1||ce&&(K==null?void 0:K.cssVarPrefix)===le)&&(mi=!1);const pi=E.jsxs(A.Fragment,{children:[E.jsx(og,{themeId:X?r:void 0,theme:Gt,children:V}),mi&&E.jsx(Z0,{styles:((mt=Gt.generateStyleSheets)==null?void 0:mt.call(Gt))||[]})]});return ce?pi:E.jsx(m.Provider,{value:hi,children:pi})}const O=typeof d=="string"?d:d.light,C=typeof d=="string"?d:d.dark;return{CssVarsProvider:N,useColorScheme:v,getInitColorSchemeScript:k=>Y2({colorSchemeStorageKey:s,defaultLightColorScheme:O,defaultDarkColorScheme:C,modeStorageKey:u,...k})}}function J2(a=""){function r(...u){if(!u.length)return"";const s=u[0];return typeof s=="string"&&!s.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${a?`${a}-`:""}${s}${r(...u.slice(1))})`:`, ${s}`}return(u,...s)=>`var(--${a?`${a}-`:""}${u}${r(...s)})`}const o0=(a,r,o,u=[])=>{let s=a;r.forEach((f,d)=>{d===r.length-1?Array.isArray(s)?s[Number(f)]=o:s&&typeof s=="object"&&(s[f]=o):s&&typeof s=="object"&&(s[f]||(s[f]=u.includes(f)?[]:{}),s=s[f])})},W2=(a,r,o)=>{function u(s,f=[],d=[]){Object.entries(s).forEach(([p,g])=>{(!o||o&&!o([...f,p]))&&g!=null&&(typeof g=="object"&&Object.keys(g).length>0?u(g,[...f,p],Array.isArray(g)?[...d,p]:d):r([...f,p],g,d))})}u(a)},F2=(a,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(u=>a.includes(u))||a[a.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function Ds(a,r){const{prefix:o,shouldSkipGeneratingVar:u}=r||{},s={},f={},d={};return W2(a,(p,g,m)=>{if((typeof g=="string"||typeof g=="number")&&(!u||!u(p,g))){const v=`--${o?`${o}-`:""}${p.join("-")}`,S=F2(p,g);Object.assign(s,{[v]:S}),o0(f,p,`var(${v})`,m),o0(d,p,`var(${v}, ${S})`,m)}},p=>p[0]==="vars"),{css:s,vars:f,varsWithDefaults:d}}function P2(a,r={}){const{getSelector:o=B,disableCssColorScheme:u,colorSchemeSelector:s}=r,{colorSchemes:f={},components:d,defaultColorScheme:p="light",...g}=a,{vars:m,css:v,varsWithDefaults:S}=Ds(g,r);let T=S;const N={},{[p]:O,...C}=f;if(Object.entries(C||{}).forEach(([L,q])=>{const{vars:z,css:W,varsWithDefaults:P}=Ds(q,r);T=$t(T,P),N[L]={css:W,vars:z}}),O){const{css:L,vars:q,varsWithDefaults:z}=Ds(O,r);T=$t(T,z),N[p]={css:L,vars:q}}function B(L,q){var W,P;let z=s;if(s==="class"&&(z=".%s"),s==="data"&&(z="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(z=`[${s}="%s"]`),L){if(z==="media")return a.defaultColorScheme===L?":root":{[`@media (prefers-color-scheme: ${((P=(W=f[L])==null?void 0:W.palette)==null?void 0:P.mode)||L})`]:{":root":q}};if(z)return a.defaultColorScheme===L?`:root, ${z.replace("%s",String(L))}`:z.replace("%s",String(L))}return":root"}return{vars:T,generateThemeVars:()=>{let L={...m};return Object.entries(N).forEach(([,{vars:q}])=>{L=$t(L,q)}),L},generateStyleSheets:()=>{var ee,te;const L=[],q=a.defaultColorScheme||"light";function z(b,Z){Object.keys(Z).length&&L.push(typeof b=="string"?{[b]:{...Z}}:b)}z(o(void 0,{...v}),v);const{[q]:W,...P}=N;if(W){const{css:b}=W,Z=(te=(ee=f[q])==null?void 0:ee.palette)==null?void 0:te.mode,I=!u&&Z?{colorScheme:Z,...b}:{...b};z(o(q,{...I}),I)}return Object.entries(P).forEach(([b,{css:Z}])=>{var Ee,pe;const I=(pe=(Ee=f[b])==null?void 0:Ee.palette)==null?void 0:pe.mode,se=!u&&I?{colorScheme:I,...Z}:{...Z};z(o(b,{...se}),se)}),L}}}function I2(a){return function(o){return a==="media"?`@media (prefers-color-scheme: ${o})`:a?a.startsWith("data-")&&!a.includes("%s")?`[${a}="${o}"] &`:a==="class"?`.${o} &`:a==="data"?`[data-${o}] &`:`${a.replace("%s",o)} &`:"&"}}function ao(a,r,o=void 0){const u={};for(const s in a){const f=a[s];let d="",p=!0;for(let g=0;g<f.length;g+=1){const m=f[g];m&&(d+=(p===!0?"":" ")+r(m),p=!1,o&&o[m]&&(d+=" "+o[m]))}u[s]=d}return u}function sg(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Pr.white,default:Pr.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const ex=sg();function fg(){return{text:{primary:Pr.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Pr.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const c0=fg();function s0(a,r,o,u){const s=u.light||u,f=u.dark||u*1.5;a[r]||(a.hasOwnProperty(o)?a[r]=a[o]:r==="light"?a.light=no(a.main,s):r==="dark"&&(a.dark=to(a.main,f)))}function tx(a="light"){return a==="dark"?{main:wl[200],light:wl[50],dark:wl[400]}:{main:wl[700],light:wl[400],dark:wl[800]}}function nx(a="light"){return a==="dark"?{main:Dl[200],light:Dl[50],dark:Dl[400]}:{main:Dl[500],light:Dl[300],dark:Dl[700]}}function ax(a="light"){return a==="dark"?{main:zl[500],light:zl[300],dark:zl[700]}:{main:zl[700],light:zl[400],dark:zl[800]}}function lx(a="light"){return a==="dark"?{main:Nl[400],light:Nl[300],dark:Nl[700]}:{main:Nl[700],light:Nl[500],dark:Nl[900]}}function rx(a="light"){return a==="dark"?{main:jl[400],light:jl[300],dark:jl[700]}:{main:jl[800],light:jl[500],dark:jl[900]}}function ix(a="light"){return a==="dark"?{main:Vr[400],light:Vr[300],dark:Vr[700]}:{main:"#ed6c02",light:Vr[500],dark:Vr[900]}}function Ef(a){const{mode:r="light",contrastThreshold:o=3,tonalOffset:u=.2,...s}=a,f=a.primary||tx(r),d=a.secondary||nx(r),p=a.error||ax(r),g=a.info||lx(r),m=a.success||rx(r),v=a.warning||ix(r);function S(C){return _2(C,c0.text.primary)>=o?c0.text.primary:ex.text.primary}const T=({color:C,name:B,mainShade:k=500,lightShade:V=300,darkShade:L=700})=>{if(C={...C},!C.main&&C[k]&&(C.main=C[k]),!C.hasOwnProperty("main"))throw new Error($a(11,B?` (${B})`:"",k));if(typeof C.main!="string")throw new Error($a(12,B?` (${B})`:"",JSON.stringify(C.main)));return s0(C,"light",V,u),s0(C,"dark",L,u),C.contrastText||(C.contrastText=S(C.main)),C};let N;return r==="light"?N=sg():r==="dark"&&(N=fg()),$t({common:{...Pr},mode:r,primary:T({color:f,name:"primary"}),secondary:T({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:T({color:p,name:"error"}),warning:T({color:v,name:"warning"}),info:T({color:g,name:"info"}),success:T({color:m,name:"success"}),grey:sb,contrastThreshold:o,getContrastText:S,augmentColor:T,tonalOffset:u,...N},s)}function ux(a){const r={};return Object.entries(a).forEach(u=>{const[s,f]=u;typeof f=="object"&&(r[s]=`${f.fontStyle?`${f.fontStyle} `:""}${f.fontVariant?`${f.fontVariant} `:""}${f.fontWeight?`${f.fontWeight} `:""}${f.fontStretch?`${f.fontStretch} `:""}${f.fontSize||""}${f.lineHeight?`/${f.lineHeight} `:""}${f.fontFamily||""}`)}),r}function ox(a,r){return{toolbar:{minHeight:56,[a.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[a.up("sm")]:{minHeight:64}},...r}}function cx(a){return Math.round(a*1e5)/1e5}const f0={textTransform:"uppercase"},d0='"Roboto", "Helvetica", "Arial", sans-serif';function dg(a,r){const{fontFamily:o=d0,fontSize:u=14,fontWeightLight:s=300,fontWeightRegular:f=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:g=16,allVariants:m,pxToRem:v,...S}=typeof r=="function"?r(a):r,T=u/14,N=v||(B=>`${B/g*T}rem`),O=(B,k,V,L,q)=>({fontFamily:o,fontWeight:B,fontSize:N(k),lineHeight:V,...o===d0?{letterSpacing:`${cx(L/k)}em`}:{},...q,...m}),C={h1:O(s,96,1.167,-1.5),h2:O(s,60,1.2,-.5),h3:O(f,48,1.167,0),h4:O(f,34,1.235,.25),h5:O(f,24,1.334,0),h6:O(d,20,1.6,.15),subtitle1:O(f,16,1.75,.15),subtitle2:O(d,14,1.57,.1),body1:O(f,16,1.5,.15),body2:O(f,14,1.43,.15),button:O(d,14,1.75,.4,f0),caption:O(f,12,1.66,.4),overline:O(f,12,2.66,1,f0),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return $t({htmlFontSize:g,pxToRem:N,fontFamily:o,fontSize:u,fontWeightLight:s,fontWeightRegular:f,fontWeightMedium:d,fontWeightBold:p,...C},S,{clone:!1})}const sx=.2,fx=.14,dx=.12;function $e(...a){return[`${a[0]}px ${a[1]}px ${a[2]}px ${a[3]}px rgba(0,0,0,${sx})`,`${a[4]}px ${a[5]}px ${a[6]}px ${a[7]}px rgba(0,0,0,${fx})`,`${a[8]}px ${a[9]}px ${a[10]}px ${a[11]}px rgba(0,0,0,${dx})`].join(",")}const hx=["none",$e(0,2,1,-1,0,1,1,0,0,1,3,0),$e(0,3,1,-2,0,2,2,0,0,1,5,0),$e(0,3,3,-2,0,3,4,0,0,1,8,0),$e(0,2,4,-1,0,4,5,0,0,1,10,0),$e(0,3,5,-1,0,5,8,0,0,1,14,0),$e(0,3,5,-1,0,6,10,0,0,1,18,0),$e(0,4,5,-2,0,7,10,1,0,2,16,1),$e(0,5,5,-3,0,8,10,1,0,3,14,2),$e(0,5,6,-3,0,9,12,1,0,3,16,2),$e(0,6,6,-3,0,10,14,1,0,4,18,3),$e(0,6,7,-4,0,11,15,1,0,4,20,3),$e(0,7,8,-4,0,12,17,2,0,5,22,4),$e(0,7,8,-4,0,13,19,2,0,5,24,4),$e(0,7,9,-4,0,14,21,2,0,5,26,4),$e(0,8,9,-5,0,15,22,2,0,6,28,5),$e(0,8,10,-5,0,16,24,2,0,6,30,5),$e(0,8,11,-5,0,17,26,2,0,6,32,5),$e(0,9,11,-5,0,18,28,2,0,7,34,6),$e(0,9,12,-6,0,19,29,2,0,7,36,6),$e(0,10,13,-6,0,20,31,3,0,8,38,7),$e(0,10,13,-6,0,21,33,3,0,8,40,7),$e(0,10,14,-6,0,22,35,3,0,8,42,7),$e(0,11,14,-7,0,23,36,3,0,9,44,8),$e(0,11,15,-7,0,24,38,3,0,9,46,8)],mx={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},px={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function h0(a){return`${Math.round(a)}ms`}function gx(a){if(!a)return 0;const r=a/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function yx(a){const r={...mx,...a.easing},o={...px,...a.duration};return{getAutoHeightDuration:gx,create:(s=["all"],f={})=>{const{duration:d=o.standard,easing:p=r.easeInOut,delay:g=0,...m}=f;return(Array.isArray(s)?s:[s]).map(v=>`${v} ${typeof d=="string"?d:h0(d)} ${p} ${typeof g=="string"?g:h0(g)}`).join(",")},...a,easing:r,duration:o}}const vx={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function bx(a){return vn(a)||typeof a>"u"||typeof a=="string"||typeof a=="boolean"||typeof a=="number"||Array.isArray(a)}function hg(a={}){const r={...a};function o(u){const s=Object.entries(u);for(let f=0;f<s.length;f++){const[d,p]=s[f];!bx(p)||d.startsWith("unstable_")?delete u[d]:vn(p)&&(u[d]={...p},o(u[d]))}}return o(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Xs(a={},...r){const{breakpoints:o,mixins:u={},spacing:s,palette:f={},transitions:d={},typography:p={},shape:g,...m}=a;if(a.vars&&a.generateThemeVars===void 0)throw new Error($a(20));const v=Ef(f),S=pf(a);let T=$t(S,{mixins:ox(S.breakpoints,u),palette:v,shadows:hx.slice(),typography:dg(v,p),transitions:yx(d),zIndex:{...vx}});return T=$t(T,m),T=r.reduce((N,O)=>$t(N,O),T),T.unstable_sxConfig={...ci,...m==null?void 0:m.unstable_sxConfig},T.unstable_sx=function(O){return va({sx:O,theme:this})},T.toRuntimeSource=hg,T}function Sx(a){let r;return a<1?r=5.11916*a**2:r=4.5*Math.log(a+1)+2,Math.round(r*10)/1e3}const xx=[...Array(25)].map((a,r)=>{if(r===0)return"none";const o=Sx(r);return`linear-gradient(rgba(255 255 255 / ${o}), rgba(255 255 255 / ${o}))`});function mg(a){return{inputPlaceholder:a==="dark"?.5:.42,inputUnderline:a==="dark"?.7:.42,switchTrackDisabled:a==="dark"?.2:.12,switchTrack:a==="dark"?.3:.38}}function pg(a){return a==="dark"?xx:[]}function Ex(a){const{palette:r={mode:"light"},opacity:o,overlays:u,...s}=a,f=Ef(r);return{palette:f,opacity:{...mg(f.mode),...o},overlays:u||pg(f.mode),...s}}function Tx(a){var r;return!!a[0].match(/(cssVarPrefix|colorSchemeSelector|modularCssLayers|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!a[0].match(/sxConfig$/)||a[0]==="palette"&&!!((r=a[1])!=null&&r.match(/(mode|contrastThreshold|tonalOffset)/))}const Cx=a=>[...[...Array(25)].map((r,o)=>`--${a?`${a}-`:""}overlays-${o}`),`--${a?`${a}-`:""}palette-AppBar-darkBg`,`--${a?`${a}-`:""}palette-AppBar-darkColor`],_x=a=>(r,o)=>{const u=a.rootSelector||":root",s=a.colorSchemeSelector;let f=s;if(s==="class"&&(f=".%s"),s==="data"&&(f="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(f=`[${s}="%s"]`),a.defaultColorScheme===r){if(r==="dark"){const d={};return Cx(a.cssVarPrefix).forEach(p=>{d[p]=o[p],delete o[p]}),f==="media"?{[u]:o,"@media (prefers-color-scheme: dark)":{[u]:d}}:f?{[f.replace("%s",r)]:d,[`${u}, ${f.replace("%s",r)}`]:o}:{[u]:{...o,...d}}}if(f&&f!=="media")return`${u}, ${f.replace("%s",String(r))}`}else if(r){if(f==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[u]:o}};if(f)return f.replace("%s",String(r))}return u};function Ax(a,r){r.forEach(o=>{a[o]||(a[o]={})})}function $(a,r,o){!a[r]&&o&&(a[r]=o)}function Zr(a){return typeof a!="string"||!a.startsWith("hsl")?a:rg(a)}function Ln(a,r){`${r}Channel`in a||(a[`${r}Channel`]=Qr(Zr(a[r])))}function Rx(a){return typeof a=="number"?`${a}px`:typeof a=="string"||typeof a=="function"||Array.isArray(a)?a:"8px"}const pn=a=>{try{return a()}catch{}},Ox=(a="mui")=>J2(a);function ws(a,r,o,u){if(!r)return;r=r===!0?{}:r;const s=u==="dark"?"dark":"light";if(!o){a[u]=Ex({...r,palette:{mode:s,...r==null?void 0:r.palette}});return}const{palette:f,...d}=Xs({...o,palette:{mode:s,...r==null?void 0:r.palette}});return a[u]={...r,palette:f,opacity:{...mg(s),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||pg(s)},d}function Mx(a={},...r){const{colorSchemes:o={light:!0},defaultColorScheme:u,disableCssColorScheme:s=!1,cssVarPrefix:f="mui",shouldSkipGeneratingVar:d=Tx,colorSchemeSelector:p=o.light&&o.dark?"media":void 0,rootSelector:g=":root",...m}=a,v=Object.keys(o)[0],S=u||(o.light&&v!=="light"?"light":v),T=Ox(f),{[S]:N,light:O,dark:C,...B}=o,k={...B};let V=N;if((S==="dark"&&!("dark"in o)||S==="light"&&!("light"in o))&&(V=!0),!V)throw new Error($a(21,S));const L=ws(k,V,m,S);O&&!k.light&&ws(k,O,void 0,"light"),C&&!k.dark&&ws(k,C,void 0,"dark");let q={defaultColorScheme:S,...L,cssVarPrefix:f,colorSchemeSelector:p,rootSelector:g,getCssVar:T,colorSchemes:k,font:{...ux(L.typography),...L.font},spacing:Rx(m.spacing)};Object.keys(q.colorSchemes).forEach(te=>{const b=q.colorSchemes[te].palette,Z=I=>{const se=I.split("-"),Ee=se[1],pe=se[2];return T(I,b[Ee][pe])};if(b.mode==="light"&&($(b.common,"background","#fff"),$(b.common,"onBackground","#000")),b.mode==="dark"&&($(b.common,"background","#000"),$(b.common,"onBackground","#fff")),Ax(b,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),b.mode==="light"){$(b.Alert,"errorColor",He(b.error.light,.6)),$(b.Alert,"infoColor",He(b.info.light,.6)),$(b.Alert,"successColor",He(b.success.light,.6)),$(b.Alert,"warningColor",He(b.warning.light,.6)),$(b.Alert,"errorFilledBg",Z("palette-error-main")),$(b.Alert,"infoFilledBg",Z("palette-info-main")),$(b.Alert,"successFilledBg",Z("palette-success-main")),$(b.Alert,"warningFilledBg",Z("palette-warning-main")),$(b.Alert,"errorFilledColor",pn(()=>b.getContrastText(b.error.main))),$(b.Alert,"infoFilledColor",pn(()=>b.getContrastText(b.info.main))),$(b.Alert,"successFilledColor",pn(()=>b.getContrastText(b.success.main))),$(b.Alert,"warningFilledColor",pn(()=>b.getContrastText(b.warning.main))),$(b.Alert,"errorStandardBg",Le(b.error.light,.9)),$(b.Alert,"infoStandardBg",Le(b.info.light,.9)),$(b.Alert,"successStandardBg",Le(b.success.light,.9)),$(b.Alert,"warningStandardBg",Le(b.warning.light,.9)),$(b.Alert,"errorIconColor",Z("palette-error-main")),$(b.Alert,"infoIconColor",Z("palette-info-main")),$(b.Alert,"successIconColor",Z("palette-success-main")),$(b.Alert,"warningIconColor",Z("palette-warning-main")),$(b.AppBar,"defaultBg",Z("palette-grey-100")),$(b.Avatar,"defaultBg",Z("palette-grey-400")),$(b.Button,"inheritContainedBg",Z("palette-grey-300")),$(b.Button,"inheritContainedHoverBg",Z("palette-grey-A100")),$(b.Chip,"defaultBorder",Z("palette-grey-400")),$(b.Chip,"defaultAvatarColor",Z("palette-grey-700")),$(b.Chip,"defaultIconColor",Z("palette-grey-700")),$(b.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),$(b.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),$(b.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),$(b.LinearProgress,"primaryBg",Le(b.primary.main,.62)),$(b.LinearProgress,"secondaryBg",Le(b.secondary.main,.62)),$(b.LinearProgress,"errorBg",Le(b.error.main,.62)),$(b.LinearProgress,"infoBg",Le(b.info.main,.62)),$(b.LinearProgress,"successBg",Le(b.success.main,.62)),$(b.LinearProgress,"warningBg",Le(b.warning.main,.62)),$(b.Skeleton,"bg",`rgba(${Z("palette-text-primaryChannel")} / 0.11)`),$(b.Slider,"primaryTrack",Le(b.primary.main,.62)),$(b.Slider,"secondaryTrack",Le(b.secondary.main,.62)),$(b.Slider,"errorTrack",Le(b.error.main,.62)),$(b.Slider,"infoTrack",Le(b.info.main,.62)),$(b.Slider,"successTrack",Le(b.success.main,.62)),$(b.Slider,"warningTrack",Le(b.warning.main,.62));const I=Mu(b.background.default,.8);$(b.SnackbarContent,"bg",I),$(b.SnackbarContent,"color",pn(()=>b.getContrastText(I))),$(b.SpeedDialAction,"fabHoverBg",Mu(b.background.paper,.15)),$(b.StepConnector,"border",Z("palette-grey-400")),$(b.StepContent,"border",Z("palette-grey-400")),$(b.Switch,"defaultColor",Z("palette-common-white")),$(b.Switch,"defaultDisabledColor",Z("palette-grey-100")),$(b.Switch,"primaryDisabledColor",Le(b.primary.main,.62)),$(b.Switch,"secondaryDisabledColor",Le(b.secondary.main,.62)),$(b.Switch,"errorDisabledColor",Le(b.error.main,.62)),$(b.Switch,"infoDisabledColor",Le(b.info.main,.62)),$(b.Switch,"successDisabledColor",Le(b.success.main,.62)),$(b.Switch,"warningDisabledColor",Le(b.warning.main,.62)),$(b.TableCell,"border",Le(Ou(b.divider,1),.88)),$(b.Tooltip,"bg",Ou(b.grey[700],.92))}if(b.mode==="dark"){$(b.Alert,"errorColor",Le(b.error.light,.6)),$(b.Alert,"infoColor",Le(b.info.light,.6)),$(b.Alert,"successColor",Le(b.success.light,.6)),$(b.Alert,"warningColor",Le(b.warning.light,.6)),$(b.Alert,"errorFilledBg",Z("palette-error-dark")),$(b.Alert,"infoFilledBg",Z("palette-info-dark")),$(b.Alert,"successFilledBg",Z("palette-success-dark")),$(b.Alert,"warningFilledBg",Z("palette-warning-dark")),$(b.Alert,"errorFilledColor",pn(()=>b.getContrastText(b.error.dark))),$(b.Alert,"infoFilledColor",pn(()=>b.getContrastText(b.info.dark))),$(b.Alert,"successFilledColor",pn(()=>b.getContrastText(b.success.dark))),$(b.Alert,"warningFilledColor",pn(()=>b.getContrastText(b.warning.dark))),$(b.Alert,"errorStandardBg",He(b.error.light,.9)),$(b.Alert,"infoStandardBg",He(b.info.light,.9)),$(b.Alert,"successStandardBg",He(b.success.light,.9)),$(b.Alert,"warningStandardBg",He(b.warning.light,.9)),$(b.Alert,"errorIconColor",Z("palette-error-main")),$(b.Alert,"infoIconColor",Z("palette-info-main")),$(b.Alert,"successIconColor",Z("palette-success-main")),$(b.Alert,"warningIconColor",Z("palette-warning-main")),$(b.AppBar,"defaultBg",Z("palette-grey-900")),$(b.AppBar,"darkBg",Z("palette-background-paper")),$(b.AppBar,"darkColor",Z("palette-text-primary")),$(b.Avatar,"defaultBg",Z("palette-grey-600")),$(b.Button,"inheritContainedBg",Z("palette-grey-800")),$(b.Button,"inheritContainedHoverBg",Z("palette-grey-700")),$(b.Chip,"defaultBorder",Z("palette-grey-700")),$(b.Chip,"defaultAvatarColor",Z("palette-grey-300")),$(b.Chip,"defaultIconColor",Z("palette-grey-300")),$(b.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),$(b.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),$(b.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),$(b.LinearProgress,"primaryBg",He(b.primary.main,.5)),$(b.LinearProgress,"secondaryBg",He(b.secondary.main,.5)),$(b.LinearProgress,"errorBg",He(b.error.main,.5)),$(b.LinearProgress,"infoBg",He(b.info.main,.5)),$(b.LinearProgress,"successBg",He(b.success.main,.5)),$(b.LinearProgress,"warningBg",He(b.warning.main,.5)),$(b.Skeleton,"bg",`rgba(${Z("palette-text-primaryChannel")} / 0.13)`),$(b.Slider,"primaryTrack",He(b.primary.main,.5)),$(b.Slider,"secondaryTrack",He(b.secondary.main,.5)),$(b.Slider,"errorTrack",He(b.error.main,.5)),$(b.Slider,"infoTrack",He(b.info.main,.5)),$(b.Slider,"successTrack",He(b.success.main,.5)),$(b.Slider,"warningTrack",He(b.warning.main,.5));const I=Mu(b.background.default,.98);$(b.SnackbarContent,"bg",I),$(b.SnackbarContent,"color",pn(()=>b.getContrastText(I))),$(b.SpeedDialAction,"fabHoverBg",Mu(b.background.paper,.15)),$(b.StepConnector,"border",Z("palette-grey-600")),$(b.StepContent,"border",Z("palette-grey-600")),$(b.Switch,"defaultColor",Z("palette-grey-300")),$(b.Switch,"defaultDisabledColor",Z("palette-grey-600")),$(b.Switch,"primaryDisabledColor",He(b.primary.main,.55)),$(b.Switch,"secondaryDisabledColor",He(b.secondary.main,.55)),$(b.Switch,"errorDisabledColor",He(b.error.main,.55)),$(b.Switch,"infoDisabledColor",He(b.info.main,.55)),$(b.Switch,"successDisabledColor",He(b.success.main,.55)),$(b.Switch,"warningDisabledColor",He(b.warning.main,.55)),$(b.TableCell,"border",He(Ou(b.divider,1),.68)),$(b.Tooltip,"bg",Ou(b.grey[700],.92))}Ln(b.background,"default"),Ln(b.background,"paper"),Ln(b.common,"background"),Ln(b.common,"onBackground"),Ln(b,"divider"),Object.keys(b).forEach(I=>{const se=b[I];I!=="tonalOffset"&&se&&typeof se=="object"&&(se.main&&$(b[I],"mainChannel",Qr(Zr(se.main))),se.light&&$(b[I],"lightChannel",Qr(Zr(se.light))),se.dark&&$(b[I],"darkChannel",Qr(Zr(se.dark))),se.contrastText&&$(b[I],"contrastTextChannel",Qr(Zr(se.contrastText))),I==="text"&&(Ln(b[I],"primary"),Ln(b[I],"secondary")),I==="action"&&(se.active&&Ln(b[I],"active"),se.selected&&Ln(b[I],"selected")))})}),q=r.reduce((te,b)=>$t(te,b),q);const z={prefix:f,disableCssColorScheme:s,shouldSkipGeneratingVar:d,getSelector:_x(q)},{vars:W,generateThemeVars:P,generateStyleSheets:ee}=P2(q,z);return q.vars=W,Object.entries(q.colorSchemes[q.defaultColorScheme]).forEach(([te,b])=>{q[te]=b}),q.generateThemeVars=P,q.generateStyleSheets=ee,q.generateSpacing=function(){return P0(m.spacing,hf(this))},q.getColorSchemeSelector=I2(p),q.spacing=q.generateSpacing(),q.shouldSkipGeneratingVar=d,q.unstable_sxConfig={...ci,...m==null?void 0:m.unstable_sxConfig},q.unstable_sx=function(b){return va({sx:b,theme:this})},q.toRuntimeSource=hg,q}function m0(a,r,o){a.colorSchemes&&o&&(a.colorSchemes[r]={...o!==!0&&o,palette:Ef({...o===!0?{}:o.palette,mode:r})})}function fi(a={},...r){const{palette:o,cssVariables:u=!1,colorSchemes:s=o?void 0:{light:!0},defaultColorScheme:f=o==null?void 0:o.mode,...d}=a,p=f||"light",g=s==null?void 0:s[p],m={...s,...o?{[p]:{...typeof g!="boolean"&&g,palette:o}}:void 0};if(u===!1){if(!("colorSchemes"in a))return Xs(a,...r);let v=o;"palette"in a||m[p]&&(m[p]!==!0?v=m[p].palette:p==="dark"&&(v={mode:"dark"}));const S=Xs({...a,palette:v},...r);return S.defaultColorScheme=p,S.colorSchemes=m,S.palette.mode==="light"&&(S.colorSchemes.light={...m.light!==!0&&m.light,palette:S.palette},m0(S,"dark",m.dark)),S.palette.mode==="dark"&&(S.colorSchemes.dark={...m.dark!==!0&&m.dark,palette:S.palette},m0(S,"light",m.light)),S}return!o&&!("light"in m)&&p==="light"&&(m.light=!0),Mx({...d,colorSchemes:m,defaultColorScheme:p,...typeof u!="boolean"&&u},...r)}const gg=fi();function zx(a){return a!=="ownerState"&&a!=="theme"&&a!=="sx"&&a!=="as"}const yg=a=>zx(a)&&a!=="classes",Sn=y2({themeId:qn,defaultTheme:gg,rootShouldForwardProp:yg});function Dx({theme:a,...r}){const o=qn in a?a[qn]:void 0;return E.jsx(og,{...r,themeId:o?qn:void 0,theme:o||a})}const zu={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:wx}=K2({themeId:qn,theme:()=>fi({cssVariables:!0}),colorSchemeStorageKey:zu.colorSchemeStorageKey,modeStorageKey:zu.modeStorageKey,defaultColorScheme:{light:zu.defaultLightColorScheme,dark:zu.defaultDarkColorScheme},resolveTheme:a=>{const r={...a,typography:dg(a.palette,a.typography)};return r.unstable_sx=function(u){return va({sx:u,theme:this})},r}}),Nx=wx;function jx({theme:a,...r}){const o=A.useMemo(()=>{if(typeof a=="function")return a;const u=qn in a?a[qn]:a;return"colorSchemes"in u?null:"vars"in u?a:{...a,vars:null}},[a]);return o?E.jsx(Dx,{theme:o,...r}):E.jsx(Nx,{theme:a,...r})}function Bx(a){return E.jsx(eg,{...a,defaultTheme:gg,themeId:qn})}function vg(a){return function(o){return E.jsx(Bx,{styles:typeof a=="function"?u=>a({theme:u,...o}):a})}}const ni=q2;function di(a){return B2(a)}function Ux(a){return si("MuiSvgIcon",a)}Gl("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Hx=a=>{const{color:r,fontSize:o,classes:u}=a,s={root:["root",r!=="inherit"&&`color${Yt(r)}`,`fontSize${Yt(o)}`]};return ao(s,Ux,u)},Lx=Sn("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:o}=a;return[r.root,o.color!=="inherit"&&r[`color${Yt(o.color)}`],r[`fontSize${Yt(o.fontSize)}`]]}})(ni(({theme:a})=>{var r,o,u,s,f,d,p,g,m,v,S,T,N,O;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(s=(r=a.transitions)==null?void 0:r.create)==null?void 0:s.call(r,"fill",{duration:(u=(o=(a.vars??a).transitions)==null?void 0:o.duration)==null?void 0:u.shorter}),variants:[{props:C=>!C.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((d=(f=a.typography)==null?void 0:f.pxToRem)==null?void 0:d.call(f,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((g=(p=a.typography)==null?void 0:p.pxToRem)==null?void 0:g.call(p,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((v=(m=a.typography)==null?void 0:m.pxToRem)==null?void 0:v.call(m,35))||"2.1875rem"}},...Object.entries((a.vars??a).palette).filter(([,C])=>C&&C.main).map(([C])=>{var B,k;return{props:{color:C},style:{color:(k=(B=(a.vars??a).palette)==null?void 0:B[C])==null?void 0:k.main}}}),{props:{color:"action"},style:{color:(T=(S=(a.vars??a).palette)==null?void 0:S.action)==null?void 0:T.active}},{props:{color:"disabled"},style:{color:(O=(N=(a.vars??a).palette)==null?void 0:N.action)==null?void 0:O.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Qs=A.forwardRef(function(r,o){const u=di({props:r,name:"MuiSvgIcon"}),{children:s,className:f,color:d="inherit",component:p="svg",fontSize:g="medium",htmlColor:m,inheritViewBox:v=!1,titleAccess:S,viewBox:T="0 0 24 24",...N}=u,O=A.isValidElement(s)&&s.type==="svg",C={...u,color:d,component:p,fontSize:g,instanceFontSize:r.fontSize,inheritViewBox:v,viewBox:T,hasSvgAsChild:O},B={};v||(B.viewBox=T);const k=Hx(C);return E.jsxs(Lx,{as:p,className:xt(k.root,f),focusable:"false",color:m,"aria-hidden":S?void 0:!0,role:S?"img":void 0,ref:o,...B,...N,...O&&s.props,ownerState:C,children:[O?s.props.children:s,S?E.jsx("title",{children:S}):null]})});Qs.muiName="SvgIcon";function bg(a,r){function o(u,s){return E.jsx(Qs,{"data-testid":void 0,ref:s,...u,children:a})}return o.muiName=Qs.muiName,A.memo(A.forwardRef(o))}function kx(a){const{controlled:r,default:o}=a,{current:u}=A.useRef(r!==void 0),[s,f]=A.useState(o),d=u?r:s,p=A.useCallback(g=>{u||f(g)},[]);return[d,p]}function Bu(a){const r=A.useRef(a);return yf(()=>{r.current=a}),A.useRef((...o)=>(0,r.current)(...o)).current}function Zs(...a){const r=A.useRef(void 0),o=A.useCallback(u=>{const s=a.map(f=>{if(f==null)return null;if(typeof f=="function"){const d=f,p=d(u);return typeof p=="function"?p:()=>{d(null)}}return f.current=u,()=>{f.current=null}});return()=>{s.forEach(f=>f==null?void 0:f())}},a);return A.useMemo(()=>a.every(u=>u==null)?null:u=>{r.current&&(r.current(),r.current=void 0),u!=null&&(r.current=o(u))},a)}function qx(a,r){if(a==null)return{};var o={};for(var u in a)if({}.hasOwnProperty.call(a,u)){if(r.indexOf(u)!==-1)continue;o[u]=a[u]}return o}function Ks(a,r){return Ks=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,u){return o.__proto__=u,o},Ks(a,r)}function $x(a,r){a.prototype=Object.create(r.prototype),a.prototype.constructor=a,Ks(a,r)}const p0=Bl.createContext(null);function Yx(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function Tf(a,r){var o=function(f){return r&&A.isValidElement(f)?r(f):f},u=Object.create(null);return a&&A.Children.map(a,function(s){return s}).forEach(function(s){u[s.key]=o(s)}),u}function Gx(a,r){a=a||{},r=r||{};function o(v){return v in r?r[v]:a[v]}var u=Object.create(null),s=[];for(var f in a)f in r?s.length&&(u[f]=s,s=[]):s.push(f);var d,p={};for(var g in r){if(u[g])for(d=0;d<u[g].length;d++){var m=u[g][d];p[u[g][d]]=o(m)}p[g]=o(g)}for(d=0;d<s.length;d++)p[s[d]]=o(s[d]);return p}function qa(a,r,o){return o[r]!=null?o[r]:a.props[r]}function Vx(a,r){return Tf(a.children,function(o){return A.cloneElement(o,{onExited:r.bind(null,o),in:!0,appear:qa(o,"appear",a),enter:qa(o,"enter",a),exit:qa(o,"exit",a)})})}function Xx(a,r,o){var u=Tf(a.children),s=Gx(r,u);return Object.keys(s).forEach(function(f){var d=s[f];if(A.isValidElement(d)){var p=f in r,g=f in u,m=r[f],v=A.isValidElement(m)&&!m.props.in;g&&(!p||v)?s[f]=A.cloneElement(d,{onExited:o.bind(null,d),in:!0,exit:qa(d,"exit",a),enter:qa(d,"enter",a)}):!g&&p&&!v?s[f]=A.cloneElement(d,{in:!1}):g&&p&&A.isValidElement(m)&&(s[f]=A.cloneElement(d,{onExited:o.bind(null,d),in:m.props.in,exit:qa(d,"exit",a),enter:qa(d,"enter",a)}))}}),s}var Qx=Object.values||function(a){return Object.keys(a).map(function(r){return a[r]})},Zx={component:"div",childFactory:function(r){return r}},Cf=function(a){$x(r,a);function r(u,s){var f;f=a.call(this,u,s)||this;var d=f.handleExited.bind(Yx(f));return f.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},f}var o=r.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(s,f){var d=f.children,p=f.handleExited,g=f.firstRender;return{children:g?Vx(s,p):Xx(s,d,p),firstRender:!1}},o.handleExited=function(s,f){var d=Tf(this.props.children);s.key in d||(s.props.onExited&&s.props.onExited(f),this.mounted&&this.setState(function(p){var g=ku({},p.children);return delete g[s.key],{children:g}}))},o.render=function(){var s=this.props,f=s.component,d=s.childFactory,p=qx(s,["component","childFactory"]),g=this.state.contextValue,m=Qx(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,f===null?Bl.createElement(p0.Provider,{value:g},m):Bl.createElement(p0.Provider,{value:g},Bl.createElement(f,p,m))},r}(Bl.Component);Cf.propTypes={};Cf.defaultProps=Zx;const g0={};function Sg(a,r){const o=A.useRef(g0);return o.current===g0&&(o.current=a(r)),o}const Kx=[];function Jx(a){A.useEffect(a,Kx)}class _f{constructor(){$r(this,"currentId",null);$r(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});$r(this,"disposeEffect",()=>this.clear)}static create(){return new _f}start(r,o){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,o()},r)}}function Wx(){const a=Sg(_f.create).current;return Jx(a.disposeEffect),a}function Fx(a){return typeof a=="string"}function Px(a,r,o){return a===void 0||Fx(a)?r:{...r,ownerState:{...r.ownerState,...o}}}function Ix(a,r,o){return typeof a=="function"?a(r,o):a}function eE(a,r=[]){if(a===void 0)return{};const o={};return Object.keys(a).filter(u=>u.match(/^on[A-Z]/)&&typeof a[u]=="function"&&!r.includes(u)).forEach(u=>{o[u]=a[u]}),o}function y0(a){if(a===void 0)return{};const r={};return Object.keys(a).filter(o=>!(o.match(/^on[A-Z]/)&&typeof a[o]=="function")).forEach(o=>{r[o]=a[o]}),r}function tE(a){const{getSlotProps:r,additionalProps:o,externalSlotProps:u,externalForwardedProps:s,className:f}=a;if(!r){const N=xt(o==null?void 0:o.className,f,s==null?void 0:s.className,u==null?void 0:u.className),O={...o==null?void 0:o.style,...s==null?void 0:s.style,...u==null?void 0:u.style},C={...o,...s,...u};return N.length>0&&(C.className=N),Object.keys(O).length>0&&(C.style=O),{props:C,internalRef:void 0}}const d=eE({...s,...u}),p=y0(u),g=y0(s),m=r(d),v=xt(m==null?void 0:m.className,o==null?void 0:o.className,f,s==null?void 0:s.className,u==null?void 0:u.className),S={...m==null?void 0:m.style,...o==null?void 0:o.style,...s==null?void 0:s.style,...u==null?void 0:u.style},T={...m,...o,...g,...p};return v.length>0&&(T.className=v),Object.keys(S).length>0&&(T.style=S),{props:T,internalRef:m.ref}}function Jr(a,r){const{className:o,elementType:u,ownerState:s,externalForwardedProps:f,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...g}=r,{component:m,slots:v={[a]:void 0},slotProps:S={[a]:void 0},...T}=f,N=v[a]||u,O=Ix(S[a],s),{props:{component:C,...B},internalRef:k}=tE({className:o,...g,externalForwardedProps:a==="root"?T:void 0,externalSlotProps:O}),V=Zs(k,O==null?void 0:O.ref,r.ref),L=a==="root"?C||m:C,q=Px(N,{...a==="root"&&!m&&!v[a]&&d,...a!=="root"&&!v[a]&&d,...B,...L&&!p&&{as:L},...L&&p&&{component:L},ref:V},s);return[N,q]}function v0(a){try{return a.matches(":focus-visible")}catch{}return!1}class Yu{constructor(){$r(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new Yu}static use(){const r=Sg(Yu.create).current,[o,u]=A.useState(!1);return r.shouldMount=o,r.setShouldMount=u,A.useEffect(r.mountEffect,[o]),r}mount(){return this.mounted||(this.mounted=aE(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.start(...r)})}stop(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.stop(...r)})}pulsate(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.pulsate(...r)})}}function nE(){return Yu.use()}function aE(){let a,r;const o=new Promise((u,s)=>{a=u,r=s});return o.resolve=a,o.reject=r,o}function lE(a){const{className:r,classes:o,pulsate:u=!1,rippleX:s,rippleY:f,rippleSize:d,in:p,onExited:g,timeout:m}=a,[v,S]=A.useState(!1),T=xt(r,o.ripple,o.rippleVisible,u&&o.ripplePulsate),N={width:d,height:d,top:-(d/2)+f,left:-(d/2)+s},O=xt(o.child,v&&o.childLeaving,u&&o.childPulsate);return!p&&!v&&S(!0),A.useEffect(()=>{if(!p&&g!=null){const C=setTimeout(g,m);return()=>{clearTimeout(C)}}},[g,p,m]),E.jsx("span",{className:T,style:N,children:E.jsx("span",{className:O})})}const It=Gl("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Js=550,rE=80,iE=sf`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,uE=sf`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,oE=sf`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,cE=Sn("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),sE=Sn(lE,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${It.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${iE};
    animation-duration: ${Js}ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
  }

  &.${It.ripplePulsate} {
    animation-duration: ${({theme:a})=>a.transitions.duration.shorter}ms;
  }

  & .${It.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${It.childLeaving} {
    opacity: 0;
    animation-name: ${uE};
    animation-duration: ${Js}ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
  }

  & .${It.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${oE};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,fE=A.forwardRef(function(r,o){const u=di({props:r,name:"MuiTouchRipple"}),{center:s=!1,classes:f={},className:d,...p}=u,[g,m]=A.useState([]),v=A.useRef(0),S=A.useRef(null);A.useEffect(()=>{S.current&&(S.current(),S.current=null)},[g]);const T=A.useRef(!1),N=Wx(),O=A.useRef(null),C=A.useRef(null),B=A.useCallback(q=>{const{pulsate:z,rippleX:W,rippleY:P,rippleSize:ee,cb:te}=q;m(b=>[...b,E.jsx(sE,{classes:{ripple:xt(f.ripple,It.ripple),rippleVisible:xt(f.rippleVisible,It.rippleVisible),ripplePulsate:xt(f.ripplePulsate,It.ripplePulsate),child:xt(f.child,It.child),childLeaving:xt(f.childLeaving,It.childLeaving),childPulsate:xt(f.childPulsate,It.childPulsate)},timeout:Js,pulsate:z,rippleX:W,rippleY:P,rippleSize:ee},v.current)]),v.current+=1,S.current=te},[f]),k=A.useCallback((q={},z={},W=()=>{})=>{const{pulsate:P=!1,center:ee=s||z.pulsate,fakeElement:te=!1}=z;if((q==null?void 0:q.type)==="mousedown"&&T.current){T.current=!1;return}(q==null?void 0:q.type)==="touchstart"&&(T.current=!0);const b=te?null:C.current,Z=b?b.getBoundingClientRect():{width:0,height:0,left:0,top:0};let I,se,Ee;if(ee||q===void 0||q.clientX===0&&q.clientY===0||!q.clientX&&!q.touches)I=Math.round(Z.width/2),se=Math.round(Z.height/2);else{const{clientX:pe,clientY:H}=q.touches&&q.touches.length>0?q.touches[0]:q;I=Math.round(pe-Z.left),se=Math.round(H-Z.top)}if(ee)Ee=Math.sqrt((2*Z.width**2+Z.height**2)/3),Ee%2===0&&(Ee+=1);else{const pe=Math.max(Math.abs((b?b.clientWidth:0)-I),I)*2+2,H=Math.max(Math.abs((b?b.clientHeight:0)-se),se)*2+2;Ee=Math.sqrt(pe**2+H**2)}q!=null&&q.touches?O.current===null&&(O.current=()=>{B({pulsate:P,rippleX:I,rippleY:se,rippleSize:Ee,cb:W})},N.start(rE,()=>{O.current&&(O.current(),O.current=null)})):B({pulsate:P,rippleX:I,rippleY:se,rippleSize:Ee,cb:W})},[s,B,N]),V=A.useCallback(()=>{k({},{pulsate:!0})},[k]),L=A.useCallback((q,z)=>{if(N.clear(),(q==null?void 0:q.type)==="touchend"&&O.current){O.current(),O.current=null,N.start(0,()=>{L(q,z)});return}O.current=null,m(W=>W.length>0?W.slice(1):W),S.current=z},[N]);return A.useImperativeHandle(o,()=>({pulsate:V,start:k,stop:L}),[V,k,L]),E.jsx(cE,{className:xt(It.root,f.root,d),ref:C,...p,children:E.jsx(Cf,{component:null,exit:!0,children:g})})});function dE(a){return si("MuiButtonBase",a)}const hE=Gl("MuiButtonBase",["root","disabled","focusVisible"]),mE=a=>{const{disabled:r,focusVisible:o,focusVisibleClassName:u,classes:s}=a,d=ao({root:["root",r&&"disabled",o&&"focusVisible"]},dE,s);return o&&u&&(d.root+=` ${u}`),d},pE=Sn("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${hE.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),gE=A.forwardRef(function(r,o){const u=di({props:r,name:"MuiButtonBase"}),{action:s,centerRipple:f=!1,children:d,className:p,component:g="button",disabled:m=!1,disableRipple:v=!1,disableTouchRipple:S=!1,focusRipple:T=!1,focusVisibleClassName:N,LinkComponent:O="a",onBlur:C,onClick:B,onContextMenu:k,onDragLeave:V,onFocus:L,onFocusVisible:q,onKeyDown:z,onKeyUp:W,onMouseDown:P,onMouseLeave:ee,onMouseUp:te,onTouchEnd:b,onTouchMove:Z,onTouchStart:I,tabIndex:se=0,TouchRippleProps:Ee,touchRippleRef:pe,type:H,...K}=u,ne=A.useRef(null),ce=nE(),_=Zs(ce.ref,pe),[X,J]=A.useState(!1);m&&X&&J(!1),A.useImperativeHandle(s,()=>({focusVisible:()=>{J(!0),ne.current.focus()}}),[]);const F=ce.shouldMount&&!v&&!m;A.useEffect(()=>{X&&T&&!v&&ce.pulsate()},[v,T,X,ce]);const ie=kn(ce,"start",P,S),le=kn(ce,"stop",k,S),re=kn(ce,"stop",V,S),Ce=kn(ce,"stop",te,S),Me=kn(ce,"stop",me=>{X&&me.preventDefault(),ee&&ee(me)},S),Dt=kn(ce,"start",I,S),Sa=kn(ce,"stop",b,S),Yn=kn(ce,"stop",Z,S),Gn=kn(ce,"stop",me=>{v0(me.target)||J(!1),C&&C(me)},!1),Vn=Bu(me=>{ne.current||(ne.current=me.currentTarget),v0(me.target)&&(J(!0),q&&q(me)),L&&L(me)}),on=()=>{const me=ne.current;return g&&g!=="button"&&!(me.tagName==="A"&&me.href)},Va=Bu(me=>{T&&!me.repeat&&X&&me.key===" "&&ce.stop(me,()=>{ce.start(me)}),me.target===me.currentTarget&&on()&&me.key===" "&&me.preventDefault(),z&&z(me),me.target===me.currentTarget&&on()&&me.key==="Enter"&&!m&&(me.preventDefault(),B&&B(me))}),Vl=Bu(me=>{T&&me.key===" "&&X&&!me.defaultPrevented&&ce.stop(me,()=>{ce.pulsate(me)}),W&&W(me),B&&me.target===me.currentTarget&&on()&&me.key===" "&&!me.defaultPrevented&&B(me)});let at=g;at==="button"&&(K.href||K.to)&&(at=O);const nn={};at==="button"?(nn.type=H===void 0?"button":H,nn.disabled=m):(!K.href&&!K.to&&(nn.role="button"),m&&(nn["aria-disabled"]=m));const dt=Zs(o,ne),cn={...u,centerRipple:f,component:g,disabled:m,disableRipple:v,disableTouchRipple:S,focusRipple:T,tabIndex:se,focusVisible:X},Gt=mE(cn);return E.jsxs(pE,{as:at,className:xt(Gt.root,p),ownerState:cn,onBlur:Gn,onClick:B,onContextMenu:le,onFocus:Vn,onKeyDown:Va,onKeyUp:Vl,onMouseDown:ie,onMouseLeave:Me,onMouseUp:Ce,onDragLeave:re,onTouchEnd:Sa,onTouchMove:Yn,onTouchStart:Dt,ref:dt,tabIndex:m?-1:se,type:H,...nn,...K,children:[d,F?E.jsx(fE,{ref:_,center:f,...Ee}):null]})});function kn(a,r,o,u=!1){return Bu(s=>(o&&o(s),u||a[r](s),!0))}function yE(a){return typeof a.main=="string"}function vE(a,r=[]){if(!yE(a))return!1;for(const o of r)if(!a.hasOwnProperty(o)||typeof a[o]!="string")return!1;return!0}function bE(a=[]){return([,r])=>r&&vE(r,a)}const SE=A.createContext(void 0);function xE(){return A.useContext(SE)}const EE=Gl("MuiBox",["root"]),TE=fi(),CE=d2({themeId:qn,defaultTheme:TE,defaultClassName:EE.root,generateClassName:tg.generate});function _E(a){return si("PrivateSwitchBase",a)}Gl("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const AE=a=>{const{classes:r,checked:o,disabled:u,edge:s}=a,f={root:["root",o&&"checked",u&&"disabled",s&&`edge${Yt(s)}`],input:["input"]};return ao(f,_E,r)},RE=Sn(gE,{name:"MuiSwitchBase"})({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:a,ownerState:r})=>a==="start"&&r.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:a,ownerState:r})=>a==="end"&&r.size!=="small",style:{marginRight:-12}}]}),OE=Sn("input",{name:"MuiSwitchBase",shouldForwardProp:yg})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),ME=A.forwardRef(function(r,o){const{autoFocus:u,checked:s,checkedIcon:f,defaultChecked:d,disabled:p,disableFocusRipple:g=!1,edge:m=!1,icon:v,id:S,inputProps:T,inputRef:N,name:O,onBlur:C,onChange:B,onFocus:k,readOnly:V,required:L=!1,tabIndex:q,type:z,value:W,slots:P={},slotProps:ee={},...te}=r,[b,Z]=kx({controlled:s,default:!!d}),I=xE(),se=le=>{k&&k(le),I&&I.onFocus&&I.onFocus(le)},Ee=le=>{C&&C(le),I&&I.onBlur&&I.onBlur(le)},pe=le=>{if(le.nativeEvent.defaultPrevented)return;const re=le.target.checked;Z(re),B&&B(le,re)};let H=p;I&&typeof H>"u"&&(H=I.disabled);const K=z==="checkbox"||z==="radio",ne={...r,checked:b,disabled:H,disableFocusRipple:g,edge:m},ce=AE(ne),_={slots:P,slotProps:{input:T,...ee}},[X,J]=Jr("root",{ref:o,elementType:RE,className:ce.root,shouldForwardComponentProp:!0,externalForwardedProps:{..._,component:"span",...te},getSlotProps:le=>({...le,onFocus:re=>{var Ce;(Ce=le.onFocus)==null||Ce.call(le,re),se(re)},onBlur:re=>{var Ce;(Ce=le.onBlur)==null||Ce.call(le,re),Ee(re)}}),ownerState:ne,additionalProps:{centerRipple:!0,focusRipple:!g,disabled:H,role:void 0,tabIndex:null}}),[F,ie]=Jr("input",{ref:N,elementType:OE,className:ce.input,externalForwardedProps:_,getSlotProps:le=>({...le,onChange:re=>{var Ce;(Ce=le.onChange)==null||Ce.call(le,re),pe(re)}}),ownerState:ne,additionalProps:{autoFocus:u,checked:s,defaultChecked:d,disabled:H,id:K?S:void 0,name:O,readOnly:V,required:L,tabIndex:q,type:z,...z==="checkbox"&&W===void 0?{}:{value:W}}});return E.jsxs(X,{...J,children:[E.jsx(F,{...ie}),b?f:v]})}),Ws=typeof vg({})=="function",zE=(a,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!a.vars&&{colorScheme:a.palette.mode}}),DE=a=>({color:(a.vars||a).palette.text.primary,...a.typography.body1,backgroundColor:(a.vars||a).palette.background.default,"@media print":{backgroundColor:(a.vars||a).palette.common.white}}),xg=(a,r=!1)=>{var f,d;const o={};r&&a.colorSchemes&&typeof a.getColorSchemeSelector=="function"&&Object.entries(a.colorSchemes).forEach(([p,g])=>{var v,S;const m=a.getColorSchemeSelector(p);m.startsWith("@")?o[m]={":root":{colorScheme:(v=g.palette)==null?void 0:v.mode}}:o[m.replace(/\s*&/,"")]={colorScheme:(S=g.palette)==null?void 0:S.mode}});let u={html:zE(a,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:a.typography.fontWeightBold},body:{margin:0,...DE(a),"&::backdrop":{backgroundColor:(a.vars||a).palette.background.default}},...o};const s=(d=(f=a.components)==null?void 0:f.MuiCssBaseline)==null?void 0:d.styleOverrides;return s&&(u=[u,s]),u},Uu="mui-ecs",wE=a=>{const r=xg(a,!1),o=Array.isArray(r)?r[0]:r;return!a.vars&&o&&(o.html[`:root:has(${Uu})`]={colorScheme:a.palette.mode}),a.colorSchemes&&Object.entries(a.colorSchemes).forEach(([u,s])=>{var d,p;const f=a.getColorSchemeSelector(u);f.startsWith("@")?o[f]={[`:root:not(:has(.${Uu}))`]:{colorScheme:(d=s.palette)==null?void 0:d.mode}}:o[f.replace(/\s*&/,"")]={[`&:not(:has(.${Uu}))`]:{colorScheme:(p=s.palette)==null?void 0:p.mode}}}),r},NE=vg(Ws?({theme:a,enableColorScheme:r})=>xg(a,r):({theme:a})=>wE(a));function jE(a){const r=di({props:a,name:"MuiCssBaseline"}),{children:o,enableColorScheme:u=!1}=r;return E.jsxs(A.Fragment,{children:[Ws&&E.jsx(NE,{enableColorScheme:u}),!Ws&&!u&&E.jsx("span",{className:Uu,style:{display:"none"}}),o]})}function BE(a){return si("MuiSwitch",a)}const Ct=Gl("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),UE=a=>{const{classes:r,edge:o,size:u,color:s,checked:f,disabled:d}=a,p={root:["root",o&&`edge${Yt(o)}`,`size${Yt(u)}`],switchBase:["switchBase",`color${Yt(s)}`,f&&"checked",d&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},g=ao(p,BE,r);return{...r,...g}},HE=Sn("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:o}=a;return[r.root,o.edge&&r[`edge${Yt(o.edge)}`],r[`size${Yt(o.size)}`]]}})({display:"inline-flex",width:34+12*2,height:14+12*2,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${Ct.thumb}`]:{width:16,height:16},[`& .${Ct.switchBase}`]:{padding:4,[`&.${Ct.checked}`]:{transform:"translateX(16px)"}}}}]}),LE=Sn(ME,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(a,r)=>{const{ownerState:o}=a;return[r.switchBase,{[`& .${Ct.input}`]:r.input},o.color!=="default"&&r[`color${Yt(o.color)}`]]}})(ni(({theme:a})=>({position:"absolute",top:0,left:0,zIndex:1,color:a.vars?a.vars.palette.Switch.defaultColor:`${a.palette.mode==="light"?a.palette.common.white:a.palette.grey[300]}`,transition:a.transitions.create(["left","transform"],{duration:a.transitions.duration.shortest}),[`&.${Ct.checked}`]:{transform:"translateX(20px)"},[`&.${Ct.disabled}`]:{color:a.vars?a.vars.palette.Switch.defaultDisabledColor:`${a.palette.mode==="light"?a.palette.grey[100]:a.palette.grey[600]}`},[`&.${Ct.checked} + .${Ct.track}`]:{opacity:.5},[`&.${Ct.disabled} + .${Ct.track}`]:{opacity:a.vars?a.vars.opacity.switchTrackDisabled:`${a.palette.mode==="light"?.12:.2}`},[`& .${Ct.input}`]:{left:"-100%",width:"300%"}})),ni(({theme:a})=>({"&:hover":{backgroundColor:a.vars?`rgba(${a.vars.palette.action.activeChannel} / ${a.vars.palette.action.hoverOpacity})`:Vs(a.palette.action.active,a.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(a.palette).filter(bE(["light"])).map(([r])=>({props:{color:r},style:{[`&.${Ct.checked}`]:{color:(a.vars||a).palette[r].main,"&:hover":{backgroundColor:a.vars?`rgba(${a.vars.palette[r].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:Vs(a.palette[r].main,a.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Ct.disabled}`]:{color:a.vars?a.vars.palette.Switch[`${r}DisabledColor`]:`${a.palette.mode==="light"?no(a.palette[r].main,.62):to(a.palette[r].main,.55)}`}},[`&.${Ct.checked} + .${Ct.track}`]:{backgroundColor:(a.vars||a).palette[r].main}}}))]}))),kE=Sn("span",{name:"MuiSwitch",slot:"Track"})(ni(({theme:a})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:a.transitions.create(["opacity","background-color"],{duration:a.transitions.duration.shortest}),backgroundColor:a.vars?a.vars.palette.common.onBackground:`${a.palette.mode==="light"?a.palette.common.black:a.palette.common.white}`,opacity:a.vars?a.vars.opacity.switchTrack:`${a.palette.mode==="light"?.38:.3}`}))),qE=Sn("span",{name:"MuiSwitch",slot:"Thumb"})(ni(({theme:a})=>({boxShadow:(a.vars||a).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),$E=A.forwardRef(function(r,o){const u=di({props:r,name:"MuiSwitch"}),{className:s,color:f="primary",edge:d=!1,size:p="medium",sx:g,slots:m={},slotProps:v={},...S}=u,T={...u,color:f,edge:d,size:p},N=UE(T),O={slots:m,slotProps:v},[C,B]=Jr("root",{className:xt(N.root,s),elementType:HE,externalForwardedProps:O,ownerState:T,additionalProps:{sx:g}}),[k,V]=Jr("thumb",{className:N.thumb,elementType:qE,externalForwardedProps:O,ownerState:T}),L=E.jsx(k,{...V}),[q,z]=Jr("track",{className:N.track,elementType:kE,externalForwardedProps:O,ownerState:T});return E.jsxs(C,{...B,children:[E.jsx(LE,{type:"checkbox",icon:L,checkedIcon:L,ref:o,ownerState:T,...S,classes:{...N,root:N.switchBase},slots:{...m.switchBase&&{root:m.switchBase},...m.input&&{input:m.input}},slotProps:{...v.switchBase&&{root:typeof v.switchBase=="function"?v.switchBase(T):v.switchBase},...v.input&&{input:typeof v.input=="function"?v.input(T):v.input}}}),E.jsx(q,{...z})]})}),YE=bg(E.jsx("path",{d:"M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5M2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1m18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1M11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1m0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1M5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0z"})),GE=bg(E.jsx("path",{d:"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.81.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1"})),Eg=A.createContext({mode:"light",toggleMode:()=>{}}),VE=()=>A.useContext(Eg);function XE(){const{mode:a,toggleMode:r}=VE();return E.jsxs(CE,{display:"flex",alignItems:"center",gap:1,children:[a==="light"?E.jsx(YE,{}):E.jsx(GE,{}),E.jsx($E,{checked:a==="dark",onChange:r})]})}const QE=[{to:"/",label:"Accueil"},{to:"/sessions",label:"Sessions"},{to:"/reservation",label:"Réservation"},{to:"/contact",label:"Contact"}];function ZE(){const a=$l();return E.jsxs("nav",{className:Ts.nav,children:[QE.map(({to:r,label:o})=>E.jsx(N0,{to:r,className:`${Ts.link} ${a.pathname===r?Ts.linkActive:""}`,children:o},r)),E.jsx(XE,{})]})}const KE="_footer_1fzlg_1",JE="_list_1fzlg_21",WE="_link_1fzlg_39",FE="_linkListTitle_1fzlg_57",PE="_linkSection_1fzlg_65",Ll={footer:KE,list:JE,link:WE,linkListTitle:FE,linkSection:PE};function IE({href:a,children:r,title:o,...u}){return a.startsWith("http")||a.startsWith("mailto:")||a.startsWith("tel:")?E.jsx("a",{href:a,className:Ll.link,target:"_blank",rel:"noopener noreferrer",...u,children:o||r}):E.jsx(N0,{to:a,className:Ll.link,...u,children:o||r})}function Ns({links:a,className:r,listTitle:o}){return!a||a.length==0?null:E.jsxs("section",{className:Ll.linkSection,children:[o&&E.jsx("h3",{className:Ll.linkListTitle,children:o}),E.jsx("ul",{className:Ll.list,children:a.map(u=>E.jsx("li",{children:E.jsx(IE,{href:u.href,title:u.title})},u.href))})]})}function eT(){const a=[{href:"https://facebook.com/",title:"Facebook"},{href:"https://www.instagram.com/",title:"Instagram"}],r=[{href:"/sessions",title:"Sessions"},{href:"/mentions",title:"Mention Légales"}],o=[{href:"mailto:<EMAIL>",title:"<EMAIL>"},{href:"tel:+33123456789",title:"01 23 45 67 89"},{href:"/contact",title:"Formulaire de contact"}];return E.jsxs("footer",{className:Ll.footer,children:[E.jsx(Ns,{links:a,listTitle:"Réseaux"}),E.jsx(Ns,{links:r,listTitle:"Contenu"}),E.jsx(Ns,{links:o,listTitle:"Contacts"})]})}const tT="_app_158rt_1",nT="_main_158rt_15",aT="_form_158rt_31",lT="_input_158rt_43",rT="_select_158rt_45",iT="_textarea_158rt_47",uT="_button_158rt_59",oT="_foldableList_158rt_87",cT="_foldableListItem_158rt_95",sT="_sessionCard_158rt_103",fT="_sessionHeader_158rt_131",dT="_sessionMeta_158rt_151",hT="_theme_158rt_167",mT="_duration_158rt_169",pT="_price_158rt_171",gT="_description_158rt_183",yT="_sessionDetails_158rt_195",vT="_participants_158rt_209",bT="_availability_158rt_219",ST="_availableSpots_158rt_233",xT="_noSpots_158rt_243",ET="_nextDate_158rt_253",TT="_reserveButton_158rt_263",CT="_slotSelector_158rt_295",_T="_formGroup_158rt_303",AT="_noSlots_158rt_325",RT="_sessionInfo_158rt_343",OT="_sessionDescription_158rt_369",MT="_bookingForm_158rt_383",zT="_participantHint_158rt_393",DT="_errorList_158rt_409",wT="_error_158rt_409",NT="_successMessage_158rt_437",jT="_bookingDetails_158rt_455",BT="_errorBoundary_158rt_489",UT="_errorDetails_158rt_521",HT="_loadingContainer_158rt_561",LT="_spinner_158rt_601",kT="_spinnerInner_158rt_613",qT="_spin_158rt_601",$T="_loadingMessage_158rt_651",YT="_errorAlertContainer_158rt_663",GT="_errorAlert_158rt_663",VT="_errorValidation_158rt_691",XT="_errorNetwork_158rt_703",QT="_errorServer_158rt_715",ZT="_errorUnknown_158rt_727",KT="_errorContent_158rt_739",JT="_errorIcon_158rt_751",WT="_errorText_158rt_761",FT="_errorDismiss_158rt_771",oe={app:tT,main:nT,form:aT,input:lT,select:rT,textarea:iT,button:uT,foldableList:oT,foldableListItem:cT,sessionCard:sT,sessionHeader:fT,sessionMeta:dT,theme:hT,duration:mT,price:pT,description:gT,sessionDetails:yT,participants:vT,availability:bT,availableSpots:ST,noSpots:xT,nextDate:ET,reserveButton:TT,slotSelector:CT,formGroup:_T,noSlots:AT,sessionInfo:RT,sessionDescription:OT,bookingForm:MT,participantHint:zT,errorList:DT,error:wT,successMessage:NT,bookingDetails:jT,errorBoundary:BT,errorDetails:UT,loadingContainer:HT,"loading-small":"_loading-small_158rt_577","loading-medium":"_loading-medium_158rt_585","loading-large":"_loading-large_158rt_593",spinner:LT,spinnerInner:kT,spin:qT,loadingMessage:$T,errorAlertContainer:YT,errorAlert:GT,errorValidation:VT,errorNetwork:XT,errorServer:QT,errorUnknown:ZT,errorContent:KT,errorIcon:JT,errorText:WT,errorDismiss:FT};class b0 extends A.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,o){console.error("ErrorBoundary caught an error:",r,o)}render(){var r;return this.state.hasError?this.props.fallback?this.props.fallback:E.jsxs("div",{className:oe.errorBoundary,children:[E.jsx("h2",{children:"🎭 Quelque chose d'effrayant s'est produit..."}),E.jsx("p",{children:"Une erreur inattendue est survenue dans l'application. Nos fantômes techniques travaillent à la résoudre !"}),E.jsxs("details",{className:oe.errorDetails,children:[E.jsx("summary",{children:"Détails techniques"}),E.jsx("pre",{children:(r=this.state.error)==null?void 0:r.stack})]}),E.jsx("button",{className:oe.button,onClick:()=>window.location.reload(),children:"🔄 Recharger la page"})]}):this.props.children}}function PT({elements:a}){return E.jsx("ul",{children:a.map((r,o)=>E.jsxs("li",{children:[E.jsx("strong",{children:r.header})," — ",r.text]},r.header+o))})}function IT(){const a=[{header:"Le Manoir Hanté",text:"Explorez un manoir ancien où hantent des esprits et secrets terrifiants. Préparez-vous à résoudre des énigmes dans un décor gothique."},{header:"L’Asile Abandonné",text:"Echappez à un asile psychiatrique désaffecté, entre mystères sombres et présences inquiétantes. Sensations fortes garanties."},{header:"La Crypte Maudite",text:"Descendez dans une crypte ancestrale remplie de pièges et de malédictions. Chaque pas peut être le dernier, saurez-vous sortir vivant ?"}];return E.jsxs(E.Fragment,{children:[E.jsx("h1",{children:"La Maison Horrifique"}),E.jsxs("section",{children:[E.jsx("h2",{children:"Nos sessions d’escape game"}),E.jsx(PT,{elements:a})]}),E.jsxs("section",{children:[E.jsx("h2",{children:"À propos de l’entreprise"}),E.jsx("p",{children:"La Maison Horrifique propose des expériences immersives et terrifiantes, parfaites pour les amateurs de sensations fortes. Notre équipe dévouée veille à créer des univers riches et effrayants pour tous les aventuriers."})]})]})}function e3({room:a,onReserve:r}){const o=m=>m?`${m}€`:"Prix non défini",u=m=>m?`${m} min`:"Durée non définie",s=()=>a.availableSlots?a.availableSlots.reduce((m,v)=>m+v.availableSpots,0):0,f=()=>{if(!a.availableSlots||a.availableSlots.length===0)return null;const m=[...a.availableSlots].filter(v=>v.availableSpots>0).sort((v,S)=>new Date(v.date).getTime()-new Date(S.date).getTime());return m.length>0?m[0].date:null},d=m=>new Date(m).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),p=f(),g=s();return E.jsxs("div",{className:oe.sessionCard,children:[E.jsxs("div",{className:oe.sessionHeader,children:[E.jsx("h3",{children:a.name}),E.jsxs("div",{className:oe.sessionMeta,children:[a.theme&&E.jsxs("span",{className:oe.theme,children:["🎭 ",a.theme]}),E.jsxs("span",{className:oe.duration,children:["⏱️ ",u(a.duration)]}),E.jsxs("span",{className:oe.price,children:["💰 ",o(a.price)]})]})]}),E.jsx("p",{className:oe.description,children:a.description}),E.jsxs("div",{className:oe.sessionDetails,children:[E.jsxs("div",{className:oe.participants,children:[E.jsx("strong",{children:"Participants:"})," ",a.minParticipants||1," - ",a.maxParticipants||"illimité"]}),E.jsx("div",{className:oe.availability,children:g>0?E.jsxs(E.Fragment,{children:[E.jsxs("span",{className:oe.availableSpots,children:["✅ ",g," places disponibles"]}),p&&E.jsxs("span",{className:oe.nextDate,children:["📅 Prochaine date: ",d(p)]})]}):E.jsx("span",{className:oe.noSpots,children:"❌ Aucune place disponible"})})]}),r&&g>0&&E.jsx("button",{className:oe.reserveButton,onClick:()=>r(a.id),children:"Réserver cette session"})]})}function Tg({message:a="Chargement...",size:r="medium"}){return E.jsxs("div",{className:`${oe.loadingContainer} ${oe[`loading-${r}`]}`,children:[E.jsx("div",{className:oe.spinner,children:E.jsx("div",{className:oe.spinnerInner})}),E.jsx("p",{className:oe.loadingMessage,children:a})]})}const t3=a=>{switch(a){case"validation":return"⚠️";case"network":return"🌐";case"server":return"🔧";default:return"❌"}},n3=a=>{switch(a){case"validation":return oe.errorValidation;case"network":return oe.errorNetwork;case"server":return oe.errorServer;default:return oe.errorUnknown}};function Cg({errors:a,onDismiss:r,className:o}){return a.length===0?null:E.jsx("div",{className:`${oe.errorAlertContainer} ${o||""}`,children:a.map((u,s)=>E.jsxs("div",{className:`${oe.errorAlert} ${n3(u.type)}`,children:[E.jsxs("div",{className:oe.errorContent,children:[E.jsx("span",{className:oe.errorIcon,children:t3(u.type)}),E.jsxs("div",{className:oe.errorText,children:[E.jsx("strong",{children:u.field?`${u.field}: `:""}),u.message]})]}),r&&E.jsx("button",{className:oe.errorDismiss,onClick:()=>r(s),"aria-label":"Fermer l'erreur",children:"✕"})]},s))})}const _g=()=>{const[a,r]=A.useState([]),[o,u]=A.useState(!1),s=A.useCallback(m=>{r(v=>[...v,m])},[]),f=A.useCallback(m=>{r(v=>v.filter((S,T)=>T!==m))},[]),d=A.useCallback(()=>{r([])},[]),p=A.useCallback(m=>{if(m.name==="TypeError"&&m.message.includes("fetch"))return{message:"Impossible de se connecter au serveur. Vérifiez votre connexion internet.",type:"network"};if(m.status)switch(m.status){case 400:return{message:m.message||"Données invalides",type:"validation"};case 404:return{message:"Ressource non trouvée",type:"server"};case 500:return{message:"Erreur interne du serveur",type:"server"};default:return{message:m.message||"Une erreur est survenue",type:"server"}}return{message:m.message||"Une erreur inattendue est survenue",type:"unknown"}},[]),g=A.useCallback(async(m,v,S)=>{u(!0),d();try{const T=await m();return v==null||v(T),T}catch(T){const N=p(T);return s(N),S==null||S(N),null}finally{u(!1)}},[p,s,d]);return{errors:a,isLoading:o,addError:s,removeError:f,clearErrors:d,handleApiError:p,executeWithErrorHandling:g}},Af="https://maison.hor";function a3(){const[a,r]=A.useState([]),o=tf(),{errors:u,isLoading:s,executeWithErrorHandling:f,removeError:d}=_g();A.useEffect(()=>{p()},[]);const p=async()=>{await f(async()=>{const v=await fetch(Af+"/client/rooms");if(!v.ok)throw new Error(`Erreur ${v.status}: ${v.statusText}`);return await v.json()},v=>{r(v)})},g=v=>{o(`/reservation?roomId=${v}`)},m=()=>{p()};return E.jsxs(E.Fragment,{children:[E.jsx("h1",{children:"Nos Sessions"}),E.jsx(Cg,{errors:u,onDismiss:d}),s?E.jsx(Tg,{message:"Chargement des sessions d'escape game...",size:"large"}):u.length>0?E.jsxs("div",{style:{textAlign:"center",padding:"2rem"},children:[E.jsx("p",{children:"Impossible de charger les sessions."}),E.jsx("button",{onClick:m,style:{marginTop:"1rem"},children:"🔄 Réessayer"})]}):a.length===0?E.jsxs("div",{style:{textAlign:"center",padding:"2rem"},children:[E.jsx("p",{children:"Aucune session disponible pour le moment."}),E.jsx("button",{onClick:m,style:{marginTop:"1rem"},children:"🔄 Actualiser"})]}):E.jsx("div",{children:a.map(v=>E.jsx(e3,{room:v,onReserve:g},v.id))})]})}function l3({selectedRoomId:a,selectedSlotId:r,onSlotChange:o,onRoomChange:u}){const[s,f]=A.useState([]),[d,p]=A.useState(null),{errors:g,isLoading:m,executeWithErrorHandling:v,removeError:S}=_g();A.useEffect(()=>{T()},[]),A.useEffect(()=>{if(a&&s.length>0){const k=s.find(V=>V.id===a);k&&(!d||d.id!==k.id)&&(p(k),u==null||u(k.id,k))}},[a,s]);const T=async()=>{await v(async()=>{const k=await fetch(Af+"/client/rooms");if(!k.ok)throw new Error(`Erreur ${k.status}: ${k.statusText}`);return await k.json()},k=>{f(k)})},N=k=>{const V=parseInt(k.target.value);if(isNaN(V)){p(null);return}const L=s.find(q=>q.id===V);L?(p(L),u==null||u(L.id,L)):p(null)},O=k=>{var q;const V=parseInt(k.target.value);if(isNaN(V)||!d)return;const L=(q=d.availableSlots)==null?void 0:q.find(z=>z.id===V);L&&o(d.id,V,L)},C=k=>`${new Date(k.date).toLocaleDateString("fr-FR",{weekday:"short",month:"short",day:"numeric"})} - ${k.startTime} à ${k.endTime} (${k.availableSpots} places)`,B=()=>d!=null&&d.availableSlots?d.availableSlots.filter(k=>k.availableSpots>0):[];return m?E.jsx(Tg,{message:"Chargement des sessions...",size:"small"}):E.jsxs("div",{className:oe.slotSelector,children:[E.jsx(Cg,{errors:g,onDismiss:S}),E.jsxs("div",{className:oe.formGroup,children:[E.jsx("label",{htmlFor:"room-select",children:"Session d'escape game *"}),E.jsxs("select",{id:"room-select",value:(d==null?void 0:d.id)||"",onChange:N,className:oe.input,required:!0,children:[E.jsx("option",{value:"",children:"-- Choisissez une session --"}),s.map(k=>E.jsxs("option",{value:k.id,children:[k.name," - ",k.price,"€ (",k.duration," min)"]},k.id))]})]}),d&&E.jsxs("div",{className:oe.formGroup,children:[E.jsx("label",{htmlFor:"slot-select",children:"Créneau disponible *"}),B().length>0?E.jsxs("select",{id:"slot-select",value:r||"",onChange:O,className:oe.input,required:!0,children:[E.jsx("option",{value:"",children:"-- Choisissez un créneau --"}),B().map(k=>E.jsx("option",{value:k.id,children:C(k)},k.id))]}):E.jsx("p",{className:oe.noSlots,children:"❌ Aucun créneau disponible pour cette session"})]}),d&&E.jsxs("div",{className:oe.sessionInfo,children:[E.jsx("h4",{children:"Informations de la session"}),E.jsxs("div",{className:oe.sessionMeta,children:[E.jsxs("span",{children:["🎭 ",d.theme]}),E.jsxs("span",{children:["⏱️ ",d.duration," minutes"]}),E.jsxs("span",{children:["💰 ",d.price,"€"]}),E.jsxs("span",{children:["👥 ",d.minParticipants," - ",d.maxParticipants," participants"]})]}),E.jsx("p",{className:oe.sessionDescription,children:d.description})]})]})}function Fs(a){const{label:r,name:o,value:u,required:s}=a;let f;switch(a.type){case"textarea":f=E.jsx("textarea",{name:o,value:u,onChange:a.onChange,rows:a.rows,required:s===void 0?!0:s,className:oe.textarea});break;case"select":f=E.jsx("select",{name:o,value:u,onChange:a.onChange,required:s===void 0?!0:s,className:oe.input,children:a.options.map((d,p)=>E.jsx("option",{value:d.value,children:d.text},d.value+p))});break;default:f=E.jsx("input",{type:a.type,name:o,value:u,onChange:a.onChange,required:s===void 0?!0:s,className:oe.input})}return E.jsxs("div",{children:[E.jsx("label",{htmlFor:o,children:r}),f]})}function Ag({type:a,children:r,onClick:o,disabled:u}){return E.jsx("button",{type:a,className:oe.button,onClick:o,disabled:u,children:r})}function r3({initialRoomId:a,onSubmitted:r}){const[o,u]=A.useState({customerEmail:"",roomId:a||0,slotId:0,participantCount:1}),[s,f]=A.useState(null),[d,p]=A.useState(null),[g,m]=A.useState([]),[v,S]=A.useState(!1),T=(V,L,q)=>{u(z=>({...z,roomId:V,slotId:L})),p(q),m([])},N=(V,L)=>{u(q=>({...q,roomId:V,slotId:0})),f(L),p(null),m([])},O=V=>{u(L=>({...L,customerEmail:V.target.value})),m([])},C=V=>{const L=parseInt(V.target.value)||1;u(q=>({...q,participantCount:L})),m([])},B=()=>{const V=[];return o.customerEmail?/\S+@\S+\.\S+/.test(o.customerEmail)||V.push("L'adresse email n'est pas valide"):V.push("L'adresse email est requise"),o.roomId||V.push("Veuillez sélectionner une session"),o.slotId||V.push("Veuillez sélectionner un créneau"),s&&o.participantCount<(s.minParticipants||1)&&V.push(`Minimum ${s.minParticipants} participants requis pour cette session`),s&&s.maxParticipants&&o.participantCount>s.maxParticipants&&V.push(`Maximum ${s.maxParticipants} participants autorisés pour cette session`),d&&o.participantCount>d.availableSpots&&V.push(`Seulement ${d.availableSpots} places disponibles pour ce créneau`),V},k=async V=>{V.preventDefault();const L=B();if(L.length>0){m(L);return}S(!0),m([]);try{const q={roomId:o.roomId,slotId:o.slotId,customerEmail:o.customerEmail,participantCount:o.participantCount},z=await fetch(Af+"/client/bookings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(q)});if(!z.ok){const P=await z.json();throw new Error(P.error||"Erreur lors de la réservation")}const W=await z.json();r(W)}catch(q){m([q instanceof Error?q.message:"Erreur lors de la réservation"])}finally{S(!1)}};return E.jsxs("form",{onSubmit:k,className:oe.bookingForm,children:[E.jsx(l3,{selectedRoomId:o.roomId||void 0,selectedSlotId:o.slotId||void 0,onSlotChange:T,onRoomChange:N}),E.jsx("div",{className:oe.formGroup,children:E.jsx(Fs,{label:"Adresse email",type:"email",name:"customerEmail",value:o.customerEmail,onChange:O,required:!0})}),E.jsxs("div",{className:oe.formGroup,children:[E.jsx(Fs,{label:"Nombre de participants",type:"number",name:"participantCount",value:o.participantCount.toString(),onChange:C,required:!0}),s&&E.jsxs("small",{className:oe.participantHint,children:["Cette session accepte de ",s.minParticipants," à ",s.maxParticipants," participants"]})]}),g.length>0&&E.jsx("div",{className:oe.errorList,children:g.map((V,L)=>E.jsxs("p",{className:oe.error,children:["❌ ",V]},L))}),E.jsx(Ag,{type:"submit",disabled:v,children:v?"Réservation en cours...":"Confirmer la réservation"})]})}function i3(){const[a]=ib(),[r,o]=A.useState(null),[u,s]=A.useState();A.useEffect(()=>{const p=a.get("roomId");if(p){const g=parseInt(p);isNaN(g)||s(g)}},[a]);const f=p=>{o(p)},d=p=>new Date(p).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"});return E.jsxs("div",{className:oe.form,children:[E.jsx("h1",{children:"Réservation"}),r?E.jsxs("div",{className:oe.successMessage,children:[E.jsx("h2",{children:"✅ Réservation confirmée !"}),E.jsx("p",{children:"Merci pour votre réservation ! Voici les détails :"}),E.jsxs("div",{className:oe.bookingDetails,children:[E.jsx("h4",{children:"Détails de votre réservation"}),E.jsxs("p",{children:[E.jsx("strong",{children:"Numéro de réservation :"})," #",r.id]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Email :"})," ",r.customerEmail]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Nombre de participants :"})," ",r.participantCount]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Statut :"})," ",r.status==="confirmed"?"Confirmée":r.status]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Date de réservation :"})," ",d(r.createdAt)]})]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Important :"})," Vous recevrez un email de confirmation avec tous les détails de votre session d'escape game. Pensez à arriver 15 minutes avant l'heure prévue !"]})]}):E.jsx(r3,{initialRoomId:u,onSubmitted:f})]})}function u3({formdata:a,setFormdata:r,setSubmitted:o,fields:u,submitText:s}){const f=p=>{r({...a,[p.target.name]:p.target.value})},d=p=>{p.preventDefault(),o(!0)};return E.jsxs("form",{onSubmit:d,children:[u.map((p,g)=>E.jsx(Fs,{...p,onChange:f},g)),E.jsx(Ag,{type:"submit",children:s})]})}function o3({formdata:a,setFormdata:r,setSubmitted:o}){const u=[{label:"Nom",type:"text",name:"name",value:a.name},{label:"Email",type:"email",name:"email",value:a.email},{label:"Message",type:"textarea",name:"message",value:a.message,rows:5}];return E.jsx(u3,{formdata:a,setFormdata:r,setSubmitted:o,fields:u,submitText:"Envoyer"})}function c3(){const[a,r]=A.useState({name:"",email:"",message:""}),[o,u]=A.useState(!1);return E.jsxs("div",{className:oe.form,children:[E.jsx("h1",{children:"Contact"}),o?E.jsxs("p",{children:["Merci pour votre message, ",a.name," ! Nous vous répondrons rapidement."]}):E.jsx(o3,{formdata:a,setFormdata:r,setSubmitted:u})]})}function s3(){return E.jsxs(E.Fragment,{children:[E.jsx("h1",{children:"Mentions légales"}),E.jsx("p",{children:"La Maison Horrifique - SARL au capital de 10 000€"}),E.jsx("p",{children:"Siège social : 12 rue de l'horreur, 75000 Paris"}),E.jsx("p",{children:"Directeur de publication : Alain Térieur"}),E.jsx("p",{children:"Contact : <EMAIL>"}),E.jsx("p",{children:"Ce site est une réalisation pédagogique."})]})}function f3(){return E.jsx("h1",{children:"Admin"})}function d3(){return E.jsx(b0,{children:E.jsx("div",{className:oe.app,children:E.jsxs(nb,{children:[E.jsx(ZE,{}),E.jsx("main",{className:oe.main,children:E.jsx(b0,{children:E.jsxs(K1,{children:[E.jsx(La,{path:"/",element:E.jsx(IT,{})}),E.jsx(La,{path:"/sessions",element:E.jsx(a3,{})}),E.jsx(La,{path:"/reservation",element:E.jsx(i3,{})}),E.jsx(La,{path:"/contact",element:E.jsx(c3,{})}),E.jsx(La,{path:"/mentions",element:E.jsx(s3,{})}),E.jsx(La,{path:"/admin",element:E.jsx(f3,{})})]})})}),E.jsx(eT,{})]})})})}const h3=fi({palette:{mode:"light",primary:{main:"#1976d2"},secondary:{main:"#9c27b0"},background:{default:"#f5f5f5",paper:"#fff"}},typography:{fontFamily:"Roboto, sans-serif"},shape:{borderRadius:8}}),m3=fi({palette:{mode:"dark",primary:{main:"#dc2626"},secondary:{main:"#7c2d12"},background:{default:"#0f0f0f",paper:"#1a1a1a"},text:{primary:"#f5f5f5",secondary:"#b0b0b0"},error:{main:"#ef4444"},warning:{main:"#f59e0b"},success:{main:"#10b981"}},typography:{fontFamily:'"Creepster", "Roboto", sans-serif'},shape:{borderRadius:8}}),Rg="horror-house-theme",p3=()=>{try{const a=localStorage.getItem(Rg);if(a==="light"||a==="dark")return a}catch(a){console.warn("Erreur lors de la lecture du thème depuis localStorage:",a)}return"dark"},g3=a=>{try{localStorage.setItem(Rg,a)}catch(r){console.warn("Erreur lors de la sauvegarde du thème dans localStorage:",r)}};function y3({children:a}){const[r,o]=A.useState(()=>p3());A.useEffect(()=>{g3(r)},[r]);const u=()=>{o(f=>f==="light"?"dark":"light")},s=A.useMemo(()=>r==="dark"?m3:h3,[r]);return E.jsx(Eg.Provider,{value:{mode:r,toggleMode:u},children:E.jsxs(jx,{theme:s,children:[E.jsx(jE,{}),a]})})}async function v3(){const{worker:a}=await Zv(async()=>{const{worker:r}=await import("./browser-BhrhA5Rd.js");return{worker:r}},[]);return a.start()}const Og=document.getElementById("root");if(!Og)throw new Error("Failed to find root element.");v3().then(()=>{a1.createRoot(Og).render(E.jsx(A.StrictMode,{children:E.jsx(y3,{children:E.jsx(d3,{})})}))});
