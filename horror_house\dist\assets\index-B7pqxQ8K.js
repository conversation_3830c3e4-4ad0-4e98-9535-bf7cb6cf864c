var AS=Object.defineProperty;var OS=(n,r,o)=>r in n?AS(n,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[r]=o;var Dl=(n,r,o)=>OS(n,typeof r!="symbol"?r+"":r,o);function _S(n,r){for(var o=0;o<r.length;o++){const i=r[o];if(typeof i!="string"&&!Array.isArray(i)){for(const s in i)if(s!=="default"&&!(s in n)){const c=Object.getOwnPropertyDescriptor(i,s);c&&Object.defineProperty(n,s,c.get?c:{enumerable:!0,get:()=>i[s]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const c of s)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&i(d)}).observe(document,{childList:!0,subtree:!0});function o(s){const c={};return s.integrity&&(c.integrity=s.integrity),s.referrerPolicy&&(c.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?c.credentials="include":s.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function i(s){if(s.ep)return;s.ep=!0;const c=o(s);fetch(s.href,c)}})();const jS="modulepreload",NS=function(n){return"/"+n},Qg={},zS=function(r,o,i){let s=Promise.resolve();if(o&&o.length>0){let d=function(h){return Promise.all(h.map(y=>Promise.resolve(y).then(b=>({status:"fulfilled",value:b}),b=>({status:"rejected",reason:b}))))};document.getElementsByTagName("link");const p=document.querySelector("meta[property=csp-nonce]"),m=(p==null?void 0:p.nonce)||(p==null?void 0:p.getAttribute("nonce"));s=d(o.map(h=>{if(h=NS(h),h in Qg)return;Qg[h]=!0;const y=h.endsWith(".css"),b=y?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${h}"]${b}`))return;const x=document.createElement("link");if(x.rel=y?"stylesheet":jS,y||(x.as="script"),x.crossOrigin="",x.href=h,m&&x.setAttribute("nonce",m),document.head.appendChild(x),y)return new Promise((R,C)=>{x.addEventListener("load",R),x.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${h}`)))})}))}function c(d){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=d,window.dispatchEvent(p),!p.defaultPrevented)throw d}return s.then(d=>{for(const p of d||[])p.status==="rejected"&&c(p.reason);return r().catch(c)})};function B0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var zf={exports:{}},kl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wg;function DS(){if(Wg)return kl;Wg=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(i,s,c){var d=null;if(c!==void 0&&(d=""+c),s.key!==void 0&&(d=""+s.key),"key"in s){c={};for(var p in s)p!=="key"&&(c[p]=s[p])}else c=s;return s=c.ref,{$$typeof:n,type:i,key:d,ref:s!==void 0?s:null,props:c}}return kl.Fragment=r,kl.jsx=o,kl.jsxs=o,kl}var Zg;function kS(){return Zg||(Zg=1,zf.exports=DS()),zf.exports}var g=kS(),Df={exports:{}},Nt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jg;function BS(){if(Jg)return Nt;Jg=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),b=Symbol.iterator;function x(N){return N===null||typeof N!="object"?null:(N=b&&N[b]||N["@@iterator"],typeof N=="function"?N:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,w={};function M(N,G,et){this.props=N,this.context=G,this.refs=w,this.updater=et||R}M.prototype.isReactComponent={},M.prototype.setState=function(N,G){if(typeof N!="object"&&typeof N!="function"&&N!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,N,G,"setState")},M.prototype.forceUpdate=function(N){this.updater.enqueueForceUpdate(this,N,"forceUpdate")};function j(){}j.prototype=M.prototype;function D(N,G,et){this.props=N,this.context=G,this.refs=w,this.updater=et||R}var A=D.prototype=new j;A.constructor=D,C(A,M.prototype),A.isPureReactComponent=!0;var O=Array.isArray,_={H:null,A:null,T:null,S:null,V:null},L=Object.prototype.hasOwnProperty;function Y(N,G,et,tt,ut,lt){return et=lt.ref,{$$typeof:n,type:N,key:G,ref:et!==void 0?et:null,props:lt}}function F(N,G){return Y(N.type,G,void 0,void 0,void 0,N.props)}function K(N){return typeof N=="object"&&N!==null&&N.$$typeof===n}function E(N){var G={"=":"=0",":":"=2"};return"$"+N.replace(/[=:]/g,function(et){return G[et]})}var U=/\/+/g;function X(N,G){return typeof N=="object"&&N!==null&&N.key!=null?E(""+N.key):G.toString(36)}function at(){}function it(N){switch(N.status){case"fulfilled":return N.value;case"rejected":throw N.reason;default:switch(typeof N.status=="string"?N.then(at,at):(N.status="pending",N.then(function(G){N.status==="pending"&&(N.status="fulfilled",N.value=G)},function(G){N.status==="pending"&&(N.status="rejected",N.reason=G)})),N.status){case"fulfilled":return N.value;case"rejected":throw N.reason}}throw N}function Q(N,G,et,tt,ut){var lt=typeof N;(lt==="undefined"||lt==="boolean")&&(N=null);var ct=!1;if(N===null)ct=!0;else switch(lt){case"bigint":case"string":case"number":ct=!0;break;case"object":switch(N.$$typeof){case n:case r:ct=!0;break;case y:return ct=N._init,Q(ct(N._payload),G,et,tt,ut)}}if(ct)return ut=ut(N),ct=tt===""?"."+X(N,0):tt,O(ut)?(et="",ct!=null&&(et=ct.replace(U,"$&/")+"/"),Q(ut,G,et,"",function(zt){return zt})):ut!=null&&(K(ut)&&(ut=F(ut,et+(ut.key==null||N&&N.key===ut.key?"":(""+ut.key).replace(U,"$&/")+"/")+ct)),G.push(ut)),1;ct=0;var bt=tt===""?".":tt+":";if(O(N))for(var wt=0;wt<N.length;wt++)tt=N[wt],lt=bt+X(tt,wt),ct+=Q(tt,G,et,lt,ut);else if(wt=x(N),typeof wt=="function")for(N=wt.call(N),wt=0;!(tt=N.next()).done;)tt=tt.value,lt=bt+X(tt,wt++),ct+=Q(tt,G,et,lt,ut);else if(lt==="object"){if(typeof N.then=="function")return Q(it(N),G,et,tt,ut);throw G=String(N),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(N).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return ct}function k(N,G,et){if(N==null)return N;var tt=[],ut=0;return Q(N,tt,"","",function(lt){return G.call(et,lt,ut++)}),tt}function I(N){if(N._status===-1){var G=N._result;G=G(),G.then(function(et){(N._status===0||N._status===-1)&&(N._status=1,N._result=et)},function(et){(N._status===0||N._status===-1)&&(N._status=2,N._result=et)}),N._status===-1&&(N._status=0,N._result=G)}if(N._status===1)return N._result.default;throw N._result}var ot=typeof reportError=="function"?reportError:function(N){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof N=="object"&&N!==null&&typeof N.message=="string"?String(N.message):String(N),error:N});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",N);return}console.error(N)};function Z(){}return Nt.Children={map:k,forEach:function(N,G,et){k(N,function(){G.apply(this,arguments)},et)},count:function(N){var G=0;return k(N,function(){G++}),G},toArray:function(N){return k(N,function(G){return G})||[]},only:function(N){if(!K(N))throw Error("React.Children.only expected to receive a single React element child.");return N}},Nt.Component=M,Nt.Fragment=o,Nt.Profiler=s,Nt.PureComponent=D,Nt.StrictMode=i,Nt.Suspense=m,Nt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_,Nt.__COMPILER_RUNTIME={__proto__:null,c:function(N){return _.H.useMemoCache(N)}},Nt.cache=function(N){return function(){return N.apply(null,arguments)}},Nt.cloneElement=function(N,G,et){if(N==null)throw Error("The argument must be a React element, but you passed "+N+".");var tt=C({},N.props),ut=N.key,lt=void 0;if(G!=null)for(ct in G.ref!==void 0&&(lt=void 0),G.key!==void 0&&(ut=""+G.key),G)!L.call(G,ct)||ct==="key"||ct==="__self"||ct==="__source"||ct==="ref"&&G.ref===void 0||(tt[ct]=G[ct]);var ct=arguments.length-2;if(ct===1)tt.children=et;else if(1<ct){for(var bt=Array(ct),wt=0;wt<ct;wt++)bt[wt]=arguments[wt+2];tt.children=bt}return Y(N.type,ut,void 0,void 0,lt,tt)},Nt.createContext=function(N){return N={$$typeof:d,_currentValue:N,_currentValue2:N,_threadCount:0,Provider:null,Consumer:null},N.Provider=N,N.Consumer={$$typeof:c,_context:N},N},Nt.createElement=function(N,G,et){var tt,ut={},lt=null;if(G!=null)for(tt in G.key!==void 0&&(lt=""+G.key),G)L.call(G,tt)&&tt!=="key"&&tt!=="__self"&&tt!=="__source"&&(ut[tt]=G[tt]);var ct=arguments.length-2;if(ct===1)ut.children=et;else if(1<ct){for(var bt=Array(ct),wt=0;wt<ct;wt++)bt[wt]=arguments[wt+2];ut.children=bt}if(N&&N.defaultProps)for(tt in ct=N.defaultProps,ct)ut[tt]===void 0&&(ut[tt]=ct[tt]);return Y(N,lt,void 0,void 0,null,ut)},Nt.createRef=function(){return{current:null}},Nt.forwardRef=function(N){return{$$typeof:p,render:N}},Nt.isValidElement=K,Nt.lazy=function(N){return{$$typeof:y,_payload:{_status:-1,_result:N},_init:I}},Nt.memo=function(N,G){return{$$typeof:h,type:N,compare:G===void 0?null:G}},Nt.startTransition=function(N){var G=_.T,et={};_.T=et;try{var tt=N(),ut=_.S;ut!==null&&ut(et,tt),typeof tt=="object"&&tt!==null&&typeof tt.then=="function"&&tt.then(Z,ot)}catch(lt){ot(lt)}finally{_.T=G}},Nt.unstable_useCacheRefresh=function(){return _.H.useCacheRefresh()},Nt.use=function(N){return _.H.use(N)},Nt.useActionState=function(N,G,et){return _.H.useActionState(N,G,et)},Nt.useCallback=function(N,G){return _.H.useCallback(N,G)},Nt.useContext=function(N){return _.H.useContext(N)},Nt.useDebugValue=function(){},Nt.useDeferredValue=function(N,G){return _.H.useDeferredValue(N,G)},Nt.useEffect=function(N,G,et){var tt=_.H;if(typeof et=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return tt.useEffect(N,G)},Nt.useId=function(){return _.H.useId()},Nt.useImperativeHandle=function(N,G,et){return _.H.useImperativeHandle(N,G,et)},Nt.useInsertionEffect=function(N,G){return _.H.useInsertionEffect(N,G)},Nt.useLayoutEffect=function(N,G){return _.H.useLayoutEffect(N,G)},Nt.useMemo=function(N,G){return _.H.useMemo(N,G)},Nt.useOptimistic=function(N,G){return _.H.useOptimistic(N,G)},Nt.useReducer=function(N,G,et){return _.H.useReducer(N,G,et)},Nt.useRef=function(N){return _.H.useRef(N)},Nt.useState=function(N){return _.H.useState(N)},Nt.useSyncExternalStore=function(N,G,et){return _.H.useSyncExternalStore(N,G,et)},Nt.useTransition=function(){return _.H.useTransition()},Nt.version="19.1.0",Nt}var ty;function Md(){return ty||(ty=1,Df.exports=BS()),Df.exports}var S=Md();const Wn=B0(S),Gs=_S({__proto__:null,default:Wn},[S]);var kf={exports:{}},Bl={},Bf={exports:{}},$f={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ey;function $S(){return ey||(ey=1,function(n){function r(k,I){var ot=k.length;k.push(I);t:for(;0<ot;){var Z=ot-1>>>1,N=k[Z];if(0<s(N,I))k[Z]=I,k[ot]=N,ot=Z;else break t}}function o(k){return k.length===0?null:k[0]}function i(k){if(k.length===0)return null;var I=k[0],ot=k.pop();if(ot!==I){k[0]=ot;t:for(var Z=0,N=k.length,G=N>>>1;Z<G;){var et=2*(Z+1)-1,tt=k[et],ut=et+1,lt=k[ut];if(0>s(tt,ot))ut<N&&0>s(lt,tt)?(k[Z]=lt,k[ut]=ot,Z=ut):(k[Z]=tt,k[et]=ot,Z=et);else if(ut<N&&0>s(lt,ot))k[Z]=lt,k[ut]=ot,Z=ut;else break t}}return I}function s(k,I){var ot=k.sortIndex-I.sortIndex;return ot!==0?ot:k.id-I.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,p=d.now();n.unstable_now=function(){return d.now()-p}}var m=[],h=[],y=1,b=null,x=3,R=!1,C=!1,w=!1,M=!1,j=typeof setTimeout=="function"?setTimeout:null,D=typeof clearTimeout=="function"?clearTimeout:null,A=typeof setImmediate<"u"?setImmediate:null;function O(k){for(var I=o(h);I!==null;){if(I.callback===null)i(h);else if(I.startTime<=k)i(h),I.sortIndex=I.expirationTime,r(m,I);else break;I=o(h)}}function _(k){if(w=!1,O(k),!C)if(o(m)!==null)C=!0,L||(L=!0,X());else{var I=o(h);I!==null&&Q(_,I.startTime-k)}}var L=!1,Y=-1,F=5,K=-1;function E(){return M?!0:!(n.unstable_now()-K<F)}function U(){if(M=!1,L){var k=n.unstable_now();K=k;var I=!0;try{t:{C=!1,w&&(w=!1,D(Y),Y=-1),R=!0;var ot=x;try{e:{for(O(k),b=o(m);b!==null&&!(b.expirationTime>k&&E());){var Z=b.callback;if(typeof Z=="function"){b.callback=null,x=b.priorityLevel;var N=Z(b.expirationTime<=k);if(k=n.unstable_now(),typeof N=="function"){b.callback=N,O(k),I=!0;break e}b===o(m)&&i(m),O(k)}else i(m);b=o(m)}if(b!==null)I=!0;else{var G=o(h);G!==null&&Q(_,G.startTime-k),I=!1}}break t}finally{b=null,x=ot,R=!1}I=void 0}}finally{I?X():L=!1}}}var X;if(typeof A=="function")X=function(){A(U)};else if(typeof MessageChannel<"u"){var at=new MessageChannel,it=at.port2;at.port1.onmessage=U,X=function(){it.postMessage(null)}}else X=function(){j(U,0)};function Q(k,I){Y=j(function(){k(n.unstable_now())},I)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(k){k.callback=null},n.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<k?Math.floor(1e3/k):5},n.unstable_getCurrentPriorityLevel=function(){return x},n.unstable_next=function(k){switch(x){case 1:case 2:case 3:var I=3;break;default:I=x}var ot=x;x=I;try{return k()}finally{x=ot}},n.unstable_requestPaint=function(){M=!0},n.unstable_runWithPriority=function(k,I){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var ot=x;x=k;try{return I()}finally{x=ot}},n.unstable_scheduleCallback=function(k,I,ot){var Z=n.unstable_now();switch(typeof ot=="object"&&ot!==null?(ot=ot.delay,ot=typeof ot=="number"&&0<ot?Z+ot:Z):ot=Z,k){case 1:var N=-1;break;case 2:N=250;break;case 5:N=1073741823;break;case 4:N=1e4;break;default:N=5e3}return N=ot+N,k={id:y++,callback:I,priorityLevel:k,startTime:ot,expirationTime:N,sortIndex:-1},ot>Z?(k.sortIndex=ot,r(h,k),o(m)===null&&k===o(h)&&(w?(D(Y),Y=-1):w=!0,Q(_,ot-Z))):(k.sortIndex=N,r(m,k),C||R||(C=!0,L||(L=!0,X()))),k},n.unstable_shouldYield=E,n.unstable_wrapCallback=function(k){var I=x;return function(){var ot=x;x=I;try{return k.apply(this,arguments)}finally{x=ot}}}}($f)),$f}var ny;function LS(){return ny||(ny=1,Bf.exports=$S()),Bf.exports}var Lf={exports:{}},Ke={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ay;function US(){if(ay)return Ke;ay=1;var n=Md();function r(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)h+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(r(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");function c(m,h,y){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:b==null?null:""+b,children:m,containerInfo:h,implementation:y}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return Ke.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,Ke.createPortal=function(m,h){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(r(299));return c(m,h,null,y)},Ke.flushSync=function(m){var h=d.T,y=i.p;try{if(d.T=null,i.p=2,m)return m()}finally{d.T=h,i.p=y,i.d.f()}},Ke.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,i.d.C(m,h))},Ke.prefetchDNS=function(m){typeof m=="string"&&i.d.D(m)},Ke.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var y=h.as,b=p(y,h.crossOrigin),x=typeof h.integrity=="string"?h.integrity:void 0,R=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;y==="style"?i.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:b,integrity:x,fetchPriority:R}):y==="script"&&i.d.X(m,{crossOrigin:b,integrity:x,fetchPriority:R,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},Ke.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var y=p(h.as,h.crossOrigin);i.d.M(m,{crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&i.d.M(m)},Ke.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var y=h.as,b=p(y,h.crossOrigin);i.d.L(m,y,{crossOrigin:b,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},Ke.preloadModule=function(m,h){if(typeof m=="string")if(h){var y=p(h.as,h.crossOrigin);i.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else i.d.m(m)},Ke.requestFormReset=function(m){i.d.r(m)},Ke.unstable_batchedUpdates=function(m,h){return m(h)},Ke.useFormState=function(m,h,y){return d.H.useFormState(m,h,y)},Ke.useFormStatus=function(){return d.H.useHostTransitionStatus()},Ke.version="19.1.0",Ke}var ry;function $0(){if(ry)return Lf.exports;ry=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Lf.exports=US(),Lf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oy;function HS(){if(oy)return Bl;oy=1;var n=LS(),r=Md(),o=$0();function i(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function d(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(c(t)!==t)throw Error(i(188))}function m(t){var e=t.alternate;if(!e){if(e=c(t),e===null)throw Error(i(188));return e!==t?null:t}for(var a=t,l=e;;){var u=a.return;if(u===null)break;var f=u.alternate;if(f===null){if(l=u.return,l!==null){a=l;continue}break}if(u.child===f.child){for(f=u.child;f;){if(f===a)return p(u),t;if(f===l)return p(u),e;f=f.sibling}throw Error(i(188))}if(a.return!==l.return)a=u,l=f;else{for(var v=!1,T=u.child;T;){if(T===a){v=!0,a=u,l=f;break}if(T===l){v=!0,l=u,a=f;break}T=T.sibling}if(!v){for(T=f.child;T;){if(T===a){v=!0,a=f,l=u;break}if(T===l){v=!0,l=f,a=u;break}T=T.sibling}if(!v)throw Error(i(189))}}if(a.alternate!==l)throw Error(i(190))}if(a.tag!==3)throw Error(i(188));return a.stateNode.current===a?t:e}function h(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=h(t),e!==null)return e;t=t.sibling}return null}var y=Object.assign,b=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),w=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),D=Symbol.for("react.consumer"),A=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),L=Symbol.for("react.suspense_list"),Y=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),K=Symbol.for("react.activity"),E=Symbol.for("react.memo_cache_sentinel"),U=Symbol.iterator;function X(t){return t===null||typeof t!="object"?null:(t=U&&t[U]||t["@@iterator"],typeof t=="function"?t:null)}var at=Symbol.for("react.client.reference");function it(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===at?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case C:return"Fragment";case M:return"Profiler";case w:return"StrictMode";case _:return"Suspense";case L:return"SuspenseList";case K:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case R:return"Portal";case A:return(t.displayName||"Context")+".Provider";case D:return(t._context.displayName||"Context")+".Consumer";case O:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Y:return e=t.displayName||null,e!==null?e:it(t.type)||"Memo";case F:e=t._payload,t=t._init;try{return it(t(e))}catch{}}return null}var Q=Array.isArray,k=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ot={pending:!1,data:null,method:null,action:null},Z=[],N=-1;function G(t){return{current:t}}function et(t){0>N||(t.current=Z[N],Z[N]=null,N--)}function tt(t,e){N++,Z[N]=t.current,t.current=e}var ut=G(null),lt=G(null),ct=G(null),bt=G(null);function wt(t,e){switch(tt(ct,e),tt(lt,t),tt(ut,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Tg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Tg(e),t=Rg(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}et(ut),tt(ut,t)}function zt(){et(ut),et(lt),et(ct)}function gt(t){t.memoizedState!==null&&tt(bt,t);var e=ut.current,a=Rg(e,t.type);e!==a&&(tt(lt,t),tt(ut,a))}function _t(t){lt.current===t&&(et(ut),et(lt)),bt.current===t&&(et(bt),Ol._currentValue=ot)}var xt=Object.prototype.hasOwnProperty,qt=n.unstable_scheduleCallback,jt=n.unstable_cancelCallback,Xt=n.unstable_shouldYield,He=n.unstable_requestPaint,Kt=n.unstable_now,se=n.unstable_getCurrentPriorityLevel,ie=n.unstable_ImmediatePriority,de=n.unstable_UserBlockingPriority,Yt=n.unstable_NormalPriority,ht=n.unstable_LowPriority,un=n.unstable_IdlePriority,he=n.log,kn=n.unstable_setDisableYieldValue,en=null,ge=null;function De(t){if(typeof he=="function"&&kn(t),ge&&typeof ge.setStrictMode=="function")try{ge.setStrictMode(en,t)}catch{}}var pe=Math.clz32?Math.clz32:Ye,ue=Math.log,Et=Math.LN2;function Ye(t){return t>>>=0,t===0?32:31-(ue(t)/Et|0)|0}var Te=256,je=4194304;function xn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function tr(t,e,a){var l=t.pendingLanes;if(l===0)return 0;var u=0,f=t.suspendedLanes,v=t.pingedLanes;t=t.warmLanes;var T=l&134217727;return T!==0?(l=T&~f,l!==0?u=xn(l):(v&=T,v!==0?u=xn(v):a||(a=T&~t,a!==0&&(u=xn(a))))):(T=l&~f,T!==0?u=xn(T):v!==0?u=xn(v):a||(a=l&~t,a!==0&&(u=xn(a)))),u===0?0:e!==0&&e!==u&&(e&f)===0&&(f=u&-u,a=e&-e,f>=a||f===32&&(a&4194048)!==0)?e:u}function vt(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function ne(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Re(){var t=Te;return Te<<=1,(Te&4194048)===0&&(Te=256),t}function na(){var t=je;return je<<=1,(je&62914560)===0&&(je=4194304),t}function Nr(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function Uo(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function gb(t,e,a,l,u,f){var v=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var T=t.entanglements,z=t.expirationTimes,P=t.hiddenUpdates;for(a=v&~a;0<a;){var J=31-pe(a),rt=1<<J;T[J]=0,z[J]=-1;var q=P[J];if(q!==null)for(P[J]=null,J=0;J<q.length;J++){var V=q[J];V!==null&&(V.lane&=-536870913)}a&=~rt}l!==0&&up(t,l,0),f!==0&&u===0&&t.tag!==0&&(t.suspendedLanes|=f&~(v&~e))}function up(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-pe(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|a&4194090}function cp(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var l=31-pe(a),u=1<<l;u&e|t[l]&e&&(t[l]|=e),a&=~u}}function Cu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Eu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function fp(){var t=I.p;return t!==0?t:(t=window.event,t===void 0?32:Ig(t.type))}function yb(t,e){var a=I.p;try{return I.p=t,e()}finally{I.p=a}}var Ta=Math.random().toString(36).slice(2),Fe="__reactFiber$"+Ta,nn="__reactProps$"+Ta,zr="__reactContainer$"+Ta,Tu="__reactEvents$"+Ta,vb="__reactListeners$"+Ta,bb="__reactHandles$"+Ta,dp="__reactResources$"+Ta,Ho="__reactMarker$"+Ta;function Ru(t){delete t[Fe],delete t[nn],delete t[Tu],delete t[vb],delete t[bb]}function Dr(t){var e=t[Fe];if(e)return e;for(var a=t.parentNode;a;){if(e=a[zr]||a[Fe]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=Og(t);t!==null;){if(a=t[Fe])return a;t=Og(t)}return e}t=a,a=t.parentNode}return null}function kr(t){if(t=t[Fe]||t[zr]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Po(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(i(33))}function Br(t){var e=t[dp];return e||(e=t[dp]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ke(t){t[Ho]=!0}var pp=new Set,mp={};function er(t,e){$r(t,e),$r(t+"Capture",e)}function $r(t,e){for(mp[t]=e,t=0;t<e.length;t++)pp.add(e[t])}var Sb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),hp={},gp={};function xb(t){return xt.call(gp,t)?!0:xt.call(hp,t)?!1:Sb.test(t)?gp[t]=!0:(hp[t]=!0,!1)}function vi(t,e,a){if(xb(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function bi(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function aa(t,e,a,l){if(l===null)t.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+l)}}var wu,yp;function Lr(t){if(wu===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);wu=e&&e[1]||"",yp=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+wu+t+yp}var Mu=!1;function Au(t,e){if(!t||Mu)return"";Mu=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var rt=function(){throw Error()};if(Object.defineProperty(rt.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(rt,[])}catch(V){var q=V}Reflect.construct(t,[],rt)}else{try{rt.call()}catch(V){q=V}t.call(rt.prototype)}}else{try{throw Error()}catch(V){q=V}(rt=t())&&typeof rt.catch=="function"&&rt.catch(function(){})}}catch(V){if(V&&q&&typeof V.stack=="string")return[V.stack,q.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=l.DetermineComponentFrameRoot(),v=f[0],T=f[1];if(v&&T){var z=v.split(`
`),P=T.split(`
`);for(u=l=0;l<z.length&&!z[l].includes("DetermineComponentFrameRoot");)l++;for(;u<P.length&&!P[u].includes("DetermineComponentFrameRoot");)u++;if(l===z.length||u===P.length)for(l=z.length-1,u=P.length-1;1<=l&&0<=u&&z[l]!==P[u];)u--;for(;1<=l&&0<=u;l--,u--)if(z[l]!==P[u]){if(l!==1||u!==1)do if(l--,u--,0>u||z[l]!==P[u]){var J=`
`+z[l].replace(" at new "," at ");return t.displayName&&J.includes("<anonymous>")&&(J=J.replace("<anonymous>",t.displayName)),J}while(1<=l&&0<=u);break}}}finally{Mu=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?Lr(a):""}function Cb(t){switch(t.tag){case 26:case 27:case 5:return Lr(t.type);case 16:return Lr("Lazy");case 13:return Lr("Suspense");case 19:return Lr("SuspenseList");case 0:case 15:return Au(t.type,!1);case 11:return Au(t.type.render,!1);case 1:return Au(t.type,!0);case 31:return Lr("Activity");default:return""}}function vp(t){try{var e="";do e+=Cb(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Cn(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function bp(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Eb(t){var e=bp(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var u=a.get,f=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return u.call(this)},set:function(v){l=""+v,f.call(this,v)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(v){l=""+v},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Si(t){t._valueTracker||(t._valueTracker=Eb(t))}function Sp(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),l="";return t&&(l=bp(t)?t.checked?"true":"false":t.value),t=l,t!==a?(e.setValue(t),!0):!1}function xi(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Tb=/[\n"\\]/g;function En(t){return t.replace(Tb,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ou(t,e,a,l,u,f,v,T){t.name="",v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?t.type=v:t.removeAttribute("type"),e!=null?v==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Cn(e)):t.value!==""+Cn(e)&&(t.value=""+Cn(e)):v!=="submit"&&v!=="reset"||t.removeAttribute("value"),e!=null?_u(t,v,Cn(e)):a!=null?_u(t,v,Cn(a)):l!=null&&t.removeAttribute("value"),u==null&&f!=null&&(t.defaultChecked=!!f),u!=null&&(t.checked=u&&typeof u!="function"&&typeof u!="symbol"),T!=null&&typeof T!="function"&&typeof T!="symbol"&&typeof T!="boolean"?t.name=""+Cn(T):t.removeAttribute("name")}function xp(t,e,a,l,u,f,v,T){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||a!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;a=a!=null?""+Cn(a):"",e=e!=null?""+Cn(e):a,T||e===t.value||(t.value=e),t.defaultValue=e}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=T?t.checked:!!l,t.defaultChecked=!!l,v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"&&(t.name=v)}function _u(t,e,a){e==="number"&&xi(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function Ur(t,e,a,l){if(t=t.options,e){e={};for(var u=0;u<a.length;u++)e["$"+a[u]]=!0;for(a=0;a<t.length;a++)u=e.hasOwnProperty("$"+t[a].value),t[a].selected!==u&&(t[a].selected=u),u&&l&&(t[a].defaultSelected=!0)}else{for(a=""+Cn(a),e=null,u=0;u<t.length;u++){if(t[u].value===a){t[u].selected=!0,l&&(t[u].defaultSelected=!0);return}e!==null||t[u].disabled||(e=t[u])}e!==null&&(e.selected=!0)}}function Cp(t,e,a){if(e!=null&&(e=""+Cn(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+Cn(a):""}function Ep(t,e,a,l){if(e==null){if(l!=null){if(a!=null)throw Error(i(92));if(Q(l)){if(1<l.length)throw Error(i(93));l=l[0]}a=l}a==null&&(a=""),e=a}a=Cn(e),t.defaultValue=a,l=t.textContent,l===a&&l!==""&&l!==null&&(t.value=l)}function Hr(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var Rb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Tp(t,e,a){var l=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,a):typeof a!="number"||a===0||Rb.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function Rp(t,e,a){if(e!=null&&typeof e!="object")throw Error(i(62));if(t=t.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var u in e)l=e[u],e.hasOwnProperty(u)&&a[u]!==l&&Tp(t,u,l)}else for(var f in e)e.hasOwnProperty(f)&&Tp(t,f,e[f])}function ju(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Mb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ci(t){return Mb.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Nu=null;function zu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Pr=null,qr=null;function wp(t){var e=kr(t);if(e&&(t=e.stateNode)){var a=t[nn]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ou(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+En(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var l=a[e];if(l!==t&&l.form===t.form){var u=l[nn]||null;if(!u)throw Error(i(90));Ou(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(e=0;e<a.length;e++)l=a[e],l.form===t.form&&Sp(l)}break t;case"textarea":Cp(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&Ur(t,!!a.multiple,e,!1)}}}var Du=!1;function Mp(t,e,a){if(Du)return t(e,a);Du=!0;try{var l=t(e);return l}finally{if(Du=!1,(Pr!==null||qr!==null)&&(is(),Pr&&(e=Pr,t=qr,qr=Pr=null,wp(e),t)))for(e=0;e<t.length;e++)wp(t[e])}}function qo(t,e){var a=t.stateNode;if(a===null)return null;var l=a[nn]||null;if(l===null)return null;a=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(i(231,e,typeof a));return a}var ra=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ku=!1;if(ra)try{var Go={};Object.defineProperty(Go,"passive",{get:function(){ku=!0}}),window.addEventListener("test",Go,Go),window.removeEventListener("test",Go,Go)}catch{ku=!1}var Ra=null,Bu=null,Ei=null;function Ap(){if(Ei)return Ei;var t,e=Bu,a=e.length,l,u="value"in Ra?Ra.value:Ra.textContent,f=u.length;for(t=0;t<a&&e[t]===u[t];t++);var v=a-t;for(l=1;l<=v&&e[a-l]===u[f-l];l++);return Ei=u.slice(t,1<l?1-l:void 0)}function Ti(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ri(){return!0}function Op(){return!1}function an(t){function e(a,l,u,f,v){this._reactName=a,this._targetInst=u,this.type=l,this.nativeEvent=f,this.target=v,this.currentTarget=null;for(var T in t)t.hasOwnProperty(T)&&(a=t[T],this[T]=a?a(f):f[T]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Ri:Op,this.isPropagationStopped=Op,this}return y(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Ri)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Ri)},persist:function(){},isPersistent:Ri}),e}var nr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wi=an(nr),Io=y({},nr,{view:0,detail:0}),Ab=an(Io),$u,Lu,Vo,Mi=y({},Io,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Vo&&(Vo&&t.type==="mousemove"?($u=t.screenX-Vo.screenX,Lu=t.screenY-Vo.screenY):Lu=$u=0,Vo=t),$u)},movementY:function(t){return"movementY"in t?t.movementY:Lu}}),_p=an(Mi),Ob=y({},Mi,{dataTransfer:0}),_b=an(Ob),jb=y({},Io,{relatedTarget:0}),Uu=an(jb),Nb=y({},nr,{animationName:0,elapsedTime:0,pseudoElement:0}),zb=an(Nb),Db=y({},nr,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),kb=an(Db),Bb=y({},nr,{data:0}),jp=an(Bb),$b={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Lb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ub={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Hb(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Ub[t])?!!e[t]:!1}function Hu(){return Hb}var Pb=y({},Io,{key:function(t){if(t.key){var e=$b[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ti(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Lb[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hu,charCode:function(t){return t.type==="keypress"?Ti(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ti(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),qb=an(Pb),Gb=y({},Mi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Np=an(Gb),Ib=y({},Io,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hu}),Vb=an(Ib),Yb=y({},nr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Fb=an(Yb),Xb=y({},Mi,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Kb=an(Xb),Qb=y({},nr,{newState:0,oldState:0}),Wb=an(Qb),Zb=[9,13,27,32],Pu=ra&&"CompositionEvent"in window,Yo=null;ra&&"documentMode"in document&&(Yo=document.documentMode);var Jb=ra&&"TextEvent"in window&&!Yo,zp=ra&&(!Pu||Yo&&8<Yo&&11>=Yo),Dp=" ",kp=!1;function Bp(t,e){switch(t){case"keyup":return Zb.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $p(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Gr=!1;function t1(t,e){switch(t){case"compositionend":return $p(e);case"keypress":return e.which!==32?null:(kp=!0,Dp);case"textInput":return t=e.data,t===Dp&&kp?null:t;default:return null}}function e1(t,e){if(Gr)return t==="compositionend"||!Pu&&Bp(t,e)?(t=Ap(),Ei=Bu=Ra=null,Gr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return zp&&e.locale!=="ko"?null:e.data;default:return null}}var n1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Lp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!n1[t.type]:e==="textarea"}function Up(t,e,a,l){Pr?qr?qr.push(l):qr=[l]:Pr=l,e=ps(e,"onChange"),0<e.length&&(a=new wi("onChange","change",null,a,l),t.push({event:a,listeners:e}))}var Fo=null,Xo=null;function a1(t){bg(t,0)}function Ai(t){var e=Po(t);if(Sp(e))return t}function Hp(t,e){if(t==="change")return e}var Pp=!1;if(ra){var qu;if(ra){var Gu="oninput"in document;if(!Gu){var qp=document.createElement("div");qp.setAttribute("oninput","return;"),Gu=typeof qp.oninput=="function"}qu=Gu}else qu=!1;Pp=qu&&(!document.documentMode||9<document.documentMode)}function Gp(){Fo&&(Fo.detachEvent("onpropertychange",Ip),Xo=Fo=null)}function Ip(t){if(t.propertyName==="value"&&Ai(Xo)){var e=[];Up(e,Xo,t,zu(t)),Mp(a1,e)}}function r1(t,e,a){t==="focusin"?(Gp(),Fo=e,Xo=a,Fo.attachEvent("onpropertychange",Ip)):t==="focusout"&&Gp()}function o1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Ai(Xo)}function l1(t,e){if(t==="click")return Ai(e)}function i1(t,e){if(t==="input"||t==="change")return Ai(e)}function s1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var cn=typeof Object.is=="function"?Object.is:s1;function Ko(t,e){if(cn(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),l=Object.keys(e);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var u=a[l];if(!xt.call(e,u)||!cn(t[u],e[u]))return!1}return!0}function Vp(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Yp(t,e){var a=Vp(t);t=0;for(var l;a;){if(a.nodeType===3){if(l=t+a.textContent.length,t<=e&&l>=e)return{node:a,offset:e-t};t=l}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=Vp(a)}}function Fp(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Fp(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Xp(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=xi(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=xi(t.document)}return e}function Iu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var u1=ra&&"documentMode"in document&&11>=document.documentMode,Ir=null,Vu=null,Qo=null,Yu=!1;function Kp(t,e,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Yu||Ir==null||Ir!==xi(l)||(l=Ir,"selectionStart"in l&&Iu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Qo&&Ko(Qo,l)||(Qo=l,l=ps(Vu,"onSelect"),0<l.length&&(e=new wi("onSelect","select",null,e,a),t.push({event:e,listeners:l}),e.target=Ir)))}function ar(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Vr={animationend:ar("Animation","AnimationEnd"),animationiteration:ar("Animation","AnimationIteration"),animationstart:ar("Animation","AnimationStart"),transitionrun:ar("Transition","TransitionRun"),transitionstart:ar("Transition","TransitionStart"),transitioncancel:ar("Transition","TransitionCancel"),transitionend:ar("Transition","TransitionEnd")},Fu={},Qp={};ra&&(Qp=document.createElement("div").style,"AnimationEvent"in window||(delete Vr.animationend.animation,delete Vr.animationiteration.animation,delete Vr.animationstart.animation),"TransitionEvent"in window||delete Vr.transitionend.transition);function rr(t){if(Fu[t])return Fu[t];if(!Vr[t])return t;var e=Vr[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in Qp)return Fu[t]=e[a];return t}var Wp=rr("animationend"),Zp=rr("animationiteration"),Jp=rr("animationstart"),c1=rr("transitionrun"),f1=rr("transitionstart"),d1=rr("transitioncancel"),tm=rr("transitionend"),em=new Map,Xu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Xu.push("scrollEnd");function Bn(t,e){em.set(t,e),er(e,[t])}var nm=new WeakMap;function Tn(t,e){if(typeof t=="object"&&t!==null){var a=nm.get(t);return a!==void 0?a:(e={value:t,source:e,stack:vp(e)},nm.set(t,e),e)}return{value:t,source:e,stack:vp(e)}}var Rn=[],Yr=0,Ku=0;function Oi(){for(var t=Yr,e=Ku=Yr=0;e<t;){var a=Rn[e];Rn[e++]=null;var l=Rn[e];Rn[e++]=null;var u=Rn[e];Rn[e++]=null;var f=Rn[e];if(Rn[e++]=null,l!==null&&u!==null){var v=l.pending;v===null?u.next=u:(u.next=v.next,v.next=u),l.pending=u}f!==0&&am(a,u,f)}}function _i(t,e,a,l){Rn[Yr++]=t,Rn[Yr++]=e,Rn[Yr++]=a,Rn[Yr++]=l,Ku|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Qu(t,e,a,l){return _i(t,e,a,l),ji(t)}function Fr(t,e){return _i(t,null,null,e),ji(t)}function am(t,e,a){t.lanes|=a;var l=t.alternate;l!==null&&(l.lanes|=a);for(var u=!1,f=t.return;f!==null;)f.childLanes|=a,l=f.alternate,l!==null&&(l.childLanes|=a),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(u=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,u&&e!==null&&(u=31-pe(a),t=f.hiddenUpdates,l=t[u],l===null?t[u]=[e]:l.push(e),e.lane=a|536870912),f):null}function ji(t){if(50<xl)throw xl=0,nf=null,Error(i(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Xr={};function p1(t,e,a,l){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function fn(t,e,a,l){return new p1(t,e,a,l)}function Wu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function oa(t,e){var a=t.alternate;return a===null?(a=fn(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function rm(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ni(t,e,a,l,u,f){var v=0;if(l=t,typeof t=="function")Wu(t)&&(v=1);else if(typeof t=="string")v=hS(t,a,ut.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case K:return t=fn(31,a,e,u),t.elementType=K,t.lanes=f,t;case C:return or(a.children,u,f,e);case w:v=8,u|=24;break;case M:return t=fn(12,a,e,u|2),t.elementType=M,t.lanes=f,t;case _:return t=fn(13,a,e,u),t.elementType=_,t.lanes=f,t;case L:return t=fn(19,a,e,u),t.elementType=L,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case j:case A:v=10;break t;case D:v=9;break t;case O:v=11;break t;case Y:v=14;break t;case F:v=16,l=null;break t}v=29,a=Error(i(130,t===null?"null":typeof t,"")),l=null}return e=fn(v,a,e,u),e.elementType=t,e.type=l,e.lanes=f,e}function or(t,e,a,l){return t=fn(7,t,l,e),t.lanes=a,t}function Zu(t,e,a){return t=fn(6,t,null,e),t.lanes=a,t}function Ju(t,e,a){return e=fn(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Kr=[],Qr=0,zi=null,Di=0,wn=[],Mn=0,lr=null,la=1,ia="";function ir(t,e){Kr[Qr++]=Di,Kr[Qr++]=zi,zi=t,Di=e}function om(t,e,a){wn[Mn++]=la,wn[Mn++]=ia,wn[Mn++]=lr,lr=t;var l=la;t=ia;var u=32-pe(l)-1;l&=~(1<<u),a+=1;var f=32-pe(e)+u;if(30<f){var v=u-u%5;f=(l&(1<<v)-1).toString(32),l>>=v,u-=v,la=1<<32-pe(e)+u|a<<u|l,ia=f+t}else la=1<<f|a<<u|l,ia=t}function tc(t){t.return!==null&&(ir(t,1),om(t,1,0))}function ec(t){for(;t===zi;)zi=Kr[--Qr],Kr[Qr]=null,Di=Kr[--Qr],Kr[Qr]=null;for(;t===lr;)lr=wn[--Mn],wn[Mn]=null,ia=wn[--Mn],wn[Mn]=null,la=wn[--Mn],wn[Mn]=null}var Ze=null,ye=null,Gt=!1,sr=null,Pn=!1,nc=Error(i(519));function ur(t){var e=Error(i(418,""));throw Jo(Tn(e,t)),nc}function lm(t){var e=t.stateNode,a=t.type,l=t.memoizedProps;switch(e[Fe]=t,e[nn]=l,a){case"dialog":Ut("cancel",e),Ut("close",e);break;case"iframe":case"object":case"embed":Ut("load",e);break;case"video":case"audio":for(a=0;a<El.length;a++)Ut(El[a],e);break;case"source":Ut("error",e);break;case"img":case"image":case"link":Ut("error",e),Ut("load",e);break;case"details":Ut("toggle",e);break;case"input":Ut("invalid",e),xp(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Si(e);break;case"select":Ut("invalid",e);break;case"textarea":Ut("invalid",e),Ep(e,l.value,l.defaultValue,l.children),Si(e)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||l.suppressHydrationWarning===!0||Eg(e.textContent,a)?(l.popover!=null&&(Ut("beforetoggle",e),Ut("toggle",e)),l.onScroll!=null&&Ut("scroll",e),l.onScrollEnd!=null&&Ut("scrollend",e),l.onClick!=null&&(e.onclick=ms),e=!0):e=!1,e||ur(t)}function im(t){for(Ze=t.return;Ze;)switch(Ze.tag){case 5:case 13:Pn=!1;return;case 27:case 3:Pn=!0;return;default:Ze=Ze.return}}function Wo(t){if(t!==Ze)return!1;if(!Gt)return im(t),Gt=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||bf(t.type,t.memoizedProps)),a=!a),a&&ye&&ur(t),im(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(i(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){ye=Ln(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}ye=null}}else e===27?(e=ye,Pa(t.type)?(t=Ef,Ef=null,ye=t):ye=e):ye=Ze?Ln(t.stateNode.nextSibling):null;return!0}function Zo(){ye=Ze=null,Gt=!1}function sm(){var t=sr;return t!==null&&(ln===null?ln=t:ln.push.apply(ln,t),sr=null),t}function Jo(t){sr===null?sr=[t]:sr.push(t)}var ac=G(null),cr=null,sa=null;function wa(t,e,a){tt(ac,e._currentValue),e._currentValue=a}function ua(t){t._currentValue=ac.current,et(ac)}function rc(t,e,a){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===a)break;t=t.return}}function oc(t,e,a,l){var u=t.child;for(u!==null&&(u.return=t);u!==null;){var f=u.dependencies;if(f!==null){var v=u.child;f=f.firstContext;t:for(;f!==null;){var T=f;f=u;for(var z=0;z<e.length;z++)if(T.context===e[z]){f.lanes|=a,T=f.alternate,T!==null&&(T.lanes|=a),rc(f.return,a,t),l||(v=null);break t}f=T.next}}else if(u.tag===18){if(v=u.return,v===null)throw Error(i(341));v.lanes|=a,f=v.alternate,f!==null&&(f.lanes|=a),rc(v,a,t),v=null}else v=u.child;if(v!==null)v.return=u;else for(v=u;v!==null;){if(v===t){v=null;break}if(u=v.sibling,u!==null){u.return=v.return,v=u;break}v=v.return}u=v}}function tl(t,e,a,l){t=null;for(var u=e,f=!1;u!==null;){if(!f){if((u.flags&524288)!==0)f=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var v=u.alternate;if(v===null)throw Error(i(387));if(v=v.memoizedProps,v!==null){var T=u.type;cn(u.pendingProps.value,v.value)||(t!==null?t.push(T):t=[T])}}else if(u===bt.current){if(v=u.alternate,v===null)throw Error(i(387));v.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(t!==null?t.push(Ol):t=[Ol])}u=u.return}t!==null&&oc(e,t,a,l),e.flags|=262144}function ki(t){for(t=t.firstContext;t!==null;){if(!cn(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function fr(t){cr=t,sa=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Xe(t){return um(cr,t)}function Bi(t,e){return cr===null&&fr(t),um(t,e)}function um(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},sa===null){if(t===null)throw Error(i(308));sa=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else sa=sa.next=e;return a}var m1=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},h1=n.unstable_scheduleCallback,g1=n.unstable_NormalPriority,Ne={$$typeof:A,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function lc(){return{controller:new m1,data:new Map,refCount:0}}function el(t){t.refCount--,t.refCount===0&&h1(g1,function(){t.controller.abort()})}var nl=null,ic=0,Wr=0,Zr=null;function y1(t,e){if(nl===null){var a=nl=[];ic=0,Wr=cf(),Zr={status:"pending",value:void 0,then:function(l){a.push(l)}}}return ic++,e.then(cm,cm),e}function cm(){if(--ic===0&&nl!==null){Zr!==null&&(Zr.status="fulfilled");var t=nl;nl=null,Wr=0,Zr=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function v1(t,e){var a=[],l={status:"pending",value:null,reason:null,then:function(u){a.push(u)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var u=0;u<a.length;u++)(0,a[u])(e)},function(u){for(l.status="rejected",l.reason=u,u=0;u<a.length;u++)(0,a[u])(void 0)}),l}var fm=k.S;k.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&y1(t,e),fm!==null&&fm(t,e)};var dr=G(null);function sc(){var t=dr.current;return t!==null?t:le.pooledCache}function $i(t,e){e===null?tt(dr,dr.current):tt(dr,e.pool)}function dm(){var t=sc();return t===null?null:{parent:Ne._currentValue,pool:t}}var al=Error(i(460)),pm=Error(i(474)),Li=Error(i(542)),uc={then:function(){}};function mm(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ui(){}function hm(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(Ui,Ui),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ym(t),t;default:if(typeof e.status=="string")e.then(Ui,Ui);else{if(t=le,t!==null&&100<t.shellSuspendCounter)throw Error(i(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var u=e;u.status="fulfilled",u.value=l}},function(l){if(e.status==="pending"){var u=e;u.status="rejected",u.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ym(t),t}throw rl=e,al}}var rl=null;function gm(){if(rl===null)throw Error(i(459));var t=rl;return rl=null,t}function ym(t){if(t===al||t===Li)throw Error(i(483))}var Ma=!1;function cc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function fc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Aa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Oa(t,e,a){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Qt&2)!==0){var u=l.pending;return u===null?e.next=e:(e.next=u.next,u.next=e),l.pending=e,e=ji(t),am(t,null,a),e}return _i(t,l,e,a),ji(t)}function ol(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,cp(t,a)}}function dc(t,e){var a=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var u=null,f=null;if(a=a.firstBaseUpdate,a!==null){do{var v={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};f===null?u=f=v:f=f.next=v,a=a.next}while(a!==null);f===null?u=f=e:f=f.next=e}else u=f=e;a={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:f,shared:l.shared,callbacks:l.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var pc=!1;function ll(){if(pc){var t=Zr;if(t!==null)throw t}}function il(t,e,a,l){pc=!1;var u=t.updateQueue;Ma=!1;var f=u.firstBaseUpdate,v=u.lastBaseUpdate,T=u.shared.pending;if(T!==null){u.shared.pending=null;var z=T,P=z.next;z.next=null,v===null?f=P:v.next=P,v=z;var J=t.alternate;J!==null&&(J=J.updateQueue,T=J.lastBaseUpdate,T!==v&&(T===null?J.firstBaseUpdate=P:T.next=P,J.lastBaseUpdate=z))}if(f!==null){var rt=u.baseState;v=0,J=P=z=null,T=f;do{var q=T.lane&-536870913,V=q!==T.lane;if(V?(Ht&q)===q:(l&q)===q){q!==0&&q===Wr&&(pc=!0),J!==null&&(J=J.next={lane:0,tag:T.tag,payload:T.payload,callback:null,next:null});t:{var Tt=t,St=T;q=e;var te=a;switch(St.tag){case 1:if(Tt=St.payload,typeof Tt=="function"){rt=Tt.call(te,rt,q);break t}rt=Tt;break t;case 3:Tt.flags=Tt.flags&-65537|128;case 0:if(Tt=St.payload,q=typeof Tt=="function"?Tt.call(te,rt,q):Tt,q==null)break t;rt=y({},rt,q);break t;case 2:Ma=!0}}q=T.callback,q!==null&&(t.flags|=64,V&&(t.flags|=8192),V=u.callbacks,V===null?u.callbacks=[q]:V.push(q))}else V={lane:q,tag:T.tag,payload:T.payload,callback:T.callback,next:null},J===null?(P=J=V,z=rt):J=J.next=V,v|=q;if(T=T.next,T===null){if(T=u.shared.pending,T===null)break;V=T,T=V.next,V.next=null,u.lastBaseUpdate=V,u.shared.pending=null}}while(!0);J===null&&(z=rt),u.baseState=z,u.firstBaseUpdate=P,u.lastBaseUpdate=J,f===null&&(u.shared.lanes=0),$a|=v,t.lanes=v,t.memoizedState=rt}}function vm(t,e){if(typeof t!="function")throw Error(i(191,t));t.call(e)}function bm(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)vm(a[t],e)}var Jr=G(null),Hi=G(0);function Sm(t,e){t=ga,tt(Hi,t),tt(Jr,e),ga=t|e.baseLanes}function mc(){tt(Hi,ga),tt(Jr,Jr.current)}function hc(){ga=Hi.current,et(Jr),et(Hi)}var _a=0,Dt=null,Zt=null,we=null,Pi=!1,to=!1,pr=!1,qi=0,sl=0,eo=null,b1=0;function xe(){throw Error(i(321))}function gc(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!cn(t[a],e[a]))return!1;return!0}function yc(t,e,a,l,u,f){return _a=f,Dt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,k.H=t===null||t.memoizedState===null?ah:rh,pr=!1,f=a(l,u),pr=!1,to&&(f=Cm(e,a,l,u)),xm(t),f}function xm(t){k.H=Xi;var e=Zt!==null&&Zt.next!==null;if(_a=0,we=Zt=Dt=null,Pi=!1,sl=0,eo=null,e)throw Error(i(300));t===null||Be||(t=t.dependencies,t!==null&&ki(t)&&(Be=!0))}function Cm(t,e,a,l){Dt=t;var u=0;do{if(to&&(eo=null),sl=0,to=!1,25<=u)throw Error(i(301));if(u+=1,we=Zt=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}k.H=w1,f=e(a,l)}while(to);return f}function S1(){var t=k.H,e=t.useState()[0];return e=typeof e.then=="function"?ul(e):e,t=t.useState()[0],(Zt!==null?Zt.memoizedState:null)!==t&&(Dt.flags|=1024),e}function vc(){var t=qi!==0;return qi=0,t}function bc(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function Sc(t){if(Pi){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Pi=!1}_a=0,we=Zt=Dt=null,to=!1,sl=qi=0,eo=null}function rn(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return we===null?Dt.memoizedState=we=t:we=we.next=t,we}function Me(){if(Zt===null){var t=Dt.alternate;t=t!==null?t.memoizedState:null}else t=Zt.next;var e=we===null?Dt.memoizedState:we.next;if(e!==null)we=e,Zt=t;else{if(t===null)throw Dt.alternate===null?Error(i(467)):Error(i(310));Zt=t,t={memoizedState:Zt.memoizedState,baseState:Zt.baseState,baseQueue:Zt.baseQueue,queue:Zt.queue,next:null},we===null?Dt.memoizedState=we=t:we=we.next=t}return we}function xc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ul(t){var e=sl;return sl+=1,eo===null&&(eo=[]),t=hm(eo,t,e),e=Dt,(we===null?e.memoizedState:we.next)===null&&(e=e.alternate,k.H=e===null||e.memoizedState===null?ah:rh),t}function Gi(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ul(t);if(t.$$typeof===A)return Xe(t)}throw Error(i(438,String(t)))}function Cc(t){var e=null,a=Dt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var l=Dt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(u){return u.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=xc(),Dt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),l=0;l<t;l++)a[l]=E;return e.index++,a}function ca(t,e){return typeof e=="function"?e(t):e}function Ii(t){var e=Me();return Ec(e,Zt,t)}function Ec(t,e,a){var l=t.queue;if(l===null)throw Error(i(311));l.lastRenderedReducer=a;var u=t.baseQueue,f=l.pending;if(f!==null){if(u!==null){var v=u.next;u.next=f.next,f.next=v}e.baseQueue=u=f,l.pending=null}if(f=t.baseState,u===null)t.memoizedState=f;else{e=u.next;var T=v=null,z=null,P=e,J=!1;do{var rt=P.lane&-536870913;if(rt!==P.lane?(Ht&rt)===rt:(_a&rt)===rt){var q=P.revertLane;if(q===0)z!==null&&(z=z.next={lane:0,revertLane:0,action:P.action,hasEagerState:P.hasEagerState,eagerState:P.eagerState,next:null}),rt===Wr&&(J=!0);else if((_a&q)===q){P=P.next,q===Wr&&(J=!0);continue}else rt={lane:0,revertLane:P.revertLane,action:P.action,hasEagerState:P.hasEagerState,eagerState:P.eagerState,next:null},z===null?(T=z=rt,v=f):z=z.next=rt,Dt.lanes|=q,$a|=q;rt=P.action,pr&&a(f,rt),f=P.hasEagerState?P.eagerState:a(f,rt)}else q={lane:rt,revertLane:P.revertLane,action:P.action,hasEagerState:P.hasEagerState,eagerState:P.eagerState,next:null},z===null?(T=z=q,v=f):z=z.next=q,Dt.lanes|=rt,$a|=rt;P=P.next}while(P!==null&&P!==e);if(z===null?v=f:z.next=T,!cn(f,t.memoizedState)&&(Be=!0,J&&(a=Zr,a!==null)))throw a;t.memoizedState=f,t.baseState=v,t.baseQueue=z,l.lastRenderedState=f}return u===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Tc(t){var e=Me(),a=e.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=t;var l=a.dispatch,u=a.pending,f=e.memoizedState;if(u!==null){a.pending=null;var v=u=u.next;do f=t(f,v.action),v=v.next;while(v!==u);cn(f,e.memoizedState)||(Be=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),a.lastRenderedState=f}return[f,l]}function Em(t,e,a){var l=Dt,u=Me(),f=Gt;if(f){if(a===void 0)throw Error(i(407));a=a()}else a=e();var v=!cn((Zt||u).memoizedState,a);v&&(u.memoizedState=a,Be=!0),u=u.queue;var T=wm.bind(null,l,u,t);if(cl(2048,8,T,[t]),u.getSnapshot!==e||v||we!==null&&we.memoizedState.tag&1){if(l.flags|=2048,no(9,Vi(),Rm.bind(null,l,u,a,e),null),le===null)throw Error(i(349));f||(_a&124)!==0||Tm(l,e,a)}return a}function Tm(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=Dt.updateQueue,e===null?(e=xc(),Dt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function Rm(t,e,a,l){e.value=a,e.getSnapshot=l,Mm(e)&&Am(t)}function wm(t,e,a){return a(function(){Mm(e)&&Am(t)})}function Mm(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!cn(t,a)}catch{return!0}}function Am(t){var e=Fr(t,2);e!==null&&gn(e,t,2)}function Rc(t){var e=rn();if(typeof t=="function"){var a=t;if(t=a(),pr){De(!0);try{a()}finally{De(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ca,lastRenderedState:t},e}function Om(t,e,a,l){return t.baseState=a,Ec(t,Zt,typeof l=="function"?l:ca)}function x1(t,e,a,l,u){if(Fi(t))throw Error(i(485));if(t=e.action,t!==null){var f={payload:u,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(v){f.listeners.push(v)}};k.T!==null?a(!0):f.isTransition=!1,l(f),a=e.pending,a===null?(f.next=e.pending=f,_m(e,f)):(f.next=a.next,e.pending=a.next=f)}}function _m(t,e){var a=e.action,l=e.payload,u=t.state;if(e.isTransition){var f=k.T,v={};k.T=v;try{var T=a(u,l),z=k.S;z!==null&&z(v,T),jm(t,e,T)}catch(P){wc(t,e,P)}finally{k.T=f}}else try{f=a(u,l),jm(t,e,f)}catch(P){wc(t,e,P)}}function jm(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){Nm(t,e,l)},function(l){return wc(t,e,l)}):Nm(t,e,a)}function Nm(t,e,a){e.status="fulfilled",e.value=a,zm(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,_m(t,a)))}function wc(t,e,a){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=a,zm(e),e=e.next;while(e!==l)}t.action=null}function zm(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Dm(t,e){return e}function km(t,e){if(Gt){var a=le.formState;if(a!==null){t:{var l=Dt;if(Gt){if(ye){e:{for(var u=ye,f=Pn;u.nodeType!==8;){if(!f){u=null;break e}if(u=Ln(u.nextSibling),u===null){u=null;break e}}f=u.data,u=f==="F!"||f==="F"?u:null}if(u){ye=Ln(u.nextSibling),l=u.data==="F!";break t}}ur(l)}l=!1}l&&(e=a[0])}}return a=rn(),a.memoizedState=a.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dm,lastRenderedState:e},a.queue=l,a=th.bind(null,Dt,l),l.dispatch=a,l=Rc(!1),f=jc.bind(null,Dt,!1,l.queue),l=rn(),u={state:e,dispatch:null,action:t,pending:null},l.queue=u,a=x1.bind(null,Dt,u,f,a),u.dispatch=a,l.memoizedState=t,[e,a,!1]}function Bm(t){var e=Me();return $m(e,Zt,t)}function $m(t,e,a){if(e=Ec(t,e,Dm)[0],t=Ii(ca)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=ul(e)}catch(v){throw v===al?Li:v}else l=e;e=Me();var u=e.queue,f=u.dispatch;return a!==e.memoizedState&&(Dt.flags|=2048,no(9,Vi(),C1.bind(null,u,a),null)),[l,f,t]}function C1(t,e){t.action=e}function Lm(t){var e=Me(),a=Zt;if(a!==null)return $m(e,a,t);Me(),e=e.memoizedState,a=Me();var l=a.queue.dispatch;return a.memoizedState=t,[e,l,!1]}function no(t,e,a,l){return t={tag:t,create:a,deps:l,inst:e,next:null},e=Dt.updateQueue,e===null&&(e=xc(),Dt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(l=a.next,a.next=t,t.next=l,e.lastEffect=t),t}function Vi(){return{destroy:void 0,resource:void 0}}function Um(){return Me().memoizedState}function Yi(t,e,a,l){var u=rn();l=l===void 0?null:l,Dt.flags|=t,u.memoizedState=no(1|e,Vi(),a,l)}function cl(t,e,a,l){var u=Me();l=l===void 0?null:l;var f=u.memoizedState.inst;Zt!==null&&l!==null&&gc(l,Zt.memoizedState.deps)?u.memoizedState=no(e,f,a,l):(Dt.flags|=t,u.memoizedState=no(1|e,f,a,l))}function Hm(t,e){Yi(8390656,8,t,e)}function Pm(t,e){cl(2048,8,t,e)}function qm(t,e){return cl(4,2,t,e)}function Gm(t,e){return cl(4,4,t,e)}function Im(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Vm(t,e,a){a=a!=null?a.concat([t]):null,cl(4,4,Im.bind(null,e,t),a)}function Mc(){}function Ym(t,e){var a=Me();e=e===void 0?null:e;var l=a.memoizedState;return e!==null&&gc(e,l[1])?l[0]:(a.memoizedState=[t,e],t)}function Fm(t,e){var a=Me();e=e===void 0?null:e;var l=a.memoizedState;if(e!==null&&gc(e,l[1]))return l[0];if(l=t(),pr){De(!0);try{t()}finally{De(!1)}}return a.memoizedState=[l,e],l}function Ac(t,e,a){return a===void 0||(_a&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=Qh(),Dt.lanes|=t,$a|=t,a)}function Xm(t,e,a,l){return cn(a,e)?a:Jr.current!==null?(t=Ac(t,a,l),cn(t,e)||(Be=!0),t):(_a&42)===0?(Be=!0,t.memoizedState=a):(t=Qh(),Dt.lanes|=t,$a|=t,e)}function Km(t,e,a,l,u){var f=I.p;I.p=f!==0&&8>f?f:8;var v=k.T,T={};k.T=T,jc(t,!1,e,a);try{var z=u(),P=k.S;if(P!==null&&P(T,z),z!==null&&typeof z=="object"&&typeof z.then=="function"){var J=v1(z,l);fl(t,e,J,hn(t))}else fl(t,e,l,hn(t))}catch(rt){fl(t,e,{then:function(){},status:"rejected",reason:rt},hn())}finally{I.p=f,k.T=v}}function E1(){}function Oc(t,e,a,l){if(t.tag!==5)throw Error(i(476));var u=Qm(t).queue;Km(t,u,e,ot,a===null?E1:function(){return Wm(t),a(l)})}function Qm(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ot,baseState:ot,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ca,lastRenderedState:ot},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ca,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Wm(t){var e=Qm(t).next.queue;fl(t,e,{},hn())}function _c(){return Xe(Ol)}function Zm(){return Me().memoizedState}function Jm(){return Me().memoizedState}function T1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=hn();t=Aa(a);var l=Oa(e,t,a);l!==null&&(gn(l,e,a),ol(l,e,a)),e={cache:lc()},t.payload=e;return}e=e.return}}function R1(t,e,a){var l=hn();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Fi(t)?eh(e,a):(a=Qu(t,e,a,l),a!==null&&(gn(a,t,l),nh(a,e,l)))}function th(t,e,a){var l=hn();fl(t,e,a,l)}function fl(t,e,a,l){var u={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Fi(t))eh(e,u);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var v=e.lastRenderedState,T=f(v,a);if(u.hasEagerState=!0,u.eagerState=T,cn(T,v))return _i(t,e,u,0),le===null&&Oi(),!1}catch{}finally{}if(a=Qu(t,e,u,l),a!==null)return gn(a,t,l),nh(a,e,l),!0}return!1}function jc(t,e,a,l){if(l={lane:2,revertLane:cf(),action:l,hasEagerState:!1,eagerState:null,next:null},Fi(t)){if(e)throw Error(i(479))}else e=Qu(t,a,l,2),e!==null&&gn(e,t,2)}function Fi(t){var e=t.alternate;return t===Dt||e!==null&&e===Dt}function eh(t,e){to=Pi=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function nh(t,e,a){if((a&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,cp(t,a)}}var Xi={readContext:Xe,use:Gi,useCallback:xe,useContext:xe,useEffect:xe,useImperativeHandle:xe,useLayoutEffect:xe,useInsertionEffect:xe,useMemo:xe,useReducer:xe,useRef:xe,useState:xe,useDebugValue:xe,useDeferredValue:xe,useTransition:xe,useSyncExternalStore:xe,useId:xe,useHostTransitionStatus:xe,useFormState:xe,useActionState:xe,useOptimistic:xe,useMemoCache:xe,useCacheRefresh:xe},ah={readContext:Xe,use:Gi,useCallback:function(t,e){return rn().memoizedState=[t,e===void 0?null:e],t},useContext:Xe,useEffect:Hm,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,Yi(4194308,4,Im.bind(null,e,t),a)},useLayoutEffect:function(t,e){return Yi(4194308,4,t,e)},useInsertionEffect:function(t,e){Yi(4,2,t,e)},useMemo:function(t,e){var a=rn();e=e===void 0?null:e;var l=t();if(pr){De(!0);try{t()}finally{De(!1)}}return a.memoizedState=[l,e],l},useReducer:function(t,e,a){var l=rn();if(a!==void 0){var u=a(e);if(pr){De(!0);try{a(e)}finally{De(!1)}}}else u=e;return l.memoizedState=l.baseState=u,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:u},l.queue=t,t=t.dispatch=R1.bind(null,Dt,t),[l.memoizedState,t]},useRef:function(t){var e=rn();return t={current:t},e.memoizedState=t},useState:function(t){t=Rc(t);var e=t.queue,a=th.bind(null,Dt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:Mc,useDeferredValue:function(t,e){var a=rn();return Ac(a,t,e)},useTransition:function(){var t=Rc(!1);return t=Km.bind(null,Dt,t.queue,!0,!1),rn().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var l=Dt,u=rn();if(Gt){if(a===void 0)throw Error(i(407));a=a()}else{if(a=e(),le===null)throw Error(i(349));(Ht&124)!==0||Tm(l,e,a)}u.memoizedState=a;var f={value:a,getSnapshot:e};return u.queue=f,Hm(wm.bind(null,l,f,t),[t]),l.flags|=2048,no(9,Vi(),Rm.bind(null,l,f,a,e),null),a},useId:function(){var t=rn(),e=le.identifierPrefix;if(Gt){var a=ia,l=la;a=(l&~(1<<32-pe(l)-1)).toString(32)+a,e="«"+e+"R"+a,a=qi++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=b1++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:_c,useFormState:km,useActionState:km,useOptimistic:function(t){var e=rn();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=jc.bind(null,Dt,!0,a),a.dispatch=e,[t,e]},useMemoCache:Cc,useCacheRefresh:function(){return rn().memoizedState=T1.bind(null,Dt)}},rh={readContext:Xe,use:Gi,useCallback:Ym,useContext:Xe,useEffect:Pm,useImperativeHandle:Vm,useInsertionEffect:qm,useLayoutEffect:Gm,useMemo:Fm,useReducer:Ii,useRef:Um,useState:function(){return Ii(ca)},useDebugValue:Mc,useDeferredValue:function(t,e){var a=Me();return Xm(a,Zt.memoizedState,t,e)},useTransition:function(){var t=Ii(ca)[0],e=Me().memoizedState;return[typeof t=="boolean"?t:ul(t),e]},useSyncExternalStore:Em,useId:Zm,useHostTransitionStatus:_c,useFormState:Bm,useActionState:Bm,useOptimistic:function(t,e){var a=Me();return Om(a,Zt,t,e)},useMemoCache:Cc,useCacheRefresh:Jm},w1={readContext:Xe,use:Gi,useCallback:Ym,useContext:Xe,useEffect:Pm,useImperativeHandle:Vm,useInsertionEffect:qm,useLayoutEffect:Gm,useMemo:Fm,useReducer:Tc,useRef:Um,useState:function(){return Tc(ca)},useDebugValue:Mc,useDeferredValue:function(t,e){var a=Me();return Zt===null?Ac(a,t,e):Xm(a,Zt.memoizedState,t,e)},useTransition:function(){var t=Tc(ca)[0],e=Me().memoizedState;return[typeof t=="boolean"?t:ul(t),e]},useSyncExternalStore:Em,useId:Zm,useHostTransitionStatus:_c,useFormState:Lm,useActionState:Lm,useOptimistic:function(t,e){var a=Me();return Zt!==null?Om(a,Zt,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:Cc,useCacheRefresh:Jm},ao=null,dl=0;function Ki(t){var e=dl;return dl+=1,ao===null&&(ao=[]),hm(ao,t,e)}function pl(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Qi(t,e){throw e.$$typeof===b?Error(i(525)):(t=Object.prototype.toString.call(e),Error(i(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function oh(t){var e=t._init;return e(t._payload)}function lh(t){function e($,B){if(t){var H=$.deletions;H===null?($.deletions=[B],$.flags|=16):H.push(B)}}function a($,B){if(!t)return null;for(;B!==null;)e($,B),B=B.sibling;return null}function l($){for(var B=new Map;$!==null;)$.key!==null?B.set($.key,$):B.set($.index,$),$=$.sibling;return B}function u($,B){return $=oa($,B),$.index=0,$.sibling=null,$}function f($,B,H){return $.index=H,t?(H=$.alternate,H!==null?(H=H.index,H<B?($.flags|=67108866,B):H):($.flags|=67108866,B)):($.flags|=1048576,B)}function v($){return t&&$.alternate===null&&($.flags|=67108866),$}function T($,B,H,nt){return B===null||B.tag!==6?(B=Zu(H,$.mode,nt),B.return=$,B):(B=u(B,H),B.return=$,B)}function z($,B,H,nt){var pt=H.type;return pt===C?J($,B,H.props.children,nt,H.key):B!==null&&(B.elementType===pt||typeof pt=="object"&&pt!==null&&pt.$$typeof===F&&oh(pt)===B.type)?(B=u(B,H.props),pl(B,H),B.return=$,B):(B=Ni(H.type,H.key,H.props,null,$.mode,nt),pl(B,H),B.return=$,B)}function P($,B,H,nt){return B===null||B.tag!==4||B.stateNode.containerInfo!==H.containerInfo||B.stateNode.implementation!==H.implementation?(B=Ju(H,$.mode,nt),B.return=$,B):(B=u(B,H.children||[]),B.return=$,B)}function J($,B,H,nt,pt){return B===null||B.tag!==7?(B=or(H,$.mode,nt,pt),B.return=$,B):(B=u(B,H),B.return=$,B)}function rt($,B,H){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return B=Zu(""+B,$.mode,H),B.return=$,B;if(typeof B=="object"&&B!==null){switch(B.$$typeof){case x:return H=Ni(B.type,B.key,B.props,null,$.mode,H),pl(H,B),H.return=$,H;case R:return B=Ju(B,$.mode,H),B.return=$,B;case F:var nt=B._init;return B=nt(B._payload),rt($,B,H)}if(Q(B)||X(B))return B=or(B,$.mode,H,null),B.return=$,B;if(typeof B.then=="function")return rt($,Ki(B),H);if(B.$$typeof===A)return rt($,Bi($,B),H);Qi($,B)}return null}function q($,B,H,nt){var pt=B!==null?B.key:null;if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return pt!==null?null:T($,B,""+H,nt);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case x:return H.key===pt?z($,B,H,nt):null;case R:return H.key===pt?P($,B,H,nt):null;case F:return pt=H._init,H=pt(H._payload),q($,B,H,nt)}if(Q(H)||X(H))return pt!==null?null:J($,B,H,nt,null);if(typeof H.then=="function")return q($,B,Ki(H),nt);if(H.$$typeof===A)return q($,B,Bi($,H),nt);Qi($,H)}return null}function V($,B,H,nt,pt){if(typeof nt=="string"&&nt!==""||typeof nt=="number"||typeof nt=="bigint")return $=$.get(H)||null,T(B,$,""+nt,pt);if(typeof nt=="object"&&nt!==null){switch(nt.$$typeof){case x:return $=$.get(nt.key===null?H:nt.key)||null,z(B,$,nt,pt);case R:return $=$.get(nt.key===null?H:nt.key)||null,P(B,$,nt,pt);case F:var Bt=nt._init;return nt=Bt(nt._payload),V($,B,H,nt,pt)}if(Q(nt)||X(nt))return $=$.get(H)||null,J(B,$,nt,pt,null);if(typeof nt.then=="function")return V($,B,H,Ki(nt),pt);if(nt.$$typeof===A)return V($,B,H,Bi(B,nt),pt);Qi(B,nt)}return null}function Tt($,B,H,nt){for(var pt=null,Bt=null,yt=B,Ct=B=0,Le=null;yt!==null&&Ct<H.length;Ct++){yt.index>Ct?(Le=yt,yt=null):Le=yt.sibling;var Pt=q($,yt,H[Ct],nt);if(Pt===null){yt===null&&(yt=Le);break}t&&yt&&Pt.alternate===null&&e($,yt),B=f(Pt,B,Ct),Bt===null?pt=Pt:Bt.sibling=Pt,Bt=Pt,yt=Le}if(Ct===H.length)return a($,yt),Gt&&ir($,Ct),pt;if(yt===null){for(;Ct<H.length;Ct++)yt=rt($,H[Ct],nt),yt!==null&&(B=f(yt,B,Ct),Bt===null?pt=yt:Bt.sibling=yt,Bt=yt);return Gt&&ir($,Ct),pt}for(yt=l(yt);Ct<H.length;Ct++)Le=V(yt,$,Ct,H[Ct],nt),Le!==null&&(t&&Le.alternate!==null&&yt.delete(Le.key===null?Ct:Le.key),B=f(Le,B,Ct),Bt===null?pt=Le:Bt.sibling=Le,Bt=Le);return t&&yt.forEach(function(Ya){return e($,Ya)}),Gt&&ir($,Ct),pt}function St($,B,H,nt){if(H==null)throw Error(i(151));for(var pt=null,Bt=null,yt=B,Ct=B=0,Le=null,Pt=H.next();yt!==null&&!Pt.done;Ct++,Pt=H.next()){yt.index>Ct?(Le=yt,yt=null):Le=yt.sibling;var Ya=q($,yt,Pt.value,nt);if(Ya===null){yt===null&&(yt=Le);break}t&&yt&&Ya.alternate===null&&e($,yt),B=f(Ya,B,Ct),Bt===null?pt=Ya:Bt.sibling=Ya,Bt=Ya,yt=Le}if(Pt.done)return a($,yt),Gt&&ir($,Ct),pt;if(yt===null){for(;!Pt.done;Ct++,Pt=H.next())Pt=rt($,Pt.value,nt),Pt!==null&&(B=f(Pt,B,Ct),Bt===null?pt=Pt:Bt.sibling=Pt,Bt=Pt);return Gt&&ir($,Ct),pt}for(yt=l(yt);!Pt.done;Ct++,Pt=H.next())Pt=V(yt,$,Ct,Pt.value,nt),Pt!==null&&(t&&Pt.alternate!==null&&yt.delete(Pt.key===null?Ct:Pt.key),B=f(Pt,B,Ct),Bt===null?pt=Pt:Bt.sibling=Pt,Bt=Pt);return t&&yt.forEach(function(MS){return e($,MS)}),Gt&&ir($,Ct),pt}function te($,B,H,nt){if(typeof H=="object"&&H!==null&&H.type===C&&H.key===null&&(H=H.props.children),typeof H=="object"&&H!==null){switch(H.$$typeof){case x:t:{for(var pt=H.key;B!==null;){if(B.key===pt){if(pt=H.type,pt===C){if(B.tag===7){a($,B.sibling),nt=u(B,H.props.children),nt.return=$,$=nt;break t}}else if(B.elementType===pt||typeof pt=="object"&&pt!==null&&pt.$$typeof===F&&oh(pt)===B.type){a($,B.sibling),nt=u(B,H.props),pl(nt,H),nt.return=$,$=nt;break t}a($,B);break}else e($,B);B=B.sibling}H.type===C?(nt=or(H.props.children,$.mode,nt,H.key),nt.return=$,$=nt):(nt=Ni(H.type,H.key,H.props,null,$.mode,nt),pl(nt,H),nt.return=$,$=nt)}return v($);case R:t:{for(pt=H.key;B!==null;){if(B.key===pt)if(B.tag===4&&B.stateNode.containerInfo===H.containerInfo&&B.stateNode.implementation===H.implementation){a($,B.sibling),nt=u(B,H.children||[]),nt.return=$,$=nt;break t}else{a($,B);break}else e($,B);B=B.sibling}nt=Ju(H,$.mode,nt),nt.return=$,$=nt}return v($);case F:return pt=H._init,H=pt(H._payload),te($,B,H,nt)}if(Q(H))return Tt($,B,H,nt);if(X(H)){if(pt=X(H),typeof pt!="function")throw Error(i(150));return H=pt.call(H),St($,B,H,nt)}if(typeof H.then=="function")return te($,B,Ki(H),nt);if(H.$$typeof===A)return te($,B,Bi($,H),nt);Qi($,H)}return typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint"?(H=""+H,B!==null&&B.tag===6?(a($,B.sibling),nt=u(B,H),nt.return=$,$=nt):(a($,B),nt=Zu(H,$.mode,nt),nt.return=$,$=nt),v($)):a($,B)}return function($,B,H,nt){try{dl=0;var pt=te($,B,H,nt);return ao=null,pt}catch(yt){if(yt===al||yt===Li)throw yt;var Bt=fn(29,yt,null,$.mode);return Bt.lanes=nt,Bt.return=$,Bt}finally{}}}var ro=lh(!0),ih=lh(!1),An=G(null),qn=null;function ja(t){var e=t.alternate;tt(ze,ze.current&1),tt(An,t),qn===null&&(e===null||Jr.current!==null||e.memoizedState!==null)&&(qn=t)}function sh(t){if(t.tag===22){if(tt(ze,ze.current),tt(An,t),qn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(qn=t)}}else Na()}function Na(){tt(ze,ze.current),tt(An,An.current)}function fa(t){et(An),qn===t&&(qn=null),et(ze)}var ze=G(0);function Wi(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Cf(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Nc(t,e,a,l){e=t.memoizedState,a=a(l,e),a=a==null?e:y({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var zc={enqueueSetState:function(t,e,a){t=t._reactInternals;var l=hn(),u=Aa(l);u.payload=e,a!=null&&(u.callback=a),e=Oa(t,u,l),e!==null&&(gn(e,t,l),ol(e,t,l))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var l=hn(),u=Aa(l);u.tag=1,u.payload=e,a!=null&&(u.callback=a),e=Oa(t,u,l),e!==null&&(gn(e,t,l),ol(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=hn(),l=Aa(a);l.tag=2,e!=null&&(l.callback=e),e=Oa(t,l,a),e!==null&&(gn(e,t,a),ol(e,t,a))}};function uh(t,e,a,l,u,f,v){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,f,v):e.prototype&&e.prototype.isPureReactComponent?!Ko(a,l)||!Ko(u,f):!0}function ch(t,e,a,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,l),e.state!==t&&zc.enqueueReplaceState(e,e.state,null)}function mr(t,e){var a=e;if("ref"in e){a={};for(var l in e)l!=="ref"&&(a[l]=e[l])}if(t=t.defaultProps){a===e&&(a=y({},a));for(var u in t)a[u]===void 0&&(a[u]=t[u])}return a}var Zi=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function fh(t){Zi(t)}function dh(t){console.error(t)}function ph(t){Zi(t)}function Ji(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function mh(t,e,a){try{var l=t.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function Dc(t,e,a){return a=Aa(a),a.tag=3,a.payload={element:null},a.callback=function(){Ji(t,e)},a}function hh(t){return t=Aa(t),t.tag=3,t}function gh(t,e,a,l){var u=a.type.getDerivedStateFromError;if(typeof u=="function"){var f=l.value;t.payload=function(){return u(f)},t.callback=function(){mh(e,a,l)}}var v=a.stateNode;v!==null&&typeof v.componentDidCatch=="function"&&(t.callback=function(){mh(e,a,l),typeof u!="function"&&(La===null?La=new Set([this]):La.add(this));var T=l.stack;this.componentDidCatch(l.value,{componentStack:T!==null?T:""})})}function M1(t,e,a,l,u){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=a.alternate,e!==null&&tl(e,a,u,!0),a=An.current,a!==null){switch(a.tag){case 13:return qn===null?rf():a.alternate===null&&ve===0&&(ve=3),a.flags&=-257,a.flags|=65536,a.lanes=u,l===uc?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([l]):e.add(l),lf(t,l,u)),!1;case 22:return a.flags|=65536,l===uc?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([l]):a.add(l)),lf(t,l,u)),!1}throw Error(i(435,a.tag))}return lf(t,l,u),rf(),!1}if(Gt)return e=An.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=u,l!==nc&&(t=Error(i(422),{cause:l}),Jo(Tn(t,a)))):(l!==nc&&(e=Error(i(423),{cause:l}),Jo(Tn(e,a))),t=t.current.alternate,t.flags|=65536,u&=-u,t.lanes|=u,l=Tn(l,a),u=Dc(t.stateNode,l,u),dc(t,u),ve!==4&&(ve=2)),!1;var f=Error(i(520),{cause:l});if(f=Tn(f,a),Sl===null?Sl=[f]:Sl.push(f),ve!==4&&(ve=2),e===null)return!0;l=Tn(l,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=u&-u,a.lanes|=t,t=Dc(a.stateNode,l,t),dc(a,t),!1;case 1:if(e=a.type,f=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(La===null||!La.has(f))))return a.flags|=65536,u&=-u,a.lanes|=u,u=hh(u),gh(u,t,a,l),dc(a,u),!1}a=a.return}while(a!==null);return!1}var yh=Error(i(461)),Be=!1;function Pe(t,e,a,l){e.child=t===null?ih(e,null,a,l):ro(e,t.child,a,l)}function vh(t,e,a,l,u){a=a.render;var f=e.ref;if("ref"in l){var v={};for(var T in l)T!=="ref"&&(v[T]=l[T])}else v=l;return fr(e),l=yc(t,e,a,v,f,u),T=vc(),t!==null&&!Be?(bc(t,e,u),da(t,e,u)):(Gt&&T&&tc(e),e.flags|=1,Pe(t,e,l,u),e.child)}function bh(t,e,a,l,u){if(t===null){var f=a.type;return typeof f=="function"&&!Wu(f)&&f.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=f,Sh(t,e,f,l,u)):(t=Ni(a.type,null,l,e,e.mode,u),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!qc(t,u)){var v=f.memoizedProps;if(a=a.compare,a=a!==null?a:Ko,a(v,l)&&t.ref===e.ref)return da(t,e,u)}return e.flags|=1,t=oa(f,l),t.ref=e.ref,t.return=e,e.child=t}function Sh(t,e,a,l,u){if(t!==null){var f=t.memoizedProps;if(Ko(f,l)&&t.ref===e.ref)if(Be=!1,e.pendingProps=l=f,qc(t,u))(t.flags&131072)!==0&&(Be=!0);else return e.lanes=t.lanes,da(t,e,u)}return kc(t,e,a,l,u)}function xh(t,e,a){var l=e.pendingProps,u=l.children,f=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=f!==null?f.baseLanes|a:a,t!==null){for(u=e.child=t.child,f=0;u!==null;)f=f|u.lanes|u.childLanes,u=u.sibling;e.childLanes=f&~l}else e.childLanes=0,e.child=null;return Ch(t,e,l,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&$i(e,f!==null?f.cachePool:null),f!==null?Sm(e,f):mc(),sh(e);else return e.lanes=e.childLanes=536870912,Ch(t,e,f!==null?f.baseLanes|a:a,a)}else f!==null?($i(e,f.cachePool),Sm(e,f),Na(),e.memoizedState=null):(t!==null&&$i(e,null),mc(),Na());return Pe(t,e,u,a),e.child}function Ch(t,e,a,l){var u=sc();return u=u===null?null:{parent:Ne._currentValue,pool:u},e.memoizedState={baseLanes:a,cachePool:u},t!==null&&$i(e,null),mc(),sh(e),t!==null&&tl(t,e,l,!0),null}function ts(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(i(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function kc(t,e,a,l,u){return fr(e),a=yc(t,e,a,l,void 0,u),l=vc(),t!==null&&!Be?(bc(t,e,u),da(t,e,u)):(Gt&&l&&tc(e),e.flags|=1,Pe(t,e,a,u),e.child)}function Eh(t,e,a,l,u,f){return fr(e),e.updateQueue=null,a=Cm(e,l,a,u),xm(t),l=vc(),t!==null&&!Be?(bc(t,e,f),da(t,e,f)):(Gt&&l&&tc(e),e.flags|=1,Pe(t,e,a,f),e.child)}function Th(t,e,a,l,u){if(fr(e),e.stateNode===null){var f=Xr,v=a.contextType;typeof v=="object"&&v!==null&&(f=Xe(v)),f=new a(l,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=zc,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=l,f.state=e.memoizedState,f.refs={},cc(e),v=a.contextType,f.context=typeof v=="object"&&v!==null?Xe(v):Xr,f.state=e.memoizedState,v=a.getDerivedStateFromProps,typeof v=="function"&&(Nc(e,a,v,l),f.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(v=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),v!==f.state&&zc.enqueueReplaceState(f,f.state,null),il(e,l,f,u),ll(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){f=e.stateNode;var T=e.memoizedProps,z=mr(a,T);f.props=z;var P=f.context,J=a.contextType;v=Xr,typeof J=="object"&&J!==null&&(v=Xe(J));var rt=a.getDerivedStateFromProps;J=typeof rt=="function"||typeof f.getSnapshotBeforeUpdate=="function",T=e.pendingProps!==T,J||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(T||P!==v)&&ch(e,f,l,v),Ma=!1;var q=e.memoizedState;f.state=q,il(e,l,f,u),ll(),P=e.memoizedState,T||q!==P||Ma?(typeof rt=="function"&&(Nc(e,a,rt,l),P=e.memoizedState),(z=Ma||uh(e,a,z,l,q,P,v))?(J||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=P),f.props=l,f.state=P,f.context=v,l=z):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{f=e.stateNode,fc(t,e),v=e.memoizedProps,J=mr(a,v),f.props=J,rt=e.pendingProps,q=f.context,P=a.contextType,z=Xr,typeof P=="object"&&P!==null&&(z=Xe(P)),T=a.getDerivedStateFromProps,(P=typeof T=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(v!==rt||q!==z)&&ch(e,f,l,z),Ma=!1,q=e.memoizedState,f.state=q,il(e,l,f,u),ll();var V=e.memoizedState;v!==rt||q!==V||Ma||t!==null&&t.dependencies!==null&&ki(t.dependencies)?(typeof T=="function"&&(Nc(e,a,T,l),V=e.memoizedState),(J=Ma||uh(e,a,J,l,q,V,z)||t!==null&&t.dependencies!==null&&ki(t.dependencies))?(P||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(l,V,z),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(l,V,z)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||v===t.memoizedProps&&q===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||v===t.memoizedProps&&q===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=V),f.props=l,f.state=V,f.context=z,l=J):(typeof f.componentDidUpdate!="function"||v===t.memoizedProps&&q===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||v===t.memoizedProps&&q===t.memoizedState||(e.flags|=1024),l=!1)}return f=l,ts(t,e),l=(e.flags&128)!==0,f||l?(f=e.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&l?(e.child=ro(e,t.child,null,u),e.child=ro(e,null,a,u)):Pe(t,e,a,u),e.memoizedState=f.state,t=e.child):t=da(t,e,u),t}function Rh(t,e,a,l){return Zo(),e.flags|=256,Pe(t,e,a,l),e.child}var Bc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function $c(t){return{baseLanes:t,cachePool:dm()}}function Lc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=On),t}function wh(t,e,a){var l=e.pendingProps,u=!1,f=(e.flags&128)!==0,v;if((v=f)||(v=t!==null&&t.memoizedState===null?!1:(ze.current&2)!==0),v&&(u=!0,e.flags&=-129),v=(e.flags&32)!==0,e.flags&=-33,t===null){if(Gt){if(u?ja(e):Na(),Gt){var T=ye,z;if(z=T){t:{for(z=T,T=Pn;z.nodeType!==8;){if(!T){T=null;break t}if(z=Ln(z.nextSibling),z===null){T=null;break t}}T=z}T!==null?(e.memoizedState={dehydrated:T,treeContext:lr!==null?{id:la,overflow:ia}:null,retryLane:536870912,hydrationErrors:null},z=fn(18,null,null,0),z.stateNode=T,z.return=e,e.child=z,Ze=e,ye=null,z=!0):z=!1}z||ur(e)}if(T=e.memoizedState,T!==null&&(T=T.dehydrated,T!==null))return Cf(T)?e.lanes=32:e.lanes=536870912,null;fa(e)}return T=l.children,l=l.fallback,u?(Na(),u=e.mode,T=es({mode:"hidden",children:T},u),l=or(l,u,a,null),T.return=e,l.return=e,T.sibling=l,e.child=T,u=e.child,u.memoizedState=$c(a),u.childLanes=Lc(t,v,a),e.memoizedState=Bc,l):(ja(e),Uc(e,T))}if(z=t.memoizedState,z!==null&&(T=z.dehydrated,T!==null)){if(f)e.flags&256?(ja(e),e.flags&=-257,e=Hc(t,e,a)):e.memoizedState!==null?(Na(),e.child=t.child,e.flags|=128,e=null):(Na(),u=l.fallback,T=e.mode,l=es({mode:"visible",children:l.children},T),u=or(u,T,a,null),u.flags|=2,l.return=e,u.return=e,l.sibling=u,e.child=l,ro(e,t.child,null,a),l=e.child,l.memoizedState=$c(a),l.childLanes=Lc(t,v,a),e.memoizedState=Bc,e=u);else if(ja(e),Cf(T)){if(v=T.nextSibling&&T.nextSibling.dataset,v)var P=v.dgst;v=P,l=Error(i(419)),l.stack="",l.digest=v,Jo({value:l,source:null,stack:null}),e=Hc(t,e,a)}else if(Be||tl(t,e,a,!1),v=(a&t.childLanes)!==0,Be||v){if(v=le,v!==null&&(l=a&-a,l=(l&42)!==0?1:Cu(l),l=(l&(v.suspendedLanes|a))!==0?0:l,l!==0&&l!==z.retryLane))throw z.retryLane=l,Fr(t,l),gn(v,t,l),yh;T.data==="$?"||rf(),e=Hc(t,e,a)}else T.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=z.treeContext,ye=Ln(T.nextSibling),Ze=e,Gt=!0,sr=null,Pn=!1,t!==null&&(wn[Mn++]=la,wn[Mn++]=ia,wn[Mn++]=lr,la=t.id,ia=t.overflow,lr=e),e=Uc(e,l.children),e.flags|=4096);return e}return u?(Na(),u=l.fallback,T=e.mode,z=t.child,P=z.sibling,l=oa(z,{mode:"hidden",children:l.children}),l.subtreeFlags=z.subtreeFlags&65011712,P!==null?u=oa(P,u):(u=or(u,T,a,null),u.flags|=2),u.return=e,l.return=e,l.sibling=u,e.child=l,l=u,u=e.child,T=t.child.memoizedState,T===null?T=$c(a):(z=T.cachePool,z!==null?(P=Ne._currentValue,z=z.parent!==P?{parent:P,pool:P}:z):z=dm(),T={baseLanes:T.baseLanes|a,cachePool:z}),u.memoizedState=T,u.childLanes=Lc(t,v,a),e.memoizedState=Bc,l):(ja(e),a=t.child,t=a.sibling,a=oa(a,{mode:"visible",children:l.children}),a.return=e,a.sibling=null,t!==null&&(v=e.deletions,v===null?(e.deletions=[t],e.flags|=16):v.push(t)),e.child=a,e.memoizedState=null,a)}function Uc(t,e){return e=es({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function es(t,e){return t=fn(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Hc(t,e,a){return ro(e,t.child,null,a),t=Uc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Mh(t,e,a){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),rc(t.return,e,a)}function Pc(t,e,a,l,u){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:u}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=l,f.tail=a,f.tailMode=u)}function Ah(t,e,a){var l=e.pendingProps,u=l.revealOrder,f=l.tail;if(Pe(t,e,l.children,a),l=ze.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Mh(t,a,e);else if(t.tag===19)Mh(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(tt(ze,l),u){case"forwards":for(a=e.child,u=null;a!==null;)t=a.alternate,t!==null&&Wi(t)===null&&(u=a),a=a.sibling;a=u,a===null?(u=e.child,e.child=null):(u=a.sibling,a.sibling=null),Pc(e,!1,u,a,f);break;case"backwards":for(a=null,u=e.child,e.child=null;u!==null;){if(t=u.alternate,t!==null&&Wi(t)===null){e.child=u;break}t=u.sibling,u.sibling=a,a=u,u=t}Pc(e,!0,a,null,f);break;case"together":Pc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function da(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),$a|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(tl(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(i(153));if(e.child!==null){for(t=e.child,a=oa(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=oa(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function qc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&ki(t)))}function A1(t,e,a){switch(e.tag){case 3:wt(e,e.stateNode.containerInfo),wa(e,Ne,t.memoizedState.cache),Zo();break;case 27:case 5:gt(e);break;case 4:wt(e,e.stateNode.containerInfo);break;case 10:wa(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(ja(e),e.flags|=128,null):(a&e.child.childLanes)!==0?wh(t,e,a):(ja(e),t=da(t,e,a),t!==null?t.sibling:null);ja(e);break;case 19:var u=(t.flags&128)!==0;if(l=(a&e.childLanes)!==0,l||(tl(t,e,a,!1),l=(a&e.childLanes)!==0),u){if(l)return Ah(t,e,a);e.flags|=128}if(u=e.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),tt(ze,ze.current),l)break;return null;case 22:case 23:return e.lanes=0,xh(t,e,a);case 24:wa(e,Ne,t.memoizedState.cache)}return da(t,e,a)}function Oh(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Be=!0;else{if(!qc(t,a)&&(e.flags&128)===0)return Be=!1,A1(t,e,a);Be=(t.flags&131072)!==0}else Be=!1,Gt&&(e.flags&1048576)!==0&&om(e,Di,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,u=l._init;if(l=u(l._payload),e.type=l,typeof l=="function")Wu(l)?(t=mr(l,t),e.tag=1,e=Th(null,e,l,t,a)):(e.tag=0,e=kc(null,e,l,t,a));else{if(l!=null){if(u=l.$$typeof,u===O){e.tag=11,e=vh(null,e,l,t,a);break t}else if(u===Y){e.tag=14,e=bh(null,e,l,t,a);break t}}throw e=it(l)||l,Error(i(306,e,""))}}return e;case 0:return kc(t,e,e.type,e.pendingProps,a);case 1:return l=e.type,u=mr(l,e.pendingProps),Th(t,e,l,u,a);case 3:t:{if(wt(e,e.stateNode.containerInfo),t===null)throw Error(i(387));l=e.pendingProps;var f=e.memoizedState;u=f.element,fc(t,e),il(e,l,null,a);var v=e.memoizedState;if(l=v.cache,wa(e,Ne,l),l!==f.cache&&oc(e,[Ne],a,!0),ll(),l=v.element,f.isDehydrated)if(f={element:l,isDehydrated:!1,cache:v.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=Rh(t,e,l,a);break t}else if(l!==u){u=Tn(Error(i(424)),e),Jo(u),e=Rh(t,e,l,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(ye=Ln(t.firstChild),Ze=e,Gt=!0,sr=null,Pn=!0,a=ih(e,null,l,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Zo(),l===u){e=da(t,e,a);break t}Pe(t,e,l,a)}e=e.child}return e;case 26:return ts(t,e),t===null?(a=zg(e.type,null,e.pendingProps,null))?e.memoizedState=a:Gt||(a=e.type,t=e.pendingProps,l=hs(ct.current).createElement(a),l[Fe]=e,l[nn]=t,Ge(l,a,t),ke(l),e.stateNode=l):e.memoizedState=zg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return gt(e),t===null&&Gt&&(l=e.stateNode=_g(e.type,e.pendingProps,ct.current),Ze=e,Pn=!0,u=ye,Pa(e.type)?(Ef=u,ye=Ln(l.firstChild)):ye=u),Pe(t,e,e.pendingProps.children,a),ts(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Gt&&((u=l=ye)&&(l=nS(l,e.type,e.pendingProps,Pn),l!==null?(e.stateNode=l,Ze=e,ye=Ln(l.firstChild),Pn=!1,u=!0):u=!1),u||ur(e)),gt(e),u=e.type,f=e.pendingProps,v=t!==null?t.memoizedProps:null,l=f.children,bf(u,f)?l=null:v!==null&&bf(u,v)&&(e.flags|=32),e.memoizedState!==null&&(u=yc(t,e,S1,null,null,a),Ol._currentValue=u),ts(t,e),Pe(t,e,l,a),e.child;case 6:return t===null&&Gt&&((t=a=ye)&&(a=aS(a,e.pendingProps,Pn),a!==null?(e.stateNode=a,Ze=e,ye=null,t=!0):t=!1),t||ur(e)),null;case 13:return wh(t,e,a);case 4:return wt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=ro(e,null,l,a):Pe(t,e,l,a),e.child;case 11:return vh(t,e,e.type,e.pendingProps,a);case 7:return Pe(t,e,e.pendingProps,a),e.child;case 8:return Pe(t,e,e.pendingProps.children,a),e.child;case 12:return Pe(t,e,e.pendingProps.children,a),e.child;case 10:return l=e.pendingProps,wa(e,e.type,l.value),Pe(t,e,l.children,a),e.child;case 9:return u=e.type._context,l=e.pendingProps.children,fr(e),u=Xe(u),l=l(u),e.flags|=1,Pe(t,e,l,a),e.child;case 14:return bh(t,e,e.type,e.pendingProps,a);case 15:return Sh(t,e,e.type,e.pendingProps,a);case 19:return Ah(t,e,a);case 31:return l=e.pendingProps,a=e.mode,l={mode:l.mode,children:l.children},t===null?(a=es(l,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=oa(t.child,l),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return xh(t,e,a);case 24:return fr(e),l=Xe(Ne),t===null?(u=sc(),u===null&&(u=le,f=lc(),u.pooledCache=f,f.refCount++,f!==null&&(u.pooledCacheLanes|=a),u=f),e.memoizedState={parent:l,cache:u},cc(e),wa(e,Ne,u)):((t.lanes&a)!==0&&(fc(t,e),il(e,null,null,a),ll()),u=t.memoizedState,f=e.memoizedState,u.parent!==l?(u={parent:l,cache:l},e.memoizedState=u,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=u),wa(e,Ne,l)):(l=f.cache,wa(e,Ne,l),l!==u.cache&&oc(e,[Ne],a,!0))),Pe(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(i(156,e.tag))}function pa(t){t.flags|=4}function _h(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Lg(e)){if(e=An.current,e!==null&&((Ht&4194048)===Ht?qn!==null:(Ht&62914560)!==Ht&&(Ht&536870912)===0||e!==qn))throw rl=uc,pm;t.flags|=8192}}function ns(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?na():536870912,t.lanes|=e,so|=e)}function ml(t,e){if(!Gt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function me(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,l=0;if(e)for(var u=t.child;u!==null;)a|=u.lanes|u.childLanes,l|=u.subtreeFlags&65011712,l|=u.flags&65011712,u.return=t,u=u.sibling;else for(u=t.child;u!==null;)a|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=t,u=u.sibling;return t.subtreeFlags|=l,t.childLanes=a,e}function O1(t,e,a){var l=e.pendingProps;switch(ec(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(e),null;case 1:return me(e),null;case 3:return a=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),ua(Ne),zt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(Wo(e)?pa(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,sm())),me(e),null;case 26:return a=e.memoizedState,t===null?(pa(e),a!==null?(me(e),_h(e,a)):(me(e),e.flags&=-16777217)):a?a!==t.memoizedState?(pa(e),me(e),_h(e,a)):(me(e),e.flags&=-16777217):(t.memoizedProps!==l&&pa(e),me(e),e.flags&=-16777217),null;case 27:_t(e),a=ct.current;var u=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&pa(e);else{if(!l){if(e.stateNode===null)throw Error(i(166));return me(e),null}t=ut.current,Wo(e)?lm(e):(t=_g(u,l,a),e.stateNode=t,pa(e))}return me(e),null;case 5:if(_t(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&pa(e);else{if(!l){if(e.stateNode===null)throw Error(i(166));return me(e),null}if(t=ut.current,Wo(e))lm(e);else{switch(u=hs(ct.current),t){case 1:t=u.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=u.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=u.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=u.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=u.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?u.createElement(a,{is:l.is}):u.createElement(a)}}t[Fe]=e,t[nn]=l;t:for(u=e.child;u!==null;){if(u.tag===5||u.tag===6)t.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===e)break t;for(;u.sibling===null;){if(u.return===null||u.return===e)break t;u=u.return}u.sibling.return=u.return,u=u.sibling}e.stateNode=t;t:switch(Ge(t,a,l),a){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&pa(e)}}return me(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&pa(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(i(166));if(t=ct.current,Wo(e)){if(t=e.stateNode,a=e.memoizedProps,l=null,u=Ze,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}t[Fe]=e,t=!!(t.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Eg(t.nodeValue,a)),t||ur(e)}else t=hs(t).createTextNode(l),t[Fe]=e,e.stateNode=t}return me(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(u=Wo(e),l!==null&&l.dehydrated!==null){if(t===null){if(!u)throw Error(i(318));if(u=e.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(i(317));u[Fe]=e}else Zo(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;me(e),u=!1}else u=sm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=u),u=!0;if(!u)return e.flags&256?(fa(e),e):(fa(e),null)}if(fa(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=l!==null,t=t!==null&&t.memoizedState!==null,a){l=e.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var f=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(f=l.memoizedState.cachePool.pool),f!==u&&(l.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),ns(e,e.updateQueue),me(e),null;case 4:return zt(),t===null&&mf(e.stateNode.containerInfo),me(e),null;case 10:return ua(e.type),me(e),null;case 19:if(et(ze),u=e.memoizedState,u===null)return me(e),null;if(l=(e.flags&128)!==0,f=u.rendering,f===null)if(l)ml(u,!1);else{if(ve!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=Wi(t),f!==null){for(e.flags|=128,ml(u,!1),t=f.updateQueue,e.updateQueue=t,ns(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)rm(a,t),a=a.sibling;return tt(ze,ze.current&1|2),e.child}t=t.sibling}u.tail!==null&&Kt()>os&&(e.flags|=128,l=!0,ml(u,!1),e.lanes=4194304)}else{if(!l)if(t=Wi(f),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,ns(e,t),ml(u,!0),u.tail===null&&u.tailMode==="hidden"&&!f.alternate&&!Gt)return me(e),null}else 2*Kt()-u.renderingStartTime>os&&a!==536870912&&(e.flags|=128,l=!0,ml(u,!1),e.lanes=4194304);u.isBackwards?(f.sibling=e.child,e.child=f):(t=u.last,t!==null?t.sibling=f:e.child=f,u.last=f)}return u.tail!==null?(e=u.tail,u.rendering=e,u.tail=e.sibling,u.renderingStartTime=Kt(),e.sibling=null,t=ze.current,tt(ze,l?t&1|2:t&1),e):(me(e),null);case 22:case 23:return fa(e),hc(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(a&536870912)!==0&&(e.flags&128)===0&&(me(e),e.subtreeFlags&6&&(e.flags|=8192)):me(e),a=e.updateQueue,a!==null&&ns(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==a&&(e.flags|=2048),t!==null&&et(dr),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ua(Ne),me(e),null;case 25:return null;case 30:return null}throw Error(i(156,e.tag))}function _1(t,e){switch(ec(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ua(Ne),zt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return _t(e),null;case 13:if(fa(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(i(340));Zo()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return et(ze),null;case 4:return zt(),null;case 10:return ua(e.type),null;case 22:case 23:return fa(e),hc(),t!==null&&et(dr),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ua(Ne),null;case 25:return null;default:return null}}function jh(t,e){switch(ec(e),e.tag){case 3:ua(Ne),zt();break;case 26:case 27:case 5:_t(e);break;case 4:zt();break;case 13:fa(e);break;case 19:et(ze);break;case 10:ua(e.type);break;case 22:case 23:fa(e),hc(),t!==null&&et(dr);break;case 24:ua(Ne)}}function hl(t,e){try{var a=e.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var u=l.next;a=u;do{if((a.tag&t)===t){l=void 0;var f=a.create,v=a.inst;l=f(),v.destroy=l}a=a.next}while(a!==u)}}catch(T){ae(e,e.return,T)}}function za(t,e,a){try{var l=e.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var f=u.next;l=f;do{if((l.tag&t)===t){var v=l.inst,T=v.destroy;if(T!==void 0){v.destroy=void 0,u=e;var z=a,P=T;try{P()}catch(J){ae(u,z,J)}}}l=l.next}while(l!==f)}}catch(J){ae(e,e.return,J)}}function Nh(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{bm(e,a)}catch(l){ae(t,t.return,l)}}}function zh(t,e,a){a.props=mr(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(l){ae(t,e,l)}}function gl(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof a=="function"?t.refCleanup=a(l):a.current=l}}catch(u){ae(t,e,u)}}function Gn(t,e){var a=t.ref,l=t.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(u){ae(t,e,u)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(u){ae(t,e,u)}else a.current=null}function Dh(t){var e=t.type,a=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break t;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(u){ae(t,t.return,u)}}function Gc(t,e,a){try{var l=t.stateNode;W1(l,t.type,a,e),l[nn]=e}catch(u){ae(t,t.return,u)}}function kh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Pa(t.type)||t.tag===4}function Ic(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||kh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Pa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Vc(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=ms));else if(l!==4&&(l===27&&Pa(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(Vc(t,e,a),t=t.sibling;t!==null;)Vc(t,e,a),t=t.sibling}function as(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(l!==4&&(l===27&&Pa(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(as(t,e,a),t=t.sibling;t!==null;)as(t,e,a),t=t.sibling}function Bh(t){var e=t.stateNode,a=t.memoizedProps;try{for(var l=t.type,u=e.attributes;u.length;)e.removeAttributeNode(u[0]);Ge(e,l,a),e[Fe]=t,e[nn]=a}catch(f){ae(t,t.return,f)}}var ma=!1,Ce=!1,Yc=!1,$h=typeof WeakSet=="function"?WeakSet:Set,$e=null;function j1(t,e){if(t=t.containerInfo,yf=xs,t=Xp(t),Iu(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var u=l.anchorOffset,f=l.focusNode;l=l.focusOffset;try{a.nodeType,f.nodeType}catch{a=null;break t}var v=0,T=-1,z=-1,P=0,J=0,rt=t,q=null;e:for(;;){for(var V;rt!==a||u!==0&&rt.nodeType!==3||(T=v+u),rt!==f||l!==0&&rt.nodeType!==3||(z=v+l),rt.nodeType===3&&(v+=rt.nodeValue.length),(V=rt.firstChild)!==null;)q=rt,rt=V;for(;;){if(rt===t)break e;if(q===a&&++P===u&&(T=v),q===f&&++J===l&&(z=v),(V=rt.nextSibling)!==null)break;rt=q,q=rt.parentNode}rt=V}a=T===-1||z===-1?null:{start:T,end:z}}else a=null}a=a||{start:0,end:0}}else a=null;for(vf={focusedElem:t,selectionRange:a},xs=!1,$e=e;$e!==null;)if(e=$e,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,$e=t;else for(;$e!==null;){switch(e=$e,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,a=e,u=f.memoizedProps,f=f.memoizedState,l=a.stateNode;try{var Tt=mr(a.type,u,a.elementType===a.type);t=l.getSnapshotBeforeUpdate(Tt,f),l.__reactInternalSnapshotBeforeUpdate=t}catch(St){ae(a,a.return,St)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)xf(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":xf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(i(163))}if(t=e.sibling,t!==null){t.return=e.return,$e=t;break}$e=e.return}}function Lh(t,e,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Da(t,a),l&4&&hl(5,a);break;case 1:if(Da(t,a),l&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(v){ae(a,a.return,v)}else{var u=mr(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(u,e,t.__reactInternalSnapshotBeforeUpdate)}catch(v){ae(a,a.return,v)}}l&64&&Nh(a),l&512&&gl(a,a.return);break;case 3:if(Da(t,a),l&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{bm(t,e)}catch(v){ae(a,a.return,v)}}break;case 27:e===null&&l&4&&Bh(a);case 26:case 5:Da(t,a),e===null&&l&4&&Dh(a),l&512&&gl(a,a.return);break;case 12:Da(t,a);break;case 13:Da(t,a),l&4&&Ph(t,a),l&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=H1.bind(null,a),rS(t,a))));break;case 22:if(l=a.memoizedState!==null||ma,!l){e=e!==null&&e.memoizedState!==null||Ce,u=ma;var f=Ce;ma=l,(Ce=e)&&!f?ka(t,a,(a.subtreeFlags&8772)!==0):Da(t,a),ma=u,Ce=f}break;case 30:break;default:Da(t,a)}}function Uh(t){var e=t.alternate;e!==null&&(t.alternate=null,Uh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ru(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var ce=null,on=!1;function ha(t,e,a){for(a=a.child;a!==null;)Hh(t,e,a),a=a.sibling}function Hh(t,e,a){if(ge&&typeof ge.onCommitFiberUnmount=="function")try{ge.onCommitFiberUnmount(en,a)}catch{}switch(a.tag){case 26:Ce||Gn(a,e),ha(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ce||Gn(a,e);var l=ce,u=on;Pa(a.type)&&(ce=a.stateNode,on=!1),ha(t,e,a),Rl(a.stateNode),ce=l,on=u;break;case 5:Ce||Gn(a,e);case 6:if(l=ce,u=on,ce=null,ha(t,e,a),ce=l,on=u,ce!==null)if(on)try{(ce.nodeType===9?ce.body:ce.nodeName==="HTML"?ce.ownerDocument.body:ce).removeChild(a.stateNode)}catch(f){ae(a,e,f)}else try{ce.removeChild(a.stateNode)}catch(f){ae(a,e,f)}break;case 18:ce!==null&&(on?(t=ce,Ag(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),zl(t)):Ag(ce,a.stateNode));break;case 4:l=ce,u=on,ce=a.stateNode.containerInfo,on=!0,ha(t,e,a),ce=l,on=u;break;case 0:case 11:case 14:case 15:Ce||za(2,a,e),Ce||za(4,a,e),ha(t,e,a);break;case 1:Ce||(Gn(a,e),l=a.stateNode,typeof l.componentWillUnmount=="function"&&zh(a,e,l)),ha(t,e,a);break;case 21:ha(t,e,a);break;case 22:Ce=(l=Ce)||a.memoizedState!==null,ha(t,e,a),Ce=l;break;default:ha(t,e,a)}}function Ph(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{zl(t)}catch(a){ae(e,e.return,a)}}function N1(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new $h),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new $h),e;default:throw Error(i(435,t.tag))}}function Fc(t,e){var a=N1(t);e.forEach(function(l){var u=P1.bind(null,t,l);a.has(l)||(a.add(l),l.then(u,u))})}function dn(t,e){var a=e.deletions;if(a!==null)for(var l=0;l<a.length;l++){var u=a[l],f=t,v=e,T=v;t:for(;T!==null;){switch(T.tag){case 27:if(Pa(T.type)){ce=T.stateNode,on=!1;break t}break;case 5:ce=T.stateNode,on=!1;break t;case 3:case 4:ce=T.stateNode.containerInfo,on=!0;break t}T=T.return}if(ce===null)throw Error(i(160));Hh(f,v,u),ce=null,on=!1,f=u.alternate,f!==null&&(f.return=null),u.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)qh(e,t),e=e.sibling}var $n=null;function qh(t,e){var a=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:dn(e,t),pn(t),l&4&&(za(3,t,t.return),hl(3,t),za(5,t,t.return));break;case 1:dn(e,t),pn(t),l&512&&(Ce||a===null||Gn(a,a.return)),l&64&&ma&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var u=$n;if(dn(e,t),pn(t),l&512&&(Ce||a===null||Gn(a,a.return)),l&4){var f=a!==null?a.memoizedState:null;if(l=t.memoizedState,a===null)if(l===null)if(t.stateNode===null){t:{l=t.type,a=t.memoizedProps,u=u.ownerDocument||u;e:switch(l){case"title":f=u.getElementsByTagName("title")[0],(!f||f[Ho]||f[Fe]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=u.createElement(l),u.head.insertBefore(f,u.querySelector("head > title"))),Ge(f,l,a),f[Fe]=t,ke(f),l=f;break t;case"link":var v=Bg("link","href",u).get(l+(a.href||""));if(v){for(var T=0;T<v.length;T++)if(f=v[T],f.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&f.getAttribute("rel")===(a.rel==null?null:a.rel)&&f.getAttribute("title")===(a.title==null?null:a.title)&&f.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){v.splice(T,1);break e}}f=u.createElement(l),Ge(f,l,a),u.head.appendChild(f);break;case"meta":if(v=Bg("meta","content",u).get(l+(a.content||""))){for(T=0;T<v.length;T++)if(f=v[T],f.getAttribute("content")===(a.content==null?null:""+a.content)&&f.getAttribute("name")===(a.name==null?null:a.name)&&f.getAttribute("property")===(a.property==null?null:a.property)&&f.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&f.getAttribute("charset")===(a.charSet==null?null:a.charSet)){v.splice(T,1);break e}}f=u.createElement(l),Ge(f,l,a),u.head.appendChild(f);break;default:throw Error(i(468,l))}f[Fe]=t,ke(f),l=f}t.stateNode=l}else $g(u,t.type,t.stateNode);else t.stateNode=kg(u,l,t.memoizedProps);else f!==l?(f===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):f.count--,l===null?$g(u,t.type,t.stateNode):kg(u,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Gc(t,t.memoizedProps,a.memoizedProps)}break;case 27:dn(e,t),pn(t),l&512&&(Ce||a===null||Gn(a,a.return)),a!==null&&l&4&&Gc(t,t.memoizedProps,a.memoizedProps);break;case 5:if(dn(e,t),pn(t),l&512&&(Ce||a===null||Gn(a,a.return)),t.flags&32){u=t.stateNode;try{Hr(u,"")}catch(V){ae(t,t.return,V)}}l&4&&t.stateNode!=null&&(u=t.memoizedProps,Gc(t,u,a!==null?a.memoizedProps:u)),l&1024&&(Yc=!0);break;case 6:if(dn(e,t),pn(t),l&4){if(t.stateNode===null)throw Error(i(162));l=t.memoizedProps,a=t.stateNode;try{a.nodeValue=l}catch(V){ae(t,t.return,V)}}break;case 3:if(vs=null,u=$n,$n=gs(e.containerInfo),dn(e,t),$n=u,pn(t),l&4&&a!==null&&a.memoizedState.isDehydrated)try{zl(e.containerInfo)}catch(V){ae(t,t.return,V)}Yc&&(Yc=!1,Gh(t));break;case 4:l=$n,$n=gs(t.stateNode.containerInfo),dn(e,t),pn(t),$n=l;break;case 12:dn(e,t),pn(t);break;case 13:dn(e,t),pn(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Jc=Kt()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Fc(t,l)));break;case 22:u=t.memoizedState!==null;var z=a!==null&&a.memoizedState!==null,P=ma,J=Ce;if(ma=P||u,Ce=J||z,dn(e,t),Ce=J,ma=P,pn(t),l&8192)t:for(e=t.stateNode,e._visibility=u?e._visibility&-2:e._visibility|1,u&&(a===null||z||ma||Ce||hr(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){z=a=e;try{if(f=z.stateNode,u)v=f.style,typeof v.setProperty=="function"?v.setProperty("display","none","important"):v.display="none";else{T=z.stateNode;var rt=z.memoizedProps.style,q=rt!=null&&rt.hasOwnProperty("display")?rt.display:null;T.style.display=q==null||typeof q=="boolean"?"":(""+q).trim()}}catch(V){ae(z,z.return,V)}}}else if(e.tag===6){if(a===null){z=e;try{z.stateNode.nodeValue=u?"":z.memoizedProps}catch(V){ae(z,z.return,V)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Fc(t,a))));break;case 19:dn(e,t),pn(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Fc(t,l)));break;case 30:break;case 21:break;default:dn(e,t),pn(t)}}function pn(t){var e=t.flags;if(e&2){try{for(var a,l=t.return;l!==null;){if(kh(l)){a=l;break}l=l.return}if(a==null)throw Error(i(160));switch(a.tag){case 27:var u=a.stateNode,f=Ic(t);as(t,f,u);break;case 5:var v=a.stateNode;a.flags&32&&(Hr(v,""),a.flags&=-33);var T=Ic(t);as(t,T,v);break;case 3:case 4:var z=a.stateNode.containerInfo,P=Ic(t);Vc(t,P,z);break;default:throw Error(i(161))}}catch(J){ae(t,t.return,J)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Gh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Gh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Da(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Lh(t,e.alternate,e),e=e.sibling}function hr(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:za(4,e,e.return),hr(e);break;case 1:Gn(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&zh(e,e.return,a),hr(e);break;case 27:Rl(e.stateNode);case 26:case 5:Gn(e,e.return),hr(e);break;case 22:e.memoizedState===null&&hr(e);break;case 30:hr(e);break;default:hr(e)}t=t.sibling}}function ka(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,u=t,f=e,v=f.flags;switch(f.tag){case 0:case 11:case 15:ka(u,f,a),hl(4,f);break;case 1:if(ka(u,f,a),l=f,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(P){ae(l,l.return,P)}if(l=f,u=l.updateQueue,u!==null){var T=l.stateNode;try{var z=u.shared.hiddenCallbacks;if(z!==null)for(u.shared.hiddenCallbacks=null,u=0;u<z.length;u++)vm(z[u],T)}catch(P){ae(l,l.return,P)}}a&&v&64&&Nh(f),gl(f,f.return);break;case 27:Bh(f);case 26:case 5:ka(u,f,a),a&&l===null&&v&4&&Dh(f),gl(f,f.return);break;case 12:ka(u,f,a);break;case 13:ka(u,f,a),a&&v&4&&Ph(u,f);break;case 22:f.memoizedState===null&&ka(u,f,a),gl(f,f.return);break;case 30:break;default:ka(u,f,a)}e=e.sibling}}function Xc(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&el(a))}function Kc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&el(t))}function In(t,e,a,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ih(t,e,a,l),e=e.sibling}function Ih(t,e,a,l){var u=e.flags;switch(e.tag){case 0:case 11:case 15:In(t,e,a,l),u&2048&&hl(9,e);break;case 1:In(t,e,a,l);break;case 3:In(t,e,a,l),u&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&el(t)));break;case 12:if(u&2048){In(t,e,a,l),t=e.stateNode;try{var f=e.memoizedProps,v=f.id,T=f.onPostCommit;typeof T=="function"&&T(v,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(z){ae(e,e.return,z)}}else In(t,e,a,l);break;case 13:In(t,e,a,l);break;case 23:break;case 22:f=e.stateNode,v=e.alternate,e.memoizedState!==null?f._visibility&2?In(t,e,a,l):yl(t,e):f._visibility&2?In(t,e,a,l):(f._visibility|=2,oo(t,e,a,l,(e.subtreeFlags&10256)!==0)),u&2048&&Xc(v,e);break;case 24:In(t,e,a,l),u&2048&&Kc(e.alternate,e);break;default:In(t,e,a,l)}}function oo(t,e,a,l,u){for(u=u&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,v=e,T=a,z=l,P=v.flags;switch(v.tag){case 0:case 11:case 15:oo(f,v,T,z,u),hl(8,v);break;case 23:break;case 22:var J=v.stateNode;v.memoizedState!==null?J._visibility&2?oo(f,v,T,z,u):yl(f,v):(J._visibility|=2,oo(f,v,T,z,u)),u&&P&2048&&Xc(v.alternate,v);break;case 24:oo(f,v,T,z,u),u&&P&2048&&Kc(v.alternate,v);break;default:oo(f,v,T,z,u)}e=e.sibling}}function yl(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,l=e,u=l.flags;switch(l.tag){case 22:yl(a,l),u&2048&&Xc(l.alternate,l);break;case 24:yl(a,l),u&2048&&Kc(l.alternate,l);break;default:yl(a,l)}e=e.sibling}}var vl=8192;function lo(t){if(t.subtreeFlags&vl)for(t=t.child;t!==null;)Vh(t),t=t.sibling}function Vh(t){switch(t.tag){case 26:lo(t),t.flags&vl&&t.memoizedState!==null&&yS($n,t.memoizedState,t.memoizedProps);break;case 5:lo(t);break;case 3:case 4:var e=$n;$n=gs(t.stateNode.containerInfo),lo(t),$n=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=vl,vl=16777216,lo(t),vl=e):lo(t));break;default:lo(t)}}function Yh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function bl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];$e=l,Xh(l,t)}Yh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Fh(t),t=t.sibling}function Fh(t){switch(t.tag){case 0:case 11:case 15:bl(t),t.flags&2048&&za(9,t,t.return);break;case 3:bl(t);break;case 12:bl(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,rs(t)):bl(t);break;default:bl(t)}}function rs(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];$e=l,Xh(l,t)}Yh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:za(8,e,e.return),rs(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,rs(e));break;default:rs(e)}t=t.sibling}}function Xh(t,e){for(;$e!==null;){var a=$e;switch(a.tag){case 0:case 11:case 15:za(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:el(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,$e=l;else t:for(a=t;$e!==null;){l=$e;var u=l.sibling,f=l.return;if(Uh(l),l===a){$e=null;break t}if(u!==null){u.return=f,$e=u;break t}$e=f}}}var z1={getCacheForType:function(t){var e=Xe(Ne),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},D1=typeof WeakMap=="function"?WeakMap:Map,Qt=0,le=null,Lt=null,Ht=0,Wt=0,mn=null,Ba=!1,io=!1,Qc=!1,ga=0,ve=0,$a=0,gr=0,Wc=0,On=0,so=0,Sl=null,ln=null,Zc=!1,Jc=0,os=1/0,ls=null,La=null,qe=0,Ua=null,uo=null,co=0,tf=0,ef=null,Kh=null,xl=0,nf=null;function hn(){if((Qt&2)!==0&&Ht!==0)return Ht&-Ht;if(k.T!==null){var t=Wr;return t!==0?t:cf()}return fp()}function Qh(){On===0&&(On=(Ht&536870912)===0||Gt?Re():536870912);var t=An.current;return t!==null&&(t.flags|=32),On}function gn(t,e,a){(t===le&&(Wt===2||Wt===9)||t.cancelPendingCommit!==null)&&(fo(t,0),Ha(t,Ht,On,!1)),Uo(t,a),((Qt&2)===0||t!==le)&&(t===le&&((Qt&2)===0&&(gr|=a),ve===4&&Ha(t,Ht,On,!1)),Vn(t))}function Wh(t,e,a){if((Qt&6)!==0)throw Error(i(327));var l=!a&&(e&124)===0&&(e&t.expiredLanes)===0||vt(t,e),u=l?$1(t,e):of(t,e,!0),f=l;do{if(u===0){io&&!l&&Ha(t,e,0,!1);break}else{if(a=t.current.alternate,f&&!k1(a)){u=of(t,e,!1),f=!1;continue}if(u===2){if(f=e,t.errorRecoveryDisabledLanes&f)var v=0;else v=t.pendingLanes&-536870913,v=v!==0?v:v&536870912?536870912:0;if(v!==0){e=v;t:{var T=t;u=Sl;var z=T.current.memoizedState.isDehydrated;if(z&&(fo(T,v).flags|=256),v=of(T,v,!1),v!==2){if(Qc&&!z){T.errorRecoveryDisabledLanes|=f,gr|=f,u=4;break t}f=ln,ln=u,f!==null&&(ln===null?ln=f:ln.push.apply(ln,f))}u=v}if(f=!1,u!==2)continue}}if(u===1){fo(t,0),Ha(t,e,0,!0);break}t:{switch(l=t,f=u,f){case 0:case 1:throw Error(i(345));case 4:if((e&4194048)!==e)break;case 6:Ha(l,e,On,!Ba);break t;case 2:ln=null;break;case 3:case 5:break;default:throw Error(i(329))}if((e&62914560)===e&&(u=Jc+300-Kt(),10<u)){if(Ha(l,e,On,!Ba),tr(l,0,!0)!==0)break t;l.timeoutHandle=wg(Zh.bind(null,l,a,ln,ls,Zc,e,On,gr,so,Ba,f,2,-0,0),u);break t}Zh(l,a,ln,ls,Zc,e,On,gr,so,Ba,f,0,-0,0)}}break}while(!0);Vn(t)}function Zh(t,e,a,l,u,f,v,T,z,P,J,rt,q,V){if(t.timeoutHandle=-1,rt=e.subtreeFlags,(rt&8192||(rt&16785408)===16785408)&&(Al={stylesheets:null,count:0,unsuspend:gS},Vh(e),rt=vS(),rt!==null)){t.cancelPendingCommit=rt(og.bind(null,t,e,f,a,l,u,v,T,z,J,1,q,V)),Ha(t,f,v,!P);return}og(t,e,f,a,l,u,v,T,z)}function k1(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var u=a[l],f=u.getSnapshot;u=u.value;try{if(!cn(f(),u))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Ha(t,e,a,l){e&=~Wc,e&=~gr,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var u=e;0<u;){var f=31-pe(u),v=1<<f;l[f]=-1,u&=~v}a!==0&&up(t,a,e)}function is(){return(Qt&6)===0?(Cl(0),!1):!0}function af(){if(Lt!==null){if(Wt===0)var t=Lt.return;else t=Lt,sa=cr=null,Sc(t),ao=null,dl=0,t=Lt;for(;t!==null;)jh(t.alternate,t),t=t.return;Lt=null}}function fo(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,J1(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),af(),le=t,Lt=a=oa(t.current,null),Ht=e,Wt=0,mn=null,Ba=!1,io=vt(t,e),Qc=!1,so=On=Wc=gr=$a=ve=0,ln=Sl=null,Zc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var u=31-pe(l),f=1<<u;e|=t[u],l&=~f}return ga=e,Oi(),a}function Jh(t,e){Dt=null,k.H=Xi,e===al||e===Li?(e=gm(),Wt=3):e===pm?(e=gm(),Wt=4):Wt=e===yh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,mn=e,Lt===null&&(ve=1,Ji(t,Tn(e,t.current)))}function tg(){var t=k.H;return k.H=Xi,t===null?Xi:t}function eg(){var t=k.A;return k.A=z1,t}function rf(){ve=4,Ba||(Ht&4194048)!==Ht&&An.current!==null||(io=!0),($a&134217727)===0&&(gr&134217727)===0||le===null||Ha(le,Ht,On,!1)}function of(t,e,a){var l=Qt;Qt|=2;var u=tg(),f=eg();(le!==t||Ht!==e)&&(ls=null,fo(t,e)),e=!1;var v=ve;t:do try{if(Wt!==0&&Lt!==null){var T=Lt,z=mn;switch(Wt){case 8:af(),v=6;break t;case 3:case 2:case 9:case 6:An.current===null&&(e=!0);var P=Wt;if(Wt=0,mn=null,po(t,T,z,P),a&&io){v=0;break t}break;default:P=Wt,Wt=0,mn=null,po(t,T,z,P)}}B1(),v=ve;break}catch(J){Jh(t,J)}while(!0);return e&&t.shellSuspendCounter++,sa=cr=null,Qt=l,k.H=u,k.A=f,Lt===null&&(le=null,Ht=0,Oi()),v}function B1(){for(;Lt!==null;)ng(Lt)}function $1(t,e){var a=Qt;Qt|=2;var l=tg(),u=eg();le!==t||Ht!==e?(ls=null,os=Kt()+500,fo(t,e)):io=vt(t,e);t:do try{if(Wt!==0&&Lt!==null){e=Lt;var f=mn;e:switch(Wt){case 1:Wt=0,mn=null,po(t,e,f,1);break;case 2:case 9:if(mm(f)){Wt=0,mn=null,ag(e);break}e=function(){Wt!==2&&Wt!==9||le!==t||(Wt=7),Vn(t)},f.then(e,e);break t;case 3:Wt=7;break t;case 4:Wt=5;break t;case 7:mm(f)?(Wt=0,mn=null,ag(e)):(Wt=0,mn=null,po(t,e,f,7));break;case 5:var v=null;switch(Lt.tag){case 26:v=Lt.memoizedState;case 5:case 27:var T=Lt;if(!v||Lg(v)){Wt=0,mn=null;var z=T.sibling;if(z!==null)Lt=z;else{var P=T.return;P!==null?(Lt=P,ss(P)):Lt=null}break e}}Wt=0,mn=null,po(t,e,f,5);break;case 6:Wt=0,mn=null,po(t,e,f,6);break;case 8:af(),ve=6;break t;default:throw Error(i(462))}}L1();break}catch(J){Jh(t,J)}while(!0);return sa=cr=null,k.H=l,k.A=u,Qt=a,Lt!==null?0:(le=null,Ht=0,Oi(),ve)}function L1(){for(;Lt!==null&&!Xt();)ng(Lt)}function ng(t){var e=Oh(t.alternate,t,ga);t.memoizedProps=t.pendingProps,e===null?ss(t):Lt=e}function ag(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=Eh(a,e,e.pendingProps,e.type,void 0,Ht);break;case 11:e=Eh(a,e,e.pendingProps,e.type.render,e.ref,Ht);break;case 5:Sc(e);default:jh(a,e),e=Lt=rm(e,ga),e=Oh(a,e,ga)}t.memoizedProps=t.pendingProps,e===null?ss(t):Lt=e}function po(t,e,a,l){sa=cr=null,Sc(e),ao=null,dl=0;var u=e.return;try{if(M1(t,u,e,a,Ht)){ve=1,Ji(t,Tn(a,t.current)),Lt=null;return}}catch(f){if(u!==null)throw Lt=u,f;ve=1,Ji(t,Tn(a,t.current)),Lt=null;return}e.flags&32768?(Gt||l===1?t=!0:io||(Ht&536870912)!==0?t=!1:(Ba=t=!0,(l===2||l===9||l===3||l===6)&&(l=An.current,l!==null&&l.tag===13&&(l.flags|=16384))),rg(e,t)):ss(e)}function ss(t){var e=t;do{if((e.flags&32768)!==0){rg(e,Ba);return}t=e.return;var a=O1(e.alternate,e,ga);if(a!==null){Lt=a;return}if(e=e.sibling,e!==null){Lt=e;return}Lt=e=t}while(e!==null);ve===0&&(ve=5)}function rg(t,e){do{var a=_1(t.alternate,t);if(a!==null){a.flags&=32767,Lt=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){Lt=t;return}Lt=t=a}while(t!==null);ve=6,Lt=null}function og(t,e,a,l,u,f,v,T,z){t.cancelPendingCommit=null;do us();while(qe!==0);if((Qt&6)!==0)throw Error(i(327));if(e!==null){if(e===t.current)throw Error(i(177));if(f=e.lanes|e.childLanes,f|=Ku,gb(t,a,f,v,T,z),t===le&&(Lt=le=null,Ht=0),uo=e,Ua=t,co=a,tf=f,ef=u,Kh=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,q1(Yt,function(){return cg(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=k.T,k.T=null,u=I.p,I.p=2,v=Qt,Qt|=4;try{j1(t,e,a)}finally{Qt=v,I.p=u,k.T=l}}qe=1,lg(),ig(),sg()}}function lg(){if(qe===1){qe=0;var t=Ua,e=uo,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=k.T,k.T=null;var l=I.p;I.p=2;var u=Qt;Qt|=4;try{qh(e,t);var f=vf,v=Xp(t.containerInfo),T=f.focusedElem,z=f.selectionRange;if(v!==T&&T&&T.ownerDocument&&Fp(T.ownerDocument.documentElement,T)){if(z!==null&&Iu(T)){var P=z.start,J=z.end;if(J===void 0&&(J=P),"selectionStart"in T)T.selectionStart=P,T.selectionEnd=Math.min(J,T.value.length);else{var rt=T.ownerDocument||document,q=rt&&rt.defaultView||window;if(q.getSelection){var V=q.getSelection(),Tt=T.textContent.length,St=Math.min(z.start,Tt),te=z.end===void 0?St:Math.min(z.end,Tt);!V.extend&&St>te&&(v=te,te=St,St=v);var $=Yp(T,St),B=Yp(T,te);if($&&B&&(V.rangeCount!==1||V.anchorNode!==$.node||V.anchorOffset!==$.offset||V.focusNode!==B.node||V.focusOffset!==B.offset)){var H=rt.createRange();H.setStart($.node,$.offset),V.removeAllRanges(),St>te?(V.addRange(H),V.extend(B.node,B.offset)):(H.setEnd(B.node,B.offset),V.addRange(H))}}}}for(rt=[],V=T;V=V.parentNode;)V.nodeType===1&&rt.push({element:V,left:V.scrollLeft,top:V.scrollTop});for(typeof T.focus=="function"&&T.focus(),T=0;T<rt.length;T++){var nt=rt[T];nt.element.scrollLeft=nt.left,nt.element.scrollTop=nt.top}}xs=!!yf,vf=yf=null}finally{Qt=u,I.p=l,k.T=a}}t.current=e,qe=2}}function ig(){if(qe===2){qe=0;var t=Ua,e=uo,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=k.T,k.T=null;var l=I.p;I.p=2;var u=Qt;Qt|=4;try{Lh(t,e.alternate,e)}finally{Qt=u,I.p=l,k.T=a}}qe=3}}function sg(){if(qe===4||qe===3){qe=0,He();var t=Ua,e=uo,a=co,l=Kh;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?qe=5:(qe=0,uo=Ua=null,ug(t,t.pendingLanes));var u=t.pendingLanes;if(u===0&&(La=null),Eu(a),e=e.stateNode,ge&&typeof ge.onCommitFiberRoot=="function")try{ge.onCommitFiberRoot(en,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=k.T,u=I.p,I.p=2,k.T=null;try{for(var f=t.onRecoverableError,v=0;v<l.length;v++){var T=l[v];f(T.value,{componentStack:T.stack})}}finally{k.T=e,I.p=u}}(co&3)!==0&&us(),Vn(t),u=t.pendingLanes,(a&4194090)!==0&&(u&42)!==0?t===nf?xl++:(xl=0,nf=t):xl=0,Cl(0)}}function ug(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,el(e)))}function us(t){return lg(),ig(),sg(),cg()}function cg(){if(qe!==5)return!1;var t=Ua,e=tf;tf=0;var a=Eu(co),l=k.T,u=I.p;try{I.p=32>a?32:a,k.T=null,a=ef,ef=null;var f=Ua,v=co;if(qe=0,uo=Ua=null,co=0,(Qt&6)!==0)throw Error(i(331));var T=Qt;if(Qt|=4,Fh(f.current),Ih(f,f.current,v,a),Qt=T,Cl(0,!1),ge&&typeof ge.onPostCommitFiberRoot=="function")try{ge.onPostCommitFiberRoot(en,f)}catch{}return!0}finally{I.p=u,k.T=l,ug(t,e)}}function fg(t,e,a){e=Tn(a,e),e=Dc(t.stateNode,e,2),t=Oa(t,e,2),t!==null&&(Uo(t,2),Vn(t))}function ae(t,e,a){if(t.tag===3)fg(t,t,a);else for(;e!==null;){if(e.tag===3){fg(e,t,a);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(La===null||!La.has(l))){t=Tn(a,t),a=hh(2),l=Oa(e,a,2),l!==null&&(gh(a,l,e,t),Uo(l,2),Vn(l));break}}e=e.return}}function lf(t,e,a){var l=t.pingCache;if(l===null){l=t.pingCache=new D1;var u=new Set;l.set(e,u)}else u=l.get(e),u===void 0&&(u=new Set,l.set(e,u));u.has(a)||(Qc=!0,u.add(a),t=U1.bind(null,t,e,a),e.then(t,t))}function U1(t,e,a){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,le===t&&(Ht&a)===a&&(ve===4||ve===3&&(Ht&62914560)===Ht&&300>Kt()-Jc?(Qt&2)===0&&fo(t,0):Wc|=a,so===Ht&&(so=0)),Vn(t)}function dg(t,e){e===0&&(e=na()),t=Fr(t,e),t!==null&&(Uo(t,e),Vn(t))}function H1(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),dg(t,a)}function P1(t,e){var a=0;switch(t.tag){case 13:var l=t.stateNode,u=t.memoizedState;u!==null&&(a=u.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(i(314))}l!==null&&l.delete(e),dg(t,a)}function q1(t,e){return qt(t,e)}var cs=null,mo=null,sf=!1,fs=!1,uf=!1,yr=0;function Vn(t){t!==mo&&t.next===null&&(mo===null?cs=mo=t:mo=mo.next=t),fs=!0,sf||(sf=!0,I1())}function Cl(t,e){if(!uf&&fs){uf=!0;do for(var a=!1,l=cs;l!==null;){if(t!==0){var u=l.pendingLanes;if(u===0)var f=0;else{var v=l.suspendedLanes,T=l.pingedLanes;f=(1<<31-pe(42|t)+1)-1,f&=u&~(v&~T),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(a=!0,gg(l,f))}else f=Ht,f=tr(l,l===le?f:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(f&3)===0||vt(l,f)||(a=!0,gg(l,f));l=l.next}while(a);uf=!1}}function G1(){pg()}function pg(){fs=sf=!1;var t=0;yr!==0&&(Z1()&&(t=yr),yr=0);for(var e=Kt(),a=null,l=cs;l!==null;){var u=l.next,f=mg(l,e);f===0?(l.next=null,a===null?cs=u:a.next=u,u===null&&(mo=a)):(a=l,(t!==0||(f&3)!==0)&&(fs=!0)),l=u}Cl(t)}function mg(t,e){for(var a=t.suspendedLanes,l=t.pingedLanes,u=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var v=31-pe(f),T=1<<v,z=u[v];z===-1?((T&a)===0||(T&l)!==0)&&(u[v]=ne(T,e)):z<=e&&(t.expiredLanes|=T),f&=~T}if(e=le,a=Ht,a=tr(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,a===0||t===e&&(Wt===2||Wt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&jt(l),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||vt(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(l!==null&&jt(l),Eu(a)){case 2:case 8:a=de;break;case 32:a=Yt;break;case 268435456:a=un;break;default:a=Yt}return l=hg.bind(null,t),a=qt(a,l),t.callbackPriority=e,t.callbackNode=a,e}return l!==null&&l!==null&&jt(l),t.callbackPriority=2,t.callbackNode=null,2}function hg(t,e){if(qe!==0&&qe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(us()&&t.callbackNode!==a)return null;var l=Ht;return l=tr(t,t===le?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(Wh(t,l,e),mg(t,Kt()),t.callbackNode!=null&&t.callbackNode===a?hg.bind(null,t):null)}function gg(t,e){if(us())return null;Wh(t,e,!0)}function I1(){tS(function(){(Qt&6)!==0?qt(ie,G1):pg()})}function cf(){return yr===0&&(yr=Re()),yr}function yg(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Ci(""+t)}function vg(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function V1(t,e,a,l,u){if(e==="submit"&&a&&a.stateNode===u){var f=yg((u[nn]||null).action),v=l.submitter;v&&(e=(e=v[nn]||null)?yg(e.formAction):v.getAttribute("formAction"),e!==null&&(f=e,v=null));var T=new wi("action","action",null,l,u);t.push({event:T,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(yr!==0){var z=v?vg(u,v):new FormData(u);Oc(a,{pending:!0,data:z,method:u.method,action:f},null,z)}}else typeof f=="function"&&(T.preventDefault(),z=v?vg(u,v):new FormData(u),Oc(a,{pending:!0,data:z,method:u.method,action:f},f,z))},currentTarget:u}]})}}for(var ff=0;ff<Xu.length;ff++){var df=Xu[ff],Y1=df.toLowerCase(),F1=df[0].toUpperCase()+df.slice(1);Bn(Y1,"on"+F1)}Bn(Wp,"onAnimationEnd"),Bn(Zp,"onAnimationIteration"),Bn(Jp,"onAnimationStart"),Bn("dblclick","onDoubleClick"),Bn("focusin","onFocus"),Bn("focusout","onBlur"),Bn(c1,"onTransitionRun"),Bn(f1,"onTransitionStart"),Bn(d1,"onTransitionCancel"),Bn(tm,"onTransitionEnd"),$r("onMouseEnter",["mouseout","mouseover"]),$r("onMouseLeave",["mouseout","mouseover"]),$r("onPointerEnter",["pointerout","pointerover"]),$r("onPointerLeave",["pointerout","pointerover"]),er("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),er("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),er("onBeforeInput",["compositionend","keypress","textInput","paste"]),er("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),er("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),er("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var El="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),X1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(El));function bg(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var l=t[a],u=l.event;l=l.listeners;t:{var f=void 0;if(e)for(var v=l.length-1;0<=v;v--){var T=l[v],z=T.instance,P=T.currentTarget;if(T=T.listener,z!==f&&u.isPropagationStopped())break t;f=T,u.currentTarget=P;try{f(u)}catch(J){Zi(J)}u.currentTarget=null,f=z}else for(v=0;v<l.length;v++){if(T=l[v],z=T.instance,P=T.currentTarget,T=T.listener,z!==f&&u.isPropagationStopped())break t;f=T,u.currentTarget=P;try{f(u)}catch(J){Zi(J)}u.currentTarget=null,f=z}}}}function Ut(t,e){var a=e[Tu];a===void 0&&(a=e[Tu]=new Set);var l=t+"__bubble";a.has(l)||(Sg(e,t,2,!1),a.add(l))}function pf(t,e,a){var l=0;e&&(l|=4),Sg(a,t,l,e)}var ds="_reactListening"+Math.random().toString(36).slice(2);function mf(t){if(!t[ds]){t[ds]=!0,pp.forEach(function(a){a!=="selectionchange"&&(X1.has(a)||pf(a,!1,t),pf(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[ds]||(e[ds]=!0,pf("selectionchange",!1,e))}}function Sg(t,e,a,l){switch(Ig(e)){case 2:var u=xS;break;case 8:u=CS;break;default:u=Af}a=u.bind(null,e,a,t),u=void 0,!ku||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(u=!0),l?u!==void 0?t.addEventListener(e,a,{capture:!0,passive:u}):t.addEventListener(e,a,!0):u!==void 0?t.addEventListener(e,a,{passive:u}):t.addEventListener(e,a,!1)}function hf(t,e,a,l,u){var f=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var v=l.tag;if(v===3||v===4){var T=l.stateNode.containerInfo;if(T===u)break;if(v===4)for(v=l.return;v!==null;){var z=v.tag;if((z===3||z===4)&&v.stateNode.containerInfo===u)return;v=v.return}for(;T!==null;){if(v=Dr(T),v===null)return;if(z=v.tag,z===5||z===6||z===26||z===27){l=f=v;continue t}T=T.parentNode}}l=l.return}Mp(function(){var P=f,J=zu(a),rt=[];t:{var q=em.get(t);if(q!==void 0){var V=wi,Tt=t;switch(t){case"keypress":if(Ti(a)===0)break t;case"keydown":case"keyup":V=qb;break;case"focusin":Tt="focus",V=Uu;break;case"focusout":Tt="blur",V=Uu;break;case"beforeblur":case"afterblur":V=Uu;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":V=_p;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":V=_b;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":V=Vb;break;case Wp:case Zp:case Jp:V=zb;break;case tm:V=Fb;break;case"scroll":case"scrollend":V=Ab;break;case"wheel":V=Kb;break;case"copy":case"cut":case"paste":V=kb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":V=Np;break;case"toggle":case"beforetoggle":V=Wb}var St=(e&4)!==0,te=!St&&(t==="scroll"||t==="scrollend"),$=St?q!==null?q+"Capture":null:q;St=[];for(var B=P,H;B!==null;){var nt=B;if(H=nt.stateNode,nt=nt.tag,nt!==5&&nt!==26&&nt!==27||H===null||$===null||(nt=qo(B,$),nt!=null&&St.push(Tl(B,nt,H))),te)break;B=B.return}0<St.length&&(q=new V(q,Tt,null,a,J),rt.push({event:q,listeners:St}))}}if((e&7)===0){t:{if(q=t==="mouseover"||t==="pointerover",V=t==="mouseout"||t==="pointerout",q&&a!==Nu&&(Tt=a.relatedTarget||a.fromElement)&&(Dr(Tt)||Tt[zr]))break t;if((V||q)&&(q=J.window===J?J:(q=J.ownerDocument)?q.defaultView||q.parentWindow:window,V?(Tt=a.relatedTarget||a.toElement,V=P,Tt=Tt?Dr(Tt):null,Tt!==null&&(te=c(Tt),St=Tt.tag,Tt!==te||St!==5&&St!==27&&St!==6)&&(Tt=null)):(V=null,Tt=P),V!==Tt)){if(St=_p,nt="onMouseLeave",$="onMouseEnter",B="mouse",(t==="pointerout"||t==="pointerover")&&(St=Np,nt="onPointerLeave",$="onPointerEnter",B="pointer"),te=V==null?q:Po(V),H=Tt==null?q:Po(Tt),q=new St(nt,B+"leave",V,a,J),q.target=te,q.relatedTarget=H,nt=null,Dr(J)===P&&(St=new St($,B+"enter",Tt,a,J),St.target=H,St.relatedTarget=te,nt=St),te=nt,V&&Tt)e:{for(St=V,$=Tt,B=0,H=St;H;H=ho(H))B++;for(H=0,nt=$;nt;nt=ho(nt))H++;for(;0<B-H;)St=ho(St),B--;for(;0<H-B;)$=ho($),H--;for(;B--;){if(St===$||$!==null&&St===$.alternate)break e;St=ho(St),$=ho($)}St=null}else St=null;V!==null&&xg(rt,q,V,St,!1),Tt!==null&&te!==null&&xg(rt,te,Tt,St,!0)}}t:{if(q=P?Po(P):window,V=q.nodeName&&q.nodeName.toLowerCase(),V==="select"||V==="input"&&q.type==="file")var pt=Hp;else if(Lp(q))if(Pp)pt=i1;else{pt=o1;var Bt=r1}else V=q.nodeName,!V||V.toLowerCase()!=="input"||q.type!=="checkbox"&&q.type!=="radio"?P&&ju(P.elementType)&&(pt=Hp):pt=l1;if(pt&&(pt=pt(t,P))){Up(rt,pt,a,J);break t}Bt&&Bt(t,q,P),t==="focusout"&&P&&q.type==="number"&&P.memoizedProps.value!=null&&_u(q,"number",q.value)}switch(Bt=P?Po(P):window,t){case"focusin":(Lp(Bt)||Bt.contentEditable==="true")&&(Ir=Bt,Vu=P,Qo=null);break;case"focusout":Qo=Vu=Ir=null;break;case"mousedown":Yu=!0;break;case"contextmenu":case"mouseup":case"dragend":Yu=!1,Kp(rt,a,J);break;case"selectionchange":if(u1)break;case"keydown":case"keyup":Kp(rt,a,J)}var yt;if(Pu)t:{switch(t){case"compositionstart":var Ct="onCompositionStart";break t;case"compositionend":Ct="onCompositionEnd";break t;case"compositionupdate":Ct="onCompositionUpdate";break t}Ct=void 0}else Gr?Bp(t,a)&&(Ct="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(Ct="onCompositionStart");Ct&&(zp&&a.locale!=="ko"&&(Gr||Ct!=="onCompositionStart"?Ct==="onCompositionEnd"&&Gr&&(yt=Ap()):(Ra=J,Bu="value"in Ra?Ra.value:Ra.textContent,Gr=!0)),Bt=ps(P,Ct),0<Bt.length&&(Ct=new jp(Ct,t,null,a,J),rt.push({event:Ct,listeners:Bt}),yt?Ct.data=yt:(yt=$p(a),yt!==null&&(Ct.data=yt)))),(yt=Jb?t1(t,a):e1(t,a))&&(Ct=ps(P,"onBeforeInput"),0<Ct.length&&(Bt=new jp("onBeforeInput","beforeinput",null,a,J),rt.push({event:Bt,listeners:Ct}),Bt.data=yt)),V1(rt,t,P,a,J)}bg(rt,e)})}function Tl(t,e,a){return{instance:t,listener:e,currentTarget:a}}function ps(t,e){for(var a=e+"Capture",l=[];t!==null;){var u=t,f=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||f===null||(u=qo(t,a),u!=null&&l.unshift(Tl(t,u,f)),u=qo(t,e),u!=null&&l.push(Tl(t,u,f))),t.tag===3)return l;t=t.return}return[]}function ho(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function xg(t,e,a,l,u){for(var f=e._reactName,v=[];a!==null&&a!==l;){var T=a,z=T.alternate,P=T.stateNode;if(T=T.tag,z!==null&&z===l)break;T!==5&&T!==26&&T!==27||P===null||(z=P,u?(P=qo(a,f),P!=null&&v.unshift(Tl(a,P,z))):u||(P=qo(a,f),P!=null&&v.push(Tl(a,P,z)))),a=a.return}v.length!==0&&t.push({event:e,listeners:v})}var K1=/\r\n?/g,Q1=/\u0000|\uFFFD/g;function Cg(t){return(typeof t=="string"?t:""+t).replace(K1,`
`).replace(Q1,"")}function Eg(t,e){return e=Cg(e),Cg(t)===e}function ms(){}function Jt(t,e,a,l,u,f){switch(a){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||Hr(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&Hr(t,""+l);break;case"className":bi(t,"class",l);break;case"tabIndex":bi(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":bi(t,a,l);break;case"style":Rp(t,l,f);break;case"data":if(e!=="object"){bi(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=Ci(""+l),t.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(a==="formAction"?(e!=="input"&&Jt(t,e,"name",u.name,u,null),Jt(t,e,"formEncType",u.formEncType,u,null),Jt(t,e,"formMethod",u.formMethod,u,null),Jt(t,e,"formTarget",u.formTarget,u,null)):(Jt(t,e,"encType",u.encType,u,null),Jt(t,e,"method",u.method,u,null),Jt(t,e,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=Ci(""+l),t.setAttribute(a,l);break;case"onClick":l!=null&&(t.onclick=ms);break;case"onScroll":l!=null&&Ut("scroll",t);break;case"onScrollEnd":l!=null&&Ut("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(i(61));if(a=l.__html,a!=null){if(u.children!=null)throw Error(i(60));t.innerHTML=a}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}a=Ci(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""+l):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":l===!0?t.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,l):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(a,l):t.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(a):t.setAttribute(a,l);break;case"popover":Ut("beforetoggle",t),Ut("toggle",t),vi(t,"popover",l);break;case"xlinkActuate":aa(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":aa(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":aa(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":aa(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":aa(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":aa(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":aa(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":aa(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":aa(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":vi(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=wb.get(a)||a,vi(t,a,l))}}function gf(t,e,a,l,u,f){switch(a){case"style":Rp(t,l,f);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(i(61));if(a=l.__html,a!=null){if(u.children!=null)throw Error(i(60));t.innerHTML=a}}break;case"children":typeof l=="string"?Hr(t,l):(typeof l=="number"||typeof l=="bigint")&&Hr(t,""+l);break;case"onScroll":l!=null&&Ut("scroll",t);break;case"onScrollEnd":l!=null&&Ut("scrollend",t);break;case"onClick":l!=null&&(t.onclick=ms);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!mp.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(u=a.endsWith("Capture"),e=a.slice(2,u?a.length-7:void 0),f=t[nn]||null,f=f!=null?f[a]:null,typeof f=="function"&&t.removeEventListener(e,f,u),typeof l=="function")){typeof f!="function"&&f!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,l,u);break t}a in t?t[a]=l:l===!0?t.setAttribute(a,""):vi(t,a,l)}}}function Ge(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ut("error",t),Ut("load",t);var l=!1,u=!1,f;for(f in a)if(a.hasOwnProperty(f)){var v=a[f];if(v!=null)switch(f){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,e));default:Jt(t,e,f,v,a,null)}}u&&Jt(t,e,"srcSet",a.srcSet,a,null),l&&Jt(t,e,"src",a.src,a,null);return;case"input":Ut("invalid",t);var T=f=v=u=null,z=null,P=null;for(l in a)if(a.hasOwnProperty(l)){var J=a[l];if(J!=null)switch(l){case"name":u=J;break;case"type":v=J;break;case"checked":z=J;break;case"defaultChecked":P=J;break;case"value":f=J;break;case"defaultValue":T=J;break;case"children":case"dangerouslySetInnerHTML":if(J!=null)throw Error(i(137,e));break;default:Jt(t,e,l,J,a,null)}}xp(t,f,T,z,P,v,u,!1),Si(t);return;case"select":Ut("invalid",t),l=v=f=null;for(u in a)if(a.hasOwnProperty(u)&&(T=a[u],T!=null))switch(u){case"value":f=T;break;case"defaultValue":v=T;break;case"multiple":l=T;default:Jt(t,e,u,T,a,null)}e=f,a=v,t.multiple=!!l,e!=null?Ur(t,!!l,e,!1):a!=null&&Ur(t,!!l,a,!0);return;case"textarea":Ut("invalid",t),f=u=l=null;for(v in a)if(a.hasOwnProperty(v)&&(T=a[v],T!=null))switch(v){case"value":l=T;break;case"defaultValue":u=T;break;case"children":f=T;break;case"dangerouslySetInnerHTML":if(T!=null)throw Error(i(91));break;default:Jt(t,e,v,T,a,null)}Ep(t,l,u,f),Si(t);return;case"option":for(z in a)if(a.hasOwnProperty(z)&&(l=a[z],l!=null))switch(z){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Jt(t,e,z,l,a,null)}return;case"dialog":Ut("beforetoggle",t),Ut("toggle",t),Ut("cancel",t),Ut("close",t);break;case"iframe":case"object":Ut("load",t);break;case"video":case"audio":for(l=0;l<El.length;l++)Ut(El[l],t);break;case"image":Ut("error",t),Ut("load",t);break;case"details":Ut("toggle",t);break;case"embed":case"source":case"link":Ut("error",t),Ut("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(P in a)if(a.hasOwnProperty(P)&&(l=a[P],l!=null))switch(P){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,e));default:Jt(t,e,P,l,a,null)}return;default:if(ju(e)){for(J in a)a.hasOwnProperty(J)&&(l=a[J],l!==void 0&&gf(t,e,J,l,a,void 0));return}}for(T in a)a.hasOwnProperty(T)&&(l=a[T],l!=null&&Jt(t,e,T,l,a,null))}function W1(t,e,a,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,f=null,v=null,T=null,z=null,P=null,J=null;for(V in a){var rt=a[V];if(a.hasOwnProperty(V)&&rt!=null)switch(V){case"checked":break;case"value":break;case"defaultValue":z=rt;default:l.hasOwnProperty(V)||Jt(t,e,V,null,l,rt)}}for(var q in l){var V=l[q];if(rt=a[q],l.hasOwnProperty(q)&&(V!=null||rt!=null))switch(q){case"type":f=V;break;case"name":u=V;break;case"checked":P=V;break;case"defaultChecked":J=V;break;case"value":v=V;break;case"defaultValue":T=V;break;case"children":case"dangerouslySetInnerHTML":if(V!=null)throw Error(i(137,e));break;default:V!==rt&&Jt(t,e,q,V,l,rt)}}Ou(t,v,T,z,P,J,f,u);return;case"select":V=v=T=q=null;for(f in a)if(z=a[f],a.hasOwnProperty(f)&&z!=null)switch(f){case"value":break;case"multiple":V=z;default:l.hasOwnProperty(f)||Jt(t,e,f,null,l,z)}for(u in l)if(f=l[u],z=a[u],l.hasOwnProperty(u)&&(f!=null||z!=null))switch(u){case"value":q=f;break;case"defaultValue":T=f;break;case"multiple":v=f;default:f!==z&&Jt(t,e,u,f,l,z)}e=T,a=v,l=V,q!=null?Ur(t,!!a,q,!1):!!l!=!!a&&(e!=null?Ur(t,!!a,e,!0):Ur(t,!!a,a?[]:"",!1));return;case"textarea":V=q=null;for(T in a)if(u=a[T],a.hasOwnProperty(T)&&u!=null&&!l.hasOwnProperty(T))switch(T){case"value":break;case"children":break;default:Jt(t,e,T,null,l,u)}for(v in l)if(u=l[v],f=a[v],l.hasOwnProperty(v)&&(u!=null||f!=null))switch(v){case"value":q=u;break;case"defaultValue":V=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(i(91));break;default:u!==f&&Jt(t,e,v,u,l,f)}Cp(t,q,V);return;case"option":for(var Tt in a)if(q=a[Tt],a.hasOwnProperty(Tt)&&q!=null&&!l.hasOwnProperty(Tt))switch(Tt){case"selected":t.selected=!1;break;default:Jt(t,e,Tt,null,l,q)}for(z in l)if(q=l[z],V=a[z],l.hasOwnProperty(z)&&q!==V&&(q!=null||V!=null))switch(z){case"selected":t.selected=q&&typeof q!="function"&&typeof q!="symbol";break;default:Jt(t,e,z,q,l,V)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var St in a)q=a[St],a.hasOwnProperty(St)&&q!=null&&!l.hasOwnProperty(St)&&Jt(t,e,St,null,l,q);for(P in l)if(q=l[P],V=a[P],l.hasOwnProperty(P)&&q!==V&&(q!=null||V!=null))switch(P){case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(i(137,e));break;default:Jt(t,e,P,q,l,V)}return;default:if(ju(e)){for(var te in a)q=a[te],a.hasOwnProperty(te)&&q!==void 0&&!l.hasOwnProperty(te)&&gf(t,e,te,void 0,l,q);for(J in l)q=l[J],V=a[J],!l.hasOwnProperty(J)||q===V||q===void 0&&V===void 0||gf(t,e,J,q,l,V);return}}for(var $ in a)q=a[$],a.hasOwnProperty($)&&q!=null&&!l.hasOwnProperty($)&&Jt(t,e,$,null,l,q);for(rt in l)q=l[rt],V=a[rt],!l.hasOwnProperty(rt)||q===V||q==null&&V==null||Jt(t,e,rt,q,l,V)}var yf=null,vf=null;function hs(t){return t.nodeType===9?t:t.ownerDocument}function Tg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Rg(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function bf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Sf=null;function Z1(){var t=window.event;return t&&t.type==="popstate"?t===Sf?!1:(Sf=t,!0):(Sf=null,!1)}var wg=typeof setTimeout=="function"?setTimeout:void 0,J1=typeof clearTimeout=="function"?clearTimeout:void 0,Mg=typeof Promise=="function"?Promise:void 0,tS=typeof queueMicrotask=="function"?queueMicrotask:typeof Mg<"u"?function(t){return Mg.resolve(null).then(t).catch(eS)}:wg;function eS(t){setTimeout(function(){throw t})}function Pa(t){return t==="head"}function Ag(t,e){var a=e,l=0,u=0;do{var f=a.nextSibling;if(t.removeChild(a),f&&f.nodeType===8)if(a=f.data,a==="/$"){if(0<l&&8>l){a=l;var v=t.ownerDocument;if(a&1&&Rl(v.documentElement),a&2&&Rl(v.body),a&4)for(a=v.head,Rl(a),v=a.firstChild;v;){var T=v.nextSibling,z=v.nodeName;v[Ho]||z==="SCRIPT"||z==="STYLE"||z==="LINK"&&v.rel.toLowerCase()==="stylesheet"||a.removeChild(v),v=T}}if(u===0){t.removeChild(f),zl(e);return}u--}else a==="$"||a==="$?"||a==="$!"?u++:l=a.charCodeAt(0)-48;else l=0;a=f}while(a);zl(e)}function xf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":xf(a),Ru(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function nS(t,e,a,l){for(;t.nodeType===1;){var u=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Ho])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==u.rel||t.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||t.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||t.getAttribute("title")!==(u.title==null?null:u.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(u.src==null?null:u.src)||t.getAttribute("type")!==(u.type==null?null:u.type)||t.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=u.name==null?null:""+u.name;if(u.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=Ln(t.nextSibling),t===null)break}return null}function aS(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=Ln(t.nextSibling),t===null))return null;return t}function Cf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function rS(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var l=function(){e(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Ln(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Ef=null;function Og(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function _g(t,e,a){switch(e=hs(a),t){case"html":if(t=e.documentElement,!t)throw Error(i(452));return t;case"head":if(t=e.head,!t)throw Error(i(453));return t;case"body":if(t=e.body,!t)throw Error(i(454));return t;default:throw Error(i(451))}}function Rl(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ru(t)}var _n=new Map,jg=new Set;function gs(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var ya=I.d;I.d={f:oS,r:lS,D:iS,C:sS,L:uS,m:cS,X:dS,S:fS,M:pS};function oS(){var t=ya.f(),e=is();return t||e}function lS(t){var e=kr(t);e!==null&&e.tag===5&&e.type==="form"?Wm(e):ya.r(t)}var go=typeof document>"u"?null:document;function Ng(t,e,a){var l=go;if(l&&typeof e=="string"&&e){var u=En(e);u='link[rel="'+t+'"][href="'+u+'"]',typeof a=="string"&&(u+='[crossorigin="'+a+'"]'),jg.has(u)||(jg.add(u),t={rel:t,crossOrigin:a,href:e},l.querySelector(u)===null&&(e=l.createElement("link"),Ge(e,"link",t),ke(e),l.head.appendChild(e)))}}function iS(t){ya.D(t),Ng("dns-prefetch",t,null)}function sS(t,e){ya.C(t,e),Ng("preconnect",t,e)}function uS(t,e,a){ya.L(t,e,a);var l=go;if(l&&t&&e){var u='link[rel="preload"][as="'+En(e)+'"]';e==="image"&&a&&a.imageSrcSet?(u+='[imagesrcset="'+En(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(u+='[imagesizes="'+En(a.imageSizes)+'"]')):u+='[href="'+En(t)+'"]';var f=u;switch(e){case"style":f=yo(t);break;case"script":f=vo(t)}_n.has(f)||(t=y({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),_n.set(f,t),l.querySelector(u)!==null||e==="style"&&l.querySelector(wl(f))||e==="script"&&l.querySelector(Ml(f))||(e=l.createElement("link"),Ge(e,"link",t),ke(e),l.head.appendChild(e)))}}function cS(t,e){ya.m(t,e);var a=go;if(a&&t){var l=e&&typeof e.as=="string"?e.as:"script",u='link[rel="modulepreload"][as="'+En(l)+'"][href="'+En(t)+'"]',f=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=vo(t)}if(!_n.has(f)&&(t=y({rel:"modulepreload",href:t},e),_n.set(f,t),a.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Ml(f)))return}l=a.createElement("link"),Ge(l,"link",t),ke(l),a.head.appendChild(l)}}}function fS(t,e,a){ya.S(t,e,a);var l=go;if(l&&t){var u=Br(l).hoistableStyles,f=yo(t);e=e||"default";var v=u.get(f);if(!v){var T={loading:0,preload:null};if(v=l.querySelector(wl(f)))T.loading=5;else{t=y({rel:"stylesheet",href:t,"data-precedence":e},a),(a=_n.get(f))&&Tf(t,a);var z=v=l.createElement("link");ke(z),Ge(z,"link",t),z._p=new Promise(function(P,J){z.onload=P,z.onerror=J}),z.addEventListener("load",function(){T.loading|=1}),z.addEventListener("error",function(){T.loading|=2}),T.loading|=4,ys(v,e,l)}v={type:"stylesheet",instance:v,count:1,state:T},u.set(f,v)}}}function dS(t,e){ya.X(t,e);var a=go;if(a&&t){var l=Br(a).hoistableScripts,u=vo(t),f=l.get(u);f||(f=a.querySelector(Ml(u)),f||(t=y({src:t,async:!0},e),(e=_n.get(u))&&Rf(t,e),f=a.createElement("script"),ke(f),Ge(f,"link",t),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},l.set(u,f))}}function pS(t,e){ya.M(t,e);var a=go;if(a&&t){var l=Br(a).hoistableScripts,u=vo(t),f=l.get(u);f||(f=a.querySelector(Ml(u)),f||(t=y({src:t,async:!0,type:"module"},e),(e=_n.get(u))&&Rf(t,e),f=a.createElement("script"),ke(f),Ge(f,"link",t),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},l.set(u,f))}}function zg(t,e,a,l){var u=(u=ct.current)?gs(u):null;if(!u)throw Error(i(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=yo(a.href),a=Br(u).hoistableStyles,l=a.get(e),l||(l={type:"style",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=yo(a.href);var f=Br(u).hoistableStyles,v=f.get(t);if(v||(u=u.ownerDocument||u,v={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,v),(f=u.querySelector(wl(t)))&&!f._p&&(v.instance=f,v.state.loading=5),_n.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},_n.set(t,a),f||mS(u,t,a,v.state))),e&&l===null)throw Error(i(528,""));return v}if(e&&l!==null)throw Error(i(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=vo(a),a=Br(u).hoistableScripts,l=a.get(e),l||(l={type:"script",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,t))}}function yo(t){return'href="'+En(t)+'"'}function wl(t){return'link[rel="stylesheet"]['+t+"]"}function Dg(t){return y({},t,{"data-precedence":t.precedence,precedence:null})}function mS(t,e,a,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Ge(e,"link",a),ke(e),t.head.appendChild(e))}function vo(t){return'[src="'+En(t)+'"]'}function Ml(t){return"script[async]"+t}function kg(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+En(a.href)+'"]');if(l)return e.instance=l,ke(l),l;var u=y({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),ke(l),Ge(l,"style",u),ys(l,a.precedence,t),e.instance=l;case"stylesheet":u=yo(a.href);var f=t.querySelector(wl(u));if(f)return e.state.loading|=4,e.instance=f,ke(f),f;l=Dg(a),(u=_n.get(u))&&Tf(l,u),f=(t.ownerDocument||t).createElement("link"),ke(f);var v=f;return v._p=new Promise(function(T,z){v.onload=T,v.onerror=z}),Ge(f,"link",l),e.state.loading|=4,ys(f,a.precedence,t),e.instance=f;case"script":return f=vo(a.src),(u=t.querySelector(Ml(f)))?(e.instance=u,ke(u),u):(l=a,(u=_n.get(f))&&(l=y({},a),Rf(l,u)),t=t.ownerDocument||t,u=t.createElement("script"),ke(u),Ge(u,"link",l),t.head.appendChild(u),e.instance=u);case"void":return null;default:throw Error(i(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,ys(l,a.precedence,t));return e.instance}function ys(t,e,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,f=u,v=0;v<l.length;v++){var T=l[v];if(T.dataset.precedence===e)f=T;else if(f!==u)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function Tf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Rf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var vs=null;function Bg(t,e,a){if(vs===null){var l=new Map,u=vs=new Map;u.set(a,l)}else u=vs,l=u.get(a),l||(l=new Map,u.set(a,l));if(l.has(t))return l;for(l.set(t,null),a=a.getElementsByTagName(t),u=0;u<a.length;u++){var f=a[u];if(!(f[Ho]||f[Fe]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var v=f.getAttribute(e)||"";v=t+v;var T=l.get(v);T?T.push(f):l.set(v,[f])}}return l}function $g(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function hS(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Lg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Al=null;function gS(){}function yS(t,e,a){if(Al===null)throw Error(i(475));var l=Al;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var u=yo(a.href),f=t.querySelector(wl(u));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=bs.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=f,ke(f);return}f=t.ownerDocument||t,a=Dg(a),(u=_n.get(u))&&Tf(a,u),f=f.createElement("link"),ke(f);var v=f;v._p=new Promise(function(T,z){v.onload=T,v.onerror=z}),Ge(f,"link",a),e.instance=f}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=bs.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function vS(){if(Al===null)throw Error(i(475));var t=Al;return t.stylesheets&&t.count===0&&wf(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&wf(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function bs(){if(this.count--,this.count===0){if(this.stylesheets)wf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ss=null;function wf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ss=new Map,e.forEach(bS,t),Ss=null,bs.call(t))}function bS(t,e){if(!(e.state.loading&4)){var a=Ss.get(t);if(a)var l=a.get(null);else{a=new Map,Ss.set(t,a);for(var u=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<u.length;f++){var v=u[f];(v.nodeName==="LINK"||v.getAttribute("media")!=="not all")&&(a.set(v.dataset.precedence,v),l=v)}l&&a.set(null,l)}u=e.instance,v=u.getAttribute("data-precedence"),f=a.get(v)||l,f===l&&a.set(null,u),a.set(v,u),this.count++,l=bs.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),f?f.parentNode.insertBefore(u,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(u,t.firstChild)),e.state.loading|=4}}var Ol={$$typeof:A,Provider:null,Consumer:null,_currentValue:ot,_currentValue2:ot,_threadCount:0};function SS(t,e,a,l,u,f,v,T){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Nr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Nr(0),this.hiddenUpdates=Nr(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=f,this.onRecoverableError=v,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=T,this.incompleteTransitions=new Map}function Ug(t,e,a,l,u,f,v,T,z,P,J,rt){return t=new SS(t,e,a,v,T,z,P,rt),e=1,f===!0&&(e|=24),f=fn(3,null,null,e),t.current=f,f.stateNode=t,e=lc(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:l,isDehydrated:a,cache:e},cc(f),t}function Hg(t){return t?(t=Xr,t):Xr}function Pg(t,e,a,l,u,f){u=Hg(u),l.context===null?l.context=u:l.pendingContext=u,l=Aa(e),l.payload={element:a},f=f===void 0?null:f,f!==null&&(l.callback=f),a=Oa(t,l,e),a!==null&&(gn(a,t,e),ol(a,t,e))}function qg(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function Mf(t,e){qg(t,e),(t=t.alternate)&&qg(t,e)}function Gg(t){if(t.tag===13){var e=Fr(t,67108864);e!==null&&gn(e,t,67108864),Mf(t,67108864)}}var xs=!0;function xS(t,e,a,l){var u=k.T;k.T=null;var f=I.p;try{I.p=2,Af(t,e,a,l)}finally{I.p=f,k.T=u}}function CS(t,e,a,l){var u=k.T;k.T=null;var f=I.p;try{I.p=8,Af(t,e,a,l)}finally{I.p=f,k.T=u}}function Af(t,e,a,l){if(xs){var u=Of(l);if(u===null)hf(t,e,l,Cs,a),Vg(t,l);else if(TS(u,t,e,a,l))l.stopPropagation();else if(Vg(t,l),e&4&&-1<ES.indexOf(t)){for(;u!==null;){var f=kr(u);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var v=xn(f.pendingLanes);if(v!==0){var T=f;for(T.pendingLanes|=2,T.entangledLanes|=2;v;){var z=1<<31-pe(v);T.entanglements[1]|=z,v&=~z}Vn(f),(Qt&6)===0&&(os=Kt()+500,Cl(0))}}break;case 13:T=Fr(f,2),T!==null&&gn(T,f,2),is(),Mf(f,2)}if(f=Of(l),f===null&&hf(t,e,l,Cs,a),f===u)break;u=f}u!==null&&l.stopPropagation()}else hf(t,e,l,null,a)}}function Of(t){return t=zu(t),_f(t)}var Cs=null;function _f(t){if(Cs=null,t=Dr(t),t!==null){var e=c(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=d(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Cs=t,null}function Ig(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(se()){case ie:return 2;case de:return 8;case Yt:case ht:return 32;case un:return 268435456;default:return 32}default:return 32}}var jf=!1,qa=null,Ga=null,Ia=null,_l=new Map,jl=new Map,Va=[],ES="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Vg(t,e){switch(t){case"focusin":case"focusout":qa=null;break;case"dragenter":case"dragleave":Ga=null;break;case"mouseover":case"mouseout":Ia=null;break;case"pointerover":case"pointerout":_l.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":jl.delete(e.pointerId)}}function Nl(t,e,a,l,u,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:a,eventSystemFlags:l,nativeEvent:f,targetContainers:[u]},e!==null&&(e=kr(e),e!==null&&Gg(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,u!==null&&e.indexOf(u)===-1&&e.push(u),t)}function TS(t,e,a,l,u){switch(e){case"focusin":return qa=Nl(qa,t,e,a,l,u),!0;case"dragenter":return Ga=Nl(Ga,t,e,a,l,u),!0;case"mouseover":return Ia=Nl(Ia,t,e,a,l,u),!0;case"pointerover":var f=u.pointerId;return _l.set(f,Nl(_l.get(f)||null,t,e,a,l,u)),!0;case"gotpointercapture":return f=u.pointerId,jl.set(f,Nl(jl.get(f)||null,t,e,a,l,u)),!0}return!1}function Yg(t){var e=Dr(t.target);if(e!==null){var a=c(e);if(a!==null){if(e=a.tag,e===13){if(e=d(a),e!==null){t.blockedOn=e,yb(t.priority,function(){if(a.tag===13){var l=hn();l=Cu(l);var u=Fr(a,l);u!==null&&gn(u,a,l),Mf(a,l)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Es(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=Of(t.nativeEvent);if(a===null){a=t.nativeEvent;var l=new a.constructor(a.type,a);Nu=l,a.target.dispatchEvent(l),Nu=null}else return e=kr(a),e!==null&&Gg(e),t.blockedOn=a,!1;e.shift()}return!0}function Fg(t,e,a){Es(t)&&a.delete(e)}function RS(){jf=!1,qa!==null&&Es(qa)&&(qa=null),Ga!==null&&Es(Ga)&&(Ga=null),Ia!==null&&Es(Ia)&&(Ia=null),_l.forEach(Fg),jl.forEach(Fg)}function Ts(t,e){t.blockedOn===e&&(t.blockedOn=null,jf||(jf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,RS)))}var Rs=null;function Xg(t){Rs!==t&&(Rs=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Rs===t&&(Rs=null);for(var e=0;e<t.length;e+=3){var a=t[e],l=t[e+1],u=t[e+2];if(typeof l!="function"){if(_f(l||a)===null)continue;break}var f=kr(a);f!==null&&(t.splice(e,3),e-=3,Oc(f,{pending:!0,data:u,method:a.method,action:l},l,u))}}))}function zl(t){function e(z){return Ts(z,t)}qa!==null&&Ts(qa,t),Ga!==null&&Ts(Ga,t),Ia!==null&&Ts(Ia,t),_l.forEach(e),jl.forEach(e);for(var a=0;a<Va.length;a++){var l=Va[a];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Va.length&&(a=Va[0],a.blockedOn===null);)Yg(a),a.blockedOn===null&&Va.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var u=a[l],f=a[l+1],v=u[nn]||null;if(typeof f=="function")v||Xg(a);else if(v){var T=null;if(f&&f.hasAttribute("formAction")){if(u=f,v=f[nn]||null)T=v.formAction;else if(_f(u)!==null)continue}else T=v.action;typeof T=="function"?a[l+1]=T:(a.splice(l,3),l-=3),Xg(a)}}}function Nf(t){this._internalRoot=t}ws.prototype.render=Nf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(i(409));var a=e.current,l=hn();Pg(a,l,t,e,null,null)},ws.prototype.unmount=Nf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Pg(t.current,2,null,t,null,null),is(),e[zr]=null}};function ws(t){this._internalRoot=t}ws.prototype.unstable_scheduleHydration=function(t){if(t){var e=fp();t={blockedOn:null,target:t,priority:e};for(var a=0;a<Va.length&&e!==0&&e<Va[a].priority;a++);Va.splice(a,0,t),a===0&&Yg(t)}};var Kg=r.version;if(Kg!=="19.1.0")throw Error(i(527,Kg,"19.1.0"));I.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(i(188)):(t=Object.keys(t).join(","),Error(i(268,t)));return t=m(e),t=t!==null?h(t):null,t=t===null?null:t.stateNode,t};var wS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:k,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ms=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ms.isDisabled&&Ms.supportsFiber)try{en=Ms.inject(wS),ge=Ms}catch{}}return Bl.createRoot=function(t,e){if(!s(t))throw Error(i(299));var a=!1,l="",u=fh,f=dh,v=ph,T=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(u=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(v=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(T=e.unstable_transitionCallbacks)),e=Ug(t,1,!1,null,null,a,l,u,f,v,T,null),t[zr]=e.current,mf(t),new Nf(e)},Bl.hydrateRoot=function(t,e,a){if(!s(t))throw Error(i(299));var l=!1,u="",f=fh,v=dh,T=ph,z=null,P=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(u=a.identifierPrefix),a.onUncaughtError!==void 0&&(f=a.onUncaughtError),a.onCaughtError!==void 0&&(v=a.onCaughtError),a.onRecoverableError!==void 0&&(T=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(z=a.unstable_transitionCallbacks),a.formState!==void 0&&(P=a.formState)),e=Ug(t,1,!0,e,a??null,l,u,f,v,T,z,P),e.context=Hg(null),a=e.current,l=hn(),l=Cu(l),u=Aa(l),u.callback=null,Oa(a,u,l),a=l,e.current.lanes=a,Uo(e,a),Vn(e),t[zr]=e.current,mf(t),new ws(e)},Bl.version="19.1.0",Bl}var ly;function PS(){if(ly)return kf.exports;ly=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),kf.exports=HS(),kf.exports}var qS=PS(),L0=$0();const As=B0(L0);/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Wl(){return Wl=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},Wl.apply(this,arguments)}var Xa;(function(n){n.Pop="POP",n.Push="PUSH",n.Replace="REPLACE"})(Xa||(Xa={}));const iy="popstate";function GS(n){n===void 0&&(n={});function r(i,s){let{pathname:c,search:d,hash:p}=i.location;return ed("",{pathname:c,search:d,hash:p},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function o(i,s){return typeof s=="string"?s:Is(s)}return VS(r,o,null,n)}function _e(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function U0(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function IS(){return Math.random().toString(36).substr(2,8)}function sy(n,r){return{usr:n.state,key:n.key,idx:r}}function ed(n,r,o,i){return o===void 0&&(o=null),Wl({pathname:typeof n=="string"?n:n.pathname,search:"",hash:""},typeof r=="string"?zo(r):r,{state:o,key:r&&r.key||i||IS()})}function Is(n){let{pathname:r="/",search:o="",hash:i=""}=n;return o&&o!=="?"&&(r+=o.charAt(0)==="?"?o:"?"+o),i&&i!=="#"&&(r+=i.charAt(0)==="#"?i:"#"+i),r}function zo(n){let r={};if(n){let o=n.indexOf("#");o>=0&&(r.hash=n.substr(o),n=n.substr(0,o));let i=n.indexOf("?");i>=0&&(r.search=n.substr(i),n=n.substr(0,i)),n&&(r.pathname=n)}return r}function VS(n,r,o,i){i===void 0&&(i={});let{window:s=document.defaultView,v5Compat:c=!1}=i,d=s.history,p=Xa.Pop,m=null,h=y();h==null&&(h=0,d.replaceState(Wl({},d.state,{idx:h}),""));function y(){return(d.state||{idx:null}).idx}function b(){p=Xa.Pop;let M=y(),j=M==null?null:M-h;h=M,m&&m({action:p,location:w.location,delta:j})}function x(M,j){p=Xa.Push;let D=ed(w.location,M,j);h=y()+1;let A=sy(D,h),O=w.createHref(D);try{d.pushState(A,"",O)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;s.location.assign(O)}c&&m&&m({action:p,location:w.location,delta:1})}function R(M,j){p=Xa.Replace;let D=ed(w.location,M,j);h=y();let A=sy(D,h),O=w.createHref(D);d.replaceState(A,"",O),c&&m&&m({action:p,location:w.location,delta:0})}function C(M){let j=s.location.origin!=="null"?s.location.origin:s.location.href,D=typeof M=="string"?M:Is(M);return D=D.replace(/ $/,"%20"),_e(j,"No window.location.(origin|href) available to create URL for href: "+D),new URL(D,j)}let w={get action(){return p},get location(){return n(s,d)},listen(M){if(m)throw new Error("A history only accepts one active listener");return s.addEventListener(iy,b),m=M,()=>{s.removeEventListener(iy,b),m=null}},createHref(M){return r(s,M)},createURL:C,encodeLocation(M){let j=C(M);return{pathname:j.pathname,search:j.search,hash:j.hash}},push:x,replace:R,go(M){return d.go(M)}};return w}var uy;(function(n){n.data="data",n.deferred="deferred",n.redirect="redirect",n.error="error"})(uy||(uy={}));function YS(n,r,o){return o===void 0&&(o="/"),FS(n,r,o)}function FS(n,r,o,i){let s=typeof r=="string"?zo(r):r,c=Ad(s.pathname||"/",o);if(c==null)return null;let d=H0(n);XS(d);let p=null;for(let m=0;p==null&&m<d.length;++m){let h=lx(c);p=ax(d[m],h)}return p}function H0(n,r,o,i){r===void 0&&(r=[]),o===void 0&&(o=[]),i===void 0&&(i="");let s=(c,d,p)=>{let m={relativePath:p===void 0?c.path||"":p,caseSensitive:c.caseSensitive===!0,childrenIndex:d,route:c};m.relativePath.startsWith("/")&&(_e(m.relativePath.startsWith(i),'Absolute route path "'+m.relativePath+'" nested under path '+('"'+i+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),m.relativePath=m.relativePath.slice(i.length));let h=Ka([i,m.relativePath]),y=o.concat(m);c.children&&c.children.length>0&&(_e(c.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),H0(c.children,r,y,h)),!(c.path==null&&!c.index)&&r.push({path:h,score:ex(h,c.index),routesMeta:y})};return n.forEach((c,d)=>{var p;if(c.path===""||!((p=c.path)!=null&&p.includes("?")))s(c,d);else for(let m of P0(c.path))s(c,d,m)}),r}function P0(n){let r=n.split("/");if(r.length===0)return[];let[o,...i]=r,s=o.endsWith("?"),c=o.replace(/\?$/,"");if(i.length===0)return s?[c,""]:[c];let d=P0(i.join("/")),p=[];return p.push(...d.map(m=>m===""?c:[c,m].join("/"))),s&&p.push(...d),p.map(m=>n.startsWith("/")&&m===""?"/":m)}function XS(n){n.sort((r,o)=>r.score!==o.score?o.score-r.score:nx(r.routesMeta.map(i=>i.childrenIndex),o.routesMeta.map(i=>i.childrenIndex)))}const KS=/^:[\w-]+$/,QS=3,WS=2,ZS=1,JS=10,tx=-2,cy=n=>n==="*";function ex(n,r){let o=n.split("/"),i=o.length;return o.some(cy)&&(i+=tx),r&&(i+=WS),o.filter(s=>!cy(s)).reduce((s,c)=>s+(KS.test(c)?QS:c===""?ZS:JS),i)}function nx(n,r){return n.length===r.length&&n.slice(0,-1).every((i,s)=>i===r[s])?n[n.length-1]-r[r.length-1]:0}function ax(n,r,o){let{routesMeta:i}=n,s={},c="/",d=[];for(let p=0;p<i.length;++p){let m=i[p],h=p===i.length-1,y=c==="/"?r:r.slice(c.length)||"/",b=rx({path:m.relativePath,caseSensitive:m.caseSensitive,end:h},y),x=m.route;if(!b)return null;Object.assign(s,b.params),d.push({params:s,pathname:Ka([c,b.pathname]),pathnameBase:cx(Ka([c,b.pathnameBase])),route:x}),b.pathnameBase!=="/"&&(c=Ka([c,b.pathnameBase]))}return d}function rx(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[o,i]=ox(n.path,n.caseSensitive,n.end),s=r.match(o);if(!s)return null;let c=s[0],d=c.replace(/(.)\/+$/,"$1"),p=s.slice(1);return{params:i.reduce((h,y,b)=>{let{paramName:x,isOptional:R}=y;if(x==="*"){let w=p[b]||"";d=c.slice(0,c.length-w.length).replace(/(.)\/+$/,"$1")}const C=p[b];return R&&!C?h[x]=void 0:h[x]=(C||"").replace(/%2F/g,"/"),h},{}),pathname:c,pathnameBase:d,pattern:n}}function ox(n,r,o){r===void 0&&(r=!1),o===void 0&&(o=!0),U0(n==="*"||!n.endsWith("*")||n.endsWith("/*"),'Route path "'+n+'" will be treated as if it were '+('"'+n.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+n.replace(/\*$/,"/*")+'".'));let i=[],s="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,p,m)=>(i.push({paramName:p,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(i.push({paramName:"*"}),s+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?s+="\\/*$":n!==""&&n!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,r?void 0:"i"),i]}function lx(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return U0(!1,'The URL path "'+n+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),n}}function Ad(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let o=r.endsWith("/")?r.length-1:r.length,i=n.charAt(o);return i&&i!=="/"?null:n.slice(o)||"/"}function ix(n,r){r===void 0&&(r="/");let{pathname:o,search:i="",hash:s=""}=typeof n=="string"?zo(n):n;return{pathname:o?o.startsWith("/")?o:sx(o,r):r,search:fx(i),hash:dx(s)}}function sx(n,r){let o=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(s=>{s===".."?o.length>1&&o.pop():s!=="."&&o.push(s)}),o.length>1?o.join("/"):"/"}function Uf(n,r,o,i){return"Cannot include a '"+n+"' character in a manually specified "+("`to."+r+"` field ["+JSON.stringify(i)+"].  Please separate it out to the ")+("`to."+o+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ux(n){return n.filter((r,o)=>o===0||r.route.path&&r.route.path.length>0)}function q0(n,r){let o=ux(n);return r?o.map((i,s)=>s===o.length-1?i.pathname:i.pathnameBase):o.map(i=>i.pathnameBase)}function G0(n,r,o,i){i===void 0&&(i=!1);let s;typeof n=="string"?s=zo(n):(s=Wl({},n),_e(!s.pathname||!s.pathname.includes("?"),Uf("?","pathname","search",s)),_e(!s.pathname||!s.pathname.includes("#"),Uf("#","pathname","hash",s)),_e(!s.search||!s.search.includes("#"),Uf("#","search","hash",s)));let c=n===""||s.pathname==="",d=c?"/":s.pathname,p;if(d==null)p=o;else{let b=r.length-1;if(!i&&d.startsWith("..")){let x=d.split("/");for(;x[0]==="..";)x.shift(),b-=1;s.pathname=x.join("/")}p=b>=0?r[b]:"/"}let m=ix(s,p),h=d&&d!=="/"&&d.endsWith("/"),y=(c||d===".")&&o.endsWith("/");return!m.pathname.endsWith("/")&&(h||y)&&(m.pathname+="/"),m}const Ka=n=>n.join("/").replace(/\/\/+/g,"/"),cx=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),fx=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,dx=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function px(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}const I0=["post","put","patch","delete"];new Set(I0);const mx=["get",...I0];new Set(mx);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Zl(){return Zl=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},Zl.apply(this,arguments)}const Od=S.createContext(null),hx=S.createContext(null),Ar=S.createContext(null),tu=S.createContext(null),Or=S.createContext({outlet:null,matches:[],isDataRoute:!1}),V0=S.createContext(null);function gx(n,r){let{relative:o}=r===void 0?{}:r;ri()||_e(!1);let{basename:i,navigator:s}=S.useContext(Ar),{hash:c,pathname:d,search:p}=F0(n,{relative:o}),m=d;return i!=="/"&&(m=d==="/"?i:Ka([i,d])),s.createHref({pathname:m,search:p,hash:c})}function ri(){return S.useContext(tu)!=null}function Do(){return ri()||_e(!1),S.useContext(tu).location}function Y0(n){S.useContext(Ar).static||S.useLayoutEffect(n)}function oi(){let{isDataRoute:n}=S.useContext(Or);return n?Ox():yx()}function yx(){ri()||_e(!1);let n=S.useContext(Od),{basename:r,future:o,navigator:i}=S.useContext(Ar),{matches:s}=S.useContext(Or),{pathname:c}=Do(),d=JSON.stringify(q0(s,o.v7_relativeSplatPath)),p=S.useRef(!1);return Y0(()=>{p.current=!0}),S.useCallback(function(h,y){if(y===void 0&&(y={}),!p.current)return;if(typeof h=="number"){i.go(h);return}let b=G0(h,JSON.parse(d),c,y.relative==="path");n==null&&r!=="/"&&(b.pathname=b.pathname==="/"?r:Ka([r,b.pathname])),(y.replace?i.replace:i.push)(b,y.state,y)},[r,i,d,c,n])}function F0(n,r){let{relative:o}=r===void 0?{}:r,{future:i}=S.useContext(Ar),{matches:s}=S.useContext(Or),{pathname:c}=Do(),d=JSON.stringify(q0(s,i.v7_relativeSplatPath));return S.useMemo(()=>G0(n,JSON.parse(d),c,o==="path"),[n,d,c,o])}function vx(n,r){return bx(n,r)}function bx(n,r,o,i){ri()||_e(!1);let{navigator:s}=S.useContext(Ar),{matches:c}=S.useContext(Or),d=c[c.length-1],p=d?d.params:{};d&&d.pathname;let m=d?d.pathnameBase:"/";d&&d.route;let h=Do(),y;if(r){var b;let M=typeof r=="string"?zo(r):r;m==="/"||(b=M.pathname)!=null&&b.startsWith(m)||_e(!1),y=M}else y=h;let x=y.pathname||"/",R=x;if(m!=="/"){let M=m.replace(/^\//,"").split("/");R="/"+x.replace(/^\//,"").split("/").slice(M.length).join("/")}let C=YS(n,{pathname:R}),w=Tx(C&&C.map(M=>Object.assign({},M,{params:Object.assign({},p,M.params),pathname:Ka([m,s.encodeLocation?s.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?m:Ka([m,s.encodeLocation?s.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),c,o,i);return r&&w?S.createElement(tu.Provider,{value:{location:Zl({pathname:"/",search:"",hash:"",state:null,key:"default"},y),navigationType:Xa.Pop}},w):w}function Sx(){let n=Ax(),r=px(n)?n.status+" "+n.statusText:n instanceof Error?n.message:JSON.stringify(n),o=n instanceof Error?n.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},r),o?S.createElement("pre",{style:s},o):null,null)}const xx=S.createElement(Sx,null);class Cx extends S.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,o){return o.location!==r.location||o.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:o.error,location:o.location,revalidation:r.revalidation||o.revalidation}}componentDidCatch(r,o){console.error("React Router caught the following error during render",r,o)}render(){return this.state.error!==void 0?S.createElement(Or.Provider,{value:this.props.routeContext},S.createElement(V0.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ex(n){let{routeContext:r,match:o,children:i}=n,s=S.useContext(Od);return s&&s.static&&s.staticContext&&(o.route.errorElement||o.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=o.route.id),S.createElement(Or.Provider,{value:r},i)}function Tx(n,r,o,i){var s;if(r===void 0&&(r=[]),o===void 0&&(o=null),i===void 0&&(i=null),n==null){var c;if(!o)return null;if(o.errors)n=o.matches;else if((c=i)!=null&&c.v7_partialHydration&&r.length===0&&!o.initialized&&o.matches.length>0)n=o.matches;else return null}let d=n,p=(s=o)==null?void 0:s.errors;if(p!=null){let y=d.findIndex(b=>b.route.id&&(p==null?void 0:p[b.route.id])!==void 0);y>=0||_e(!1),d=d.slice(0,Math.min(d.length,y+1))}let m=!1,h=-1;if(o&&i&&i.v7_partialHydration)for(let y=0;y<d.length;y++){let b=d[y];if((b.route.HydrateFallback||b.route.hydrateFallbackElement)&&(h=y),b.route.id){let{loaderData:x,errors:R}=o,C=b.route.loader&&x[b.route.id]===void 0&&(!R||R[b.route.id]===void 0);if(b.route.lazy||C){m=!0,h>=0?d=d.slice(0,h+1):d=[d[0]];break}}}return d.reduceRight((y,b,x)=>{let R,C=!1,w=null,M=null;o&&(R=p&&b.route.id?p[b.route.id]:void 0,w=b.route.errorElement||xx,m&&(h<0&&x===0?(_x("route-fallback"),C=!0,M=null):h===x&&(C=!0,M=b.route.hydrateFallbackElement||null)));let j=r.concat(d.slice(0,x+1)),D=()=>{let A;return R?A=w:C?A=M:b.route.Component?A=S.createElement(b.route.Component,null):b.route.element?A=b.route.element:A=y,S.createElement(Ex,{match:b,routeContext:{outlet:y,matches:j,isDataRoute:o!=null},children:A})};return o&&(b.route.ErrorBoundary||b.route.errorElement||x===0)?S.createElement(Cx,{location:o.location,revalidation:o.revalidation,component:w,error:R,children:D(),routeContext:{outlet:null,matches:j,isDataRoute:!0}}):D()},null)}var X0=function(n){return n.UseBlocker="useBlocker",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n}(X0||{}),K0=function(n){return n.UseBlocker="useBlocker",n.UseLoaderData="useLoaderData",n.UseActionData="useActionData",n.UseRouteError="useRouteError",n.UseNavigation="useNavigation",n.UseRouteLoaderData="useRouteLoaderData",n.UseMatches="useMatches",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n.UseRouteId="useRouteId",n}(K0||{});function Rx(n){let r=S.useContext(Od);return r||_e(!1),r}function wx(n){let r=S.useContext(hx);return r||_e(!1),r}function Mx(n){let r=S.useContext(Or);return r||_e(!1),r}function Q0(n){let r=Mx(),o=r.matches[r.matches.length-1];return o.route.id||_e(!1),o.route.id}function Ax(){var n;let r=S.useContext(V0),o=wx(),i=Q0();return r!==void 0?r:(n=o.errors)==null?void 0:n[i]}function Ox(){let{router:n}=Rx(X0.UseNavigateStable),r=Q0(K0.UseNavigateStable),o=S.useRef(!1);return Y0(()=>{o.current=!0}),S.useCallback(function(s,c){c===void 0&&(c={}),o.current&&(typeof s=="number"?n.navigate(s):n.navigate(s,Zl({fromRouteId:r},c)))},[n,r])}const fy={};function _x(n,r,o){fy[n]||(fy[n]=!0)}function jx(n,r){n==null||n.v7_startTransition,n==null||n.v7_relativeSplatPath}function jn(n){_e(!1)}function Nx(n){let{basename:r="/",children:o=null,location:i,navigationType:s=Xa.Pop,navigator:c,static:d=!1,future:p}=n;ri()&&_e(!1);let m=r.replace(/^\/*/,"/"),h=S.useMemo(()=>({basename:m,navigator:c,static:d,future:Zl({v7_relativeSplatPath:!1},p)}),[m,p,c,d]);typeof i=="string"&&(i=zo(i));let{pathname:y="/",search:b="",hash:x="",state:R=null,key:C="default"}=i,w=S.useMemo(()=>{let M=Ad(y,m);return M==null?null:{location:{pathname:M,search:b,hash:x,state:R,key:C},navigationType:s}},[m,y,b,x,R,C,s]);return w==null?null:S.createElement(Ar.Provider,{value:h},S.createElement(tu.Provider,{children:o,value:w}))}function zx(n){let{children:r,location:o}=n;return vx(nd(r),o)}new Promise(()=>{});function nd(n,r){r===void 0&&(r=[]);let o=[];return S.Children.forEach(n,(i,s)=>{if(!S.isValidElement(i))return;let c=[...r,s];if(i.type===S.Fragment){o.push.apply(o,nd(i.props.children,c));return}i.type!==jn&&_e(!1),!i.props.index||!i.props.children||_e(!1);let d={id:i.props.id||c.join("-"),caseSensitive:i.props.caseSensitive,element:i.props.element,Component:i.props.Component,index:i.props.index,path:i.props.path,loader:i.props.loader,action:i.props.action,errorElement:i.props.errorElement,ErrorBoundary:i.props.ErrorBoundary,hasErrorBoundary:i.props.ErrorBoundary!=null||i.props.errorElement!=null,shouldRevalidate:i.props.shouldRevalidate,handle:i.props.handle,lazy:i.props.lazy};i.props.children&&(d.children=nd(i.props.children,c)),o.push(d)}),o}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ad(){return ad=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},ad.apply(this,arguments)}function Dx(n,r){if(n==null)return{};var o={},i=Object.keys(n),s,c;for(c=0;c<i.length;c++)s=i[c],!(r.indexOf(s)>=0)&&(o[s]=n[s]);return o}function kx(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function Bx(n,r){return n.button===0&&(!r||r==="_self")&&!kx(n)}function rd(n){return n===void 0&&(n=""),new URLSearchParams(typeof n=="string"||Array.isArray(n)||n instanceof URLSearchParams?n:Object.keys(n).reduce((r,o)=>{let i=n[o];return r.concat(Array.isArray(i)?i.map(s=>[o,s]):[[o,i]])},[]))}function $x(n,r){let o=rd(n);return r&&r.forEach((i,s)=>{o.has(s)||r.getAll(s).forEach(c=>{o.append(s,c)})}),o}const Lx=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Ux="6";try{window.__reactRouterVersion=Ux}catch{}const Hx="startTransition",dy=Gs[Hx];function Px(n){let{basename:r,children:o,future:i,window:s}=n,c=S.useRef();c.current==null&&(c.current=GS({window:s,v5Compat:!0}));let d=c.current,[p,m]=S.useState({action:d.action,location:d.location}),{v7_startTransition:h}=i||{},y=S.useCallback(b=>{h&&dy?dy(()=>m(b)):m(b)},[m,h]);return S.useLayoutEffect(()=>d.listen(y),[d,y]),S.useEffect(()=>jx(i),[i]),S.createElement(Nx,{basename:r,children:o,location:p.location,navigationType:p.action,navigator:d,future:i})}const qx=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Gx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Yl=S.forwardRef(function(r,o){let{onClick:i,relative:s,reloadDocument:c,replace:d,state:p,target:m,to:h,preventScrollReset:y,viewTransition:b}=r,x=Dx(r,Lx),{basename:R}=S.useContext(Ar),C,w=!1;if(typeof h=="string"&&Gx.test(h)&&(C=h,qx))try{let A=new URL(window.location.href),O=h.startsWith("//")?new URL(A.protocol+h):new URL(h),_=Ad(O.pathname,R);O.origin===A.origin&&_!=null?h=_+O.search+O.hash:w=!0}catch{}let M=gx(h,{relative:s}),j=Ix(h,{replace:d,state:p,target:m,preventScrollReset:y,relative:s,viewTransition:b});function D(A){i&&i(A),A.defaultPrevented||j(A)}return S.createElement("a",ad({},x,{href:C||M,onClick:w||c?i:D,ref:o,target:m}))});var py;(function(n){n.UseScrollRestoration="useScrollRestoration",n.UseSubmit="useSubmit",n.UseSubmitFetcher="useSubmitFetcher",n.UseFetcher="useFetcher",n.useViewTransitionState="useViewTransitionState"})(py||(py={}));var my;(function(n){n.UseFetcher="useFetcher",n.UseFetchers="useFetchers",n.UseScrollRestoration="useScrollRestoration"})(my||(my={}));function Ix(n,r){let{target:o,replace:i,state:s,preventScrollReset:c,relative:d,viewTransition:p}=r===void 0?{}:r,m=oi(),h=Do(),y=F0(n,{relative:d});return S.useCallback(b=>{if(Bx(b,o)){b.preventDefault();let x=i!==void 0?i:Is(h)===Is(y);m(n,{replace:x,state:s,preventScrollReset:c,relative:d,viewTransition:p})}},[h,m,y,i,s,o,n,c,d,p])}function Vx(n){let r=S.useRef(rd(n)),o=S.useRef(!1),i=Do(),s=S.useMemo(()=>$x(i.search,o.current?null:r.current),[i.search]),c=oi(),d=S.useCallback((p,m)=>{const h=rd(typeof p=="function"?p(s):p);o.current=!0,c("?"+h,m)},[c,s]);return[s,d]}const Yx="_nav_1bw4i_1",Fx="_link_1bw4i_17",Xx="_linkActive_1bw4i_29",Hf={nav:Yx,link:Fx,linkActive:Xx},Jl={black:"#000",white:"#fff"},bo={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},So={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},xo={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Co={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Eo={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},$l={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Kx={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function Sa(n,...r){const o=new URL(`https://mui.com/production-error/?code=${n}`);return r.forEach(i=>o.searchParams.append("args[]",i)),`Minified MUI error #${n}; visit ${o} for the full message.`}const Zn="$$material";function Vs(){return Vs=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var i in o)({}).hasOwnProperty.call(o,i)&&(n[i]=o[i])}return n},Vs.apply(null,arguments)}function Qx(n){if(n.sheet)return n.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===n)return document.styleSheets[r]}function Wx(n){var r=document.createElement("style");return r.setAttribute("data-emotion",n.key),n.nonce!==void 0&&r.setAttribute("nonce",n.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var Zx=function(){function n(o){var i=this;this._insertTag=function(s){var c;i.tags.length===0?i.insertionPoint?c=i.insertionPoint.nextSibling:i.prepend?c=i.container.firstChild:c=i.before:c=i.tags[i.tags.length-1].nextSibling,i.container.insertBefore(s,c),i.tags.push(s)},this.isSpeedy=o.speedy===void 0?!0:o.speedy,this.tags=[],this.ctr=0,this.nonce=o.nonce,this.key=o.key,this.container=o.container,this.prepend=o.prepend,this.insertionPoint=o.insertionPoint,this.before=null}var r=n.prototype;return r.hydrate=function(i){i.forEach(this._insertTag)},r.insert=function(i){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(Wx(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var c=Qx(s);try{c.insertRule(i,c.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(i));this.ctr++},r.flush=function(){this.tags.forEach(function(i){var s;return(s=i.parentNode)==null?void 0:s.removeChild(i)}),this.tags=[],this.ctr=0},n}(),Qe="-ms-",Ys="-moz-",It="-webkit-",W0="comm",_d="rule",jd="decl",Jx="@import",Z0="@keyframes",tC="@layer",eC=Math.abs,eu=String.fromCharCode,nC=Object.assign;function aC(n,r){return Ie(n,0)^45?(((r<<2^Ie(n,0))<<2^Ie(n,1))<<2^Ie(n,2))<<2^Ie(n,3):0}function J0(n){return n.trim()}function rC(n,r){return(n=r.exec(n))?n[0]:n}function Vt(n,r,o){return n.replace(r,o)}function od(n,r){return n.indexOf(r)}function Ie(n,r){return n.charCodeAt(r)|0}function ti(n,r,o){return n.slice(r,o)}function Xn(n){return n.length}function Nd(n){return n.length}function Os(n,r){return r.push(n),n}function oC(n,r){return n.map(r).join("")}var nu=1,_o=1,tv=0,sn=0,Ae=0,ko="";function au(n,r,o,i,s,c,d){return{value:n,root:r,parent:o,type:i,props:s,children:c,line:nu,column:_o,length:d,return:""}}function Ll(n,r){return nC(au("",null,null,"",null,null,0),n,{length:-n.length},r)}function lC(){return Ae}function iC(){return Ae=sn>0?Ie(ko,--sn):0,_o--,Ae===10&&(_o=1,nu--),Ae}function vn(){return Ae=sn<tv?Ie(ko,sn++):0,_o++,Ae===10&&(_o=1,nu++),Ae}function Jn(){return Ie(ko,sn)}function Bs(){return sn}function li(n,r){return ti(ko,n,r)}function ei(n){switch(n){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function ev(n){return nu=_o=1,tv=Xn(ko=n),sn=0,[]}function nv(n){return ko="",n}function $s(n){return J0(li(sn-1,ld(n===91?n+2:n===40?n+1:n)))}function sC(n){for(;(Ae=Jn())&&Ae<33;)vn();return ei(n)>2||ei(Ae)>3?"":" "}function uC(n,r){for(;--r&&vn()&&!(Ae<48||Ae>102||Ae>57&&Ae<65||Ae>70&&Ae<97););return li(n,Bs()+(r<6&&Jn()==32&&vn()==32))}function ld(n){for(;vn();)switch(Ae){case n:return sn;case 34:case 39:n!==34&&n!==39&&ld(Ae);break;case 40:n===41&&ld(n);break;case 92:vn();break}return sn}function cC(n,r){for(;vn()&&n+Ae!==57;)if(n+Ae===84&&Jn()===47)break;return"/*"+li(r,sn-1)+"*"+eu(n===47?n:vn())}function fC(n){for(;!ei(Jn());)vn();return li(n,sn)}function dC(n){return nv(Ls("",null,null,null,[""],n=ev(n),0,[0],n))}function Ls(n,r,o,i,s,c,d,p,m){for(var h=0,y=0,b=d,x=0,R=0,C=0,w=1,M=1,j=1,D=0,A="",O=s,_=c,L=i,Y=A;M;)switch(C=D,D=vn()){case 40:if(C!=108&&Ie(Y,b-1)==58){od(Y+=Vt($s(D),"&","&\f"),"&\f")!=-1&&(j=-1);break}case 34:case 39:case 91:Y+=$s(D);break;case 9:case 10:case 13:case 32:Y+=sC(C);break;case 92:Y+=uC(Bs()-1,7);continue;case 47:switch(Jn()){case 42:case 47:Os(pC(cC(vn(),Bs()),r,o),m);break;default:Y+="/"}break;case 123*w:p[h++]=Xn(Y)*j;case 125*w:case 59:case 0:switch(D){case 0:case 125:M=0;case 59+y:j==-1&&(Y=Vt(Y,/\f/g,"")),R>0&&Xn(Y)-b&&Os(R>32?gy(Y+";",i,o,b-1):gy(Vt(Y," ","")+";",i,o,b-2),m);break;case 59:Y+=";";default:if(Os(L=hy(Y,r,o,h,y,s,p,A,O=[],_=[],b),c),D===123)if(y===0)Ls(Y,r,L,L,O,c,b,p,_);else switch(x===99&&Ie(Y,3)===110?100:x){case 100:case 108:case 109:case 115:Ls(n,L,L,i&&Os(hy(n,L,L,0,0,s,p,A,s,O=[],b),_),s,_,b,p,i?O:_);break;default:Ls(Y,L,L,L,[""],_,0,p,_)}}h=y=R=0,w=j=1,A=Y="",b=d;break;case 58:b=1+Xn(Y),R=C;default:if(w<1){if(D==123)--w;else if(D==125&&w++==0&&iC()==125)continue}switch(Y+=eu(D),D*w){case 38:j=y>0?1:(Y+="\f",-1);break;case 44:p[h++]=(Xn(Y)-1)*j,j=1;break;case 64:Jn()===45&&(Y+=$s(vn())),x=Jn(),y=b=Xn(A=Y+=fC(Bs())),D++;break;case 45:C===45&&Xn(Y)==2&&(w=0)}}return c}function hy(n,r,o,i,s,c,d,p,m,h,y){for(var b=s-1,x=s===0?c:[""],R=Nd(x),C=0,w=0,M=0;C<i;++C)for(var j=0,D=ti(n,b+1,b=eC(w=d[C])),A=n;j<R;++j)(A=J0(w>0?x[j]+" "+D:Vt(D,/&\f/g,x[j])))&&(m[M++]=A);return au(n,r,o,s===0?_d:p,m,h,y)}function pC(n,r,o){return au(n,r,o,W0,eu(lC()),ti(n,2,-2),0)}function gy(n,r,o,i){return au(n,r,o,jd,ti(n,0,i),ti(n,i+1,-1),i)}function wo(n,r){for(var o="",i=Nd(n),s=0;s<i;s++)o+=r(n[s],s,n,r)||"";return o}function mC(n,r,o,i){switch(n.type){case tC:if(n.children.length)break;case Jx:case jd:return n.return=n.return||n.value;case W0:return"";case Z0:return n.return=n.value+"{"+wo(n.children,i)+"}";case _d:n.value=n.props.join(",")}return Xn(o=wo(n.children,i))?n.return=n.value+"{"+o+"}":""}function hC(n){var r=Nd(n);return function(o,i,s,c){for(var d="",p=0;p<r;p++)d+=n[p](o,i,s,c)||"";return d}}function gC(n){return function(r){r.root||(r=r.return)&&n(r)}}function av(n){var r=Object.create(null);return function(o){return r[o]===void 0&&(r[o]=n(o)),r[o]}}var yC=function(r,o,i){for(var s=0,c=0;s=c,c=Jn(),s===38&&c===12&&(o[i]=1),!ei(c);)vn();return li(r,sn)},vC=function(r,o){var i=-1,s=44;do switch(ei(s)){case 0:s===38&&Jn()===12&&(o[i]=1),r[i]+=yC(sn-1,o,i);break;case 2:r[i]+=$s(s);break;case 4:if(s===44){r[++i]=Jn()===58?"&\f":"",o[i]=r[i].length;break}default:r[i]+=eu(s)}while(s=vn());return r},bC=function(r,o){return nv(vC(ev(r),o))},yy=new WeakMap,SC=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var o=r.value,i=r.parent,s=r.column===i.column&&r.line===i.line;i.type!=="rule";)if(i=i.parent,!i)return;if(!(r.props.length===1&&o.charCodeAt(0)!==58&&!yy.get(i))&&!s){yy.set(r,!0);for(var c=[],d=bC(o,c),p=i.props,m=0,h=0;m<d.length;m++)for(var y=0;y<p.length;y++,h++)r.props[h]=c[m]?d[m].replace(/&\f/g,p[y]):p[y]+" "+d[m]}}},xC=function(r){if(r.type==="decl"){var o=r.value;o.charCodeAt(0)===108&&o.charCodeAt(2)===98&&(r.return="",r.value="")}};function rv(n,r){switch(aC(n,r)){case 5103:return It+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return It+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return It+n+Ys+n+Qe+n+n;case 6828:case 4268:return It+n+Qe+n+n;case 6165:return It+n+Qe+"flex-"+n+n;case 5187:return It+n+Vt(n,/(\w+).+(:[^]+)/,It+"box-$1$2"+Qe+"flex-$1$2")+n;case 5443:return It+n+Qe+"flex-item-"+Vt(n,/flex-|-self/,"")+n;case 4675:return It+n+Qe+"flex-line-pack"+Vt(n,/align-content|flex-|-self/,"")+n;case 5548:return It+n+Qe+Vt(n,"shrink","negative")+n;case 5292:return It+n+Qe+Vt(n,"basis","preferred-size")+n;case 6060:return It+"box-"+Vt(n,"-grow","")+It+n+Qe+Vt(n,"grow","positive")+n;case 4554:return It+Vt(n,/([^-])(transform)/g,"$1"+It+"$2")+n;case 6187:return Vt(Vt(Vt(n,/(zoom-|grab)/,It+"$1"),/(image-set)/,It+"$1"),n,"")+n;case 5495:case 3959:return Vt(n,/(image-set\([^]*)/,It+"$1$`$1");case 4968:return Vt(Vt(n,/(.+:)(flex-)?(.*)/,It+"box-pack:$3"+Qe+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+It+n+n;case 4095:case 3583:case 4068:case 2532:return Vt(n,/(.+)-inline(.+)/,It+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Xn(n)-1-r>6)switch(Ie(n,r+1)){case 109:if(Ie(n,r+4)!==45)break;case 102:return Vt(n,/(.+:)(.+)-([^]+)/,"$1"+It+"$2-$3$1"+Ys+(Ie(n,r+3)==108?"$3":"$2-$3"))+n;case 115:return~od(n,"stretch")?rv(Vt(n,"stretch","fill-available"),r)+n:n}break;case 4949:if(Ie(n,r+1)!==115)break;case 6444:switch(Ie(n,Xn(n)-3-(~od(n,"!important")&&10))){case 107:return Vt(n,":",":"+It)+n;case 101:return Vt(n,/(.+:)([^;!]+)(;|!.+)?/,"$1"+It+(Ie(n,14)===45?"inline-":"")+"box$3$1"+It+"$2$3$1"+Qe+"$2box$3")+n}break;case 5936:switch(Ie(n,r+11)){case 114:return It+n+Qe+Vt(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return It+n+Qe+Vt(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return It+n+Qe+Vt(n,/[svh]\w+-[tblr]{2}/,"lr")+n}return It+n+Qe+n+n}return n}var CC=function(r,o,i,s){if(r.length>-1&&!r.return)switch(r.type){case jd:r.return=rv(r.value,r.length);break;case Z0:return wo([Ll(r,{value:Vt(r.value,"@","@"+It)})],s);case _d:if(r.length)return oC(r.props,function(c){switch(rC(c,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return wo([Ll(r,{props:[Vt(c,/:(read-\w+)/,":"+Ys+"$1")]})],s);case"::placeholder":return wo([Ll(r,{props:[Vt(c,/:(plac\w+)/,":"+It+"input-$1")]}),Ll(r,{props:[Vt(c,/:(plac\w+)/,":"+Ys+"$1")]}),Ll(r,{props:[Vt(c,/:(plac\w+)/,Qe+"input-$1")]})],s)}return""})}},EC=[CC],TC=function(r){var o=r.key;if(o==="css"){var i=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(i,function(w){var M=w.getAttribute("data-emotion");M.indexOf(" ")!==-1&&(document.head.appendChild(w),w.setAttribute("data-s",""))})}var s=r.stylisPlugins||EC,c={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+o+' "]'),function(w){for(var M=w.getAttribute("data-emotion").split(" "),j=1;j<M.length;j++)c[M[j]]=!0;p.push(w)});var m,h=[SC,xC];{var y,b=[mC,gC(function(w){y.insert(w)})],x=hC(h.concat(s,b)),R=function(M){return wo(dC(M),x)};m=function(M,j,D,A){y=D,R(M?M+"{"+j.styles+"}":j.styles),A&&(C.inserted[j.name]=!0)}}var C={key:o,sheet:new Zx({key:o,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:c,registered:{},insert:m};return C.sheet.hydrate(p),C},Pf={exports:{}},Ft={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vy;function RC(){if(vy)return Ft;vy=1;var n=typeof Symbol=="function"&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,s=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,p=n?Symbol.for("react.context"):60110,m=n?Symbol.for("react.async_mode"):60111,h=n?Symbol.for("react.concurrent_mode"):60111,y=n?Symbol.for("react.forward_ref"):60112,b=n?Symbol.for("react.suspense"):60113,x=n?Symbol.for("react.suspense_list"):60120,R=n?Symbol.for("react.memo"):60115,C=n?Symbol.for("react.lazy"):60116,w=n?Symbol.for("react.block"):60121,M=n?Symbol.for("react.fundamental"):60117,j=n?Symbol.for("react.responder"):60118,D=n?Symbol.for("react.scope"):60119;function A(_){if(typeof _=="object"&&_!==null){var L=_.$$typeof;switch(L){case r:switch(_=_.type,_){case m:case h:case i:case c:case s:case b:return _;default:switch(_=_&&_.$$typeof,_){case p:case y:case C:case R:case d:return _;default:return L}}case o:return L}}}function O(_){return A(_)===h}return Ft.AsyncMode=m,Ft.ConcurrentMode=h,Ft.ContextConsumer=p,Ft.ContextProvider=d,Ft.Element=r,Ft.ForwardRef=y,Ft.Fragment=i,Ft.Lazy=C,Ft.Memo=R,Ft.Portal=o,Ft.Profiler=c,Ft.StrictMode=s,Ft.Suspense=b,Ft.isAsyncMode=function(_){return O(_)||A(_)===m},Ft.isConcurrentMode=O,Ft.isContextConsumer=function(_){return A(_)===p},Ft.isContextProvider=function(_){return A(_)===d},Ft.isElement=function(_){return typeof _=="object"&&_!==null&&_.$$typeof===r},Ft.isForwardRef=function(_){return A(_)===y},Ft.isFragment=function(_){return A(_)===i},Ft.isLazy=function(_){return A(_)===C},Ft.isMemo=function(_){return A(_)===R},Ft.isPortal=function(_){return A(_)===o},Ft.isProfiler=function(_){return A(_)===c},Ft.isStrictMode=function(_){return A(_)===s},Ft.isSuspense=function(_){return A(_)===b},Ft.isValidElementType=function(_){return typeof _=="string"||typeof _=="function"||_===i||_===h||_===c||_===s||_===b||_===x||typeof _=="object"&&_!==null&&(_.$$typeof===C||_.$$typeof===R||_.$$typeof===d||_.$$typeof===p||_.$$typeof===y||_.$$typeof===M||_.$$typeof===j||_.$$typeof===D||_.$$typeof===w)},Ft.typeOf=A,Ft}var by;function wC(){return by||(by=1,Pf.exports=RC()),Pf.exports}var qf,Sy;function MC(){if(Sy)return qf;Sy=1;var n=wC(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};c[n.ForwardRef]=i,c[n.Memo]=s;function d(C){return n.isMemo(C)?s:c[C.$$typeof]||r}var p=Object.defineProperty,m=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,y=Object.getOwnPropertyDescriptor,b=Object.getPrototypeOf,x=Object.prototype;function R(C,w,M){if(typeof w!="string"){if(x){var j=b(w);j&&j!==x&&R(C,j,M)}var D=m(w);h&&(D=D.concat(h(w)));for(var A=d(C),O=d(w),_=0;_<D.length;++_){var L=D[_];if(!o[L]&&!(M&&M[L])&&!(O&&O[L])&&!(A&&A[L])){var Y=y(w,L);try{p(C,L,Y)}catch{}}}}return C}return qf=R,qf}MC();var AC=!0;function ov(n,r,o){var i="";return o.split(" ").forEach(function(s){n[s]!==void 0?r.push(n[s]+";"):s&&(i+=s+" ")}),i}var zd=function(r,o,i){var s=r.key+"-"+o.name;(i===!1||AC===!1)&&r.registered[s]===void 0&&(r.registered[s]=o.styles)},Dd=function(r,o,i){zd(r,o,i);var s=r.key+"-"+o.name;if(r.inserted[o.name]===void 0){var c=o;do r.insert(o===c?"."+s:"",c,r.sheet,!0),c=c.next;while(c!==void 0)}};function OC(n){for(var r=0,o,i=0,s=n.length;s>=4;++i,s-=4)o=n.charCodeAt(i)&255|(n.charCodeAt(++i)&255)<<8|(n.charCodeAt(++i)&255)<<16|(n.charCodeAt(++i)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,r=(o&65535)*1540483477+((o>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(s){case 3:r^=(n.charCodeAt(i+2)&255)<<16;case 2:r^=(n.charCodeAt(i+1)&255)<<8;case 1:r^=n.charCodeAt(i)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var _C={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},jC=/[A-Z]|^ms/g,NC=/_EMO_([^_]+?)_([^]*?)_EMO_/g,lv=function(r){return r.charCodeAt(1)===45},xy=function(r){return r!=null&&typeof r!="boolean"},Gf=av(function(n){return lv(n)?n:n.replace(jC,"-$&").toLowerCase()}),Cy=function(r,o){switch(r){case"animation":case"animationName":if(typeof o=="string")return o.replace(NC,function(i,s,c){return Kn={name:s,styles:c,next:Kn},s})}return _C[r]!==1&&!lv(r)&&typeof o=="number"&&o!==0?o+"px":o};function ni(n,r,o){if(o==null)return"";var i=o;if(i.__emotion_styles!==void 0)return i;switch(typeof o){case"boolean":return"";case"object":{var s=o;if(s.anim===1)return Kn={name:s.name,styles:s.styles,next:Kn},s.name;var c=o;if(c.styles!==void 0){var d=c.next;if(d!==void 0)for(;d!==void 0;)Kn={name:d.name,styles:d.styles,next:Kn},d=d.next;var p=c.styles+";";return p}return zC(n,r,o)}case"function":{if(n!==void 0){var m=Kn,h=o(n);return Kn=m,ni(n,r,h)}break}}var y=o;if(r==null)return y;var b=r[y];return b!==void 0?b:y}function zC(n,r,o){var i="";if(Array.isArray(o))for(var s=0;s<o.length;s++)i+=ni(n,r,o[s])+";";else for(var c in o){var d=o[c];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?i+=c+"{"+r[p]+"}":xy(p)&&(i+=Gf(c)+":"+Cy(c,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var m=0;m<d.length;m++)xy(d[m])&&(i+=Gf(c)+":"+Cy(c,d[m])+";");else{var h=ni(n,r,d);switch(c){case"animation":case"animationName":{i+=Gf(c)+":"+h+";";break}default:i+=c+"{"+h+"}"}}}return i}var Ey=/label:\s*([^\s;{]+)\s*(;|$)/g,Kn;function ii(n,r,o){if(n.length===1&&typeof n[0]=="object"&&n[0]!==null&&n[0].styles!==void 0)return n[0];var i=!0,s="";Kn=void 0;var c=n[0];if(c==null||c.raw===void 0)i=!1,s+=ni(o,r,c);else{var d=c;s+=d[0]}for(var p=1;p<n.length;p++)if(s+=ni(o,r,n[p]),i){var m=c;s+=m[p]}Ey.lastIndex=0;for(var h="",y;(y=Ey.exec(s))!==null;)h+="-"+y[1];var b=OC(s)+h;return{name:b,styles:s,next:Kn}}var DC=function(r){return r()},iv=Gs.useInsertionEffect?Gs.useInsertionEffect:!1,sv=iv||DC,Ty=iv||S.useLayoutEffect,uv=S.createContext(typeof HTMLElement<"u"?TC({key:"css"}):null);uv.Provider;var kd=function(r){return S.forwardRef(function(o,i){var s=S.useContext(uv);return r(o,s,i)})},si=S.createContext({}),Bd={}.hasOwnProperty,id="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",kC=function(r,o){var i={};for(var s in o)Bd.call(o,s)&&(i[s]=o[s]);return i[id]=r,i},BC=function(r){var o=r.cache,i=r.serialized,s=r.isStringTag;return zd(o,i,s),sv(function(){return Dd(o,i,s)}),null},$C=kd(function(n,r,o){var i=n.css;typeof i=="string"&&r.registered[i]!==void 0&&(i=r.registered[i]);var s=n[id],c=[i],d="";typeof n.className=="string"?d=ov(r.registered,c,n.className):n.className!=null&&(d=n.className+" ");var p=ii(c,void 0,S.useContext(si));d+=r.key+"-"+p.name;var m={};for(var h in n)Bd.call(n,h)&&h!=="css"&&h!==id&&(m[h]=n[h]);return m.className=d,o&&(m.ref=o),S.createElement(S.Fragment,null,S.createElement(BC,{cache:r,serialized:p,isStringTag:typeof s=="string"}),S.createElement(s,m))}),LC=$C,Ry=function(r,o){var i=arguments;if(o==null||!Bd.call(o,"css"))return S.createElement.apply(void 0,i);var s=i.length,c=new Array(s);c[0]=LC,c[1]=kC(r,o);for(var d=2;d<s;d++)c[d]=i[d];return S.createElement.apply(null,c)};(function(n){var r;r||(r=n.JSX||(n.JSX={}))})(Ry||(Ry={}));var UC=kd(function(n,r){var o=n.styles,i=ii([o],void 0,S.useContext(si)),s=S.useRef();return Ty(function(){var c=r.key+"-global",d=new r.sheet.constructor({key:c,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,m=document.querySelector('style[data-emotion="'+c+" "+i.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),m!==null&&(p=!0,m.setAttribute("data-emotion",c),d.hydrate([m])),s.current=[d,p],function(){d.flush()}},[r]),Ty(function(){var c=s.current,d=c[0],p=c[1];if(p){c[1]=!1;return}if(i.next!==void 0&&Dd(r,i.next,!0),d.tags.length){var m=d.tags[d.tags.length-1].nextElementSibling;d.before=m,d.flush()}r.insert("",i,d,!1)},[r,i.name]),null});function $d(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return ii(r)}function ui(){var n=$d.apply(void 0,arguments),r="animation-"+n.name;return{name:r,styles:"@keyframes "+r+"{"+n.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var HC=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,PC=av(function(n){return HC.test(n)||n.charCodeAt(0)===111&&n.charCodeAt(1)===110&&n.charCodeAt(2)<91}),qC=PC,GC=function(r){return r!=="theme"},wy=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?qC:GC},My=function(r,o,i){var s;if(o){var c=o.shouldForwardProp;s=r.__emotion_forwardProp&&c?function(d){return r.__emotion_forwardProp(d)&&c(d)}:c}return typeof s!="function"&&i&&(s=r.__emotion_forwardProp),s},IC=function(r){var o=r.cache,i=r.serialized,s=r.isStringTag;return zd(o,i,s),sv(function(){return Dd(o,i,s)}),null},VC=function n(r,o){var i=r.__emotion_real===r,s=i&&r.__emotion_base||r,c,d;o!==void 0&&(c=o.label,d=o.target);var p=My(r,o,i),m=p||wy(s),h=!m("as");return function(){var y=arguments,b=i&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(c!==void 0&&b.push("label:"+c+";"),y[0]==null||y[0].raw===void 0)b.push.apply(b,y);else{var x=y[0];b.push(x[0]);for(var R=y.length,C=1;C<R;C++)b.push(y[C],x[C])}var w=kd(function(M,j,D){var A=h&&M.as||s,O="",_=[],L=M;if(M.theme==null){L={};for(var Y in M)L[Y]=M[Y];L.theme=S.useContext(si)}typeof M.className=="string"?O=ov(j.registered,_,M.className):M.className!=null&&(O=M.className+" ");var F=ii(b.concat(_),j.registered,L);O+=j.key+"-"+F.name,d!==void 0&&(O+=" "+d);var K=h&&p===void 0?wy(A):m,E={};for(var U in M)h&&U==="as"||K(U)&&(E[U]=M[U]);return E.className=O,D&&(E.ref=D),S.createElement(S.Fragment,null,S.createElement(IC,{cache:j,serialized:F,isStringTag:typeof A=="string"}),S.createElement(A,E))});return w.displayName=c!==void 0?c:"Styled("+(typeof s=="string"?s:s.displayName||s.name||"Component")+")",w.defaultProps=r.defaultProps,w.__emotion_real=w,w.__emotion_base=s,w.__emotion_styles=b,w.__emotion_forwardProp=p,Object.defineProperty(w,"toString",{value:function(){return"."+d}}),w.withComponent=function(M,j){var D=n(M,Vs({},o,j,{shouldForwardProp:My(w,j,!0)}));return D.apply(void 0,b)},w}},YC=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],sd=VC.bind(null);YC.forEach(function(n){sd[n]=sd(n)});function FC(n){return n==null||Object.keys(n).length===0}function cv(n){const{styles:r,defaultTheme:o={}}=n,i=typeof r=="function"?s=>r(FC(s)?o:s):r;return g.jsx(UC,{styles:i})}function fv(n,r){return sd(n,r)}function XC(n,r){Array.isArray(n.__emotion_styles)&&(n.__emotion_styles=r(n.__emotion_styles))}const Ay=[];function Qa(n){return Ay[0]=n,ii(Ay)}var If={exports:{}},ee={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oy;function KC(){if(Oy)return ee;Oy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),x=Symbol.for("react.view_transition"),R=Symbol.for("react.client.reference");function C(w){if(typeof w=="object"&&w!==null){var M=w.$$typeof;switch(M){case n:switch(w=w.type,w){case o:case s:case i:case m:case h:case x:return w;default:switch(w=w&&w.$$typeof,w){case d:case p:case b:case y:return w;case c:return w;default:return M}}case r:return M}}}return ee.ContextConsumer=c,ee.ContextProvider=d,ee.Element=n,ee.ForwardRef=p,ee.Fragment=o,ee.Lazy=b,ee.Memo=y,ee.Portal=r,ee.Profiler=s,ee.StrictMode=i,ee.Suspense=m,ee.SuspenseList=h,ee.isContextConsumer=function(w){return C(w)===c},ee.isContextProvider=function(w){return C(w)===d},ee.isElement=function(w){return typeof w=="object"&&w!==null&&w.$$typeof===n},ee.isForwardRef=function(w){return C(w)===p},ee.isFragment=function(w){return C(w)===o},ee.isLazy=function(w){return C(w)===b},ee.isMemo=function(w){return C(w)===y},ee.isPortal=function(w){return C(w)===r},ee.isProfiler=function(w){return C(w)===s},ee.isStrictMode=function(w){return C(w)===i},ee.isSuspense=function(w){return C(w)===m},ee.isSuspenseList=function(w){return C(w)===h},ee.isValidElementType=function(w){return typeof w=="string"||typeof w=="function"||w===o||w===s||w===i||w===m||w===h||typeof w=="object"&&w!==null&&(w.$$typeof===b||w.$$typeof===y||w.$$typeof===d||w.$$typeof===c||w.$$typeof===p||w.$$typeof===R||w.getModuleId!==void 0)},ee.typeOf=C,ee}var _y;function QC(){return _y||(_y=1,If.exports=KC()),If.exports}var dv=QC();function Qn(n){if(typeof n!="object"||n===null)return!1;const r=Object.getPrototypeOf(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function pv(n){if(S.isValidElement(n)||dv.isValidElementType(n)||!Qn(n))return n;const r={};return Object.keys(n).forEach(o=>{r[o]=pv(n[o])}),r}function We(n,r,o={clone:!0}){const i=o.clone?{...n}:n;return Qn(n)&&Qn(r)&&Object.keys(r).forEach(s=>{S.isValidElement(r[s])||dv.isValidElementType(r[s])?i[s]=r[s]:Qn(r[s])&&Object.prototype.hasOwnProperty.call(n,s)&&Qn(n[s])?i[s]=We(n[s],r[s],o):o.clone?i[s]=Qn(r[s])?pv(r[s]):r[s]:i[s]=r[s]}),i}const WC=n=>{const r=Object.keys(n).map(o=>({key:o,val:n[o]}))||[];return r.sort((o,i)=>o.val-i.val),r.reduce((o,i)=>({...o,[i.key]:i.val}),{})};function ZC(n){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:o="px",step:i=5,...s}=n,c=WC(r),d=Object.keys(c);function p(x){return`@media (min-width:${typeof r[x]=="number"?r[x]:x}${o})`}function m(x){return`@media (max-width:${(typeof r[x]=="number"?r[x]:x)-i/100}${o})`}function h(x,R){const C=d.indexOf(R);return`@media (min-width:${typeof r[x]=="number"?r[x]:x}${o}) and (max-width:${(C!==-1&&typeof r[d[C]]=="number"?r[d[C]]:R)-i/100}${o})`}function y(x){return d.indexOf(x)+1<d.length?h(x,d[d.indexOf(x)+1]):p(x)}function b(x){const R=d.indexOf(x);return R===0?p(d[1]):R===d.length-1?m(d[R]):h(x,d[d.indexOf(x)+1]).replace("@media","@media not all and")}return{keys:d,values:c,up:p,down:m,between:h,only:y,not:b,unit:o,...s}}function jy(n,r){if(!n.containerQueries)return r;const o=Object.keys(r).filter(i=>i.startsWith("@container")).sort((i,s)=>{var d,p;const c=/min-width:\s*([0-9.]+)/;return+(((d=i.match(c))==null?void 0:d[1])||0)-+(((p=s.match(c))==null?void 0:p[1])||0)});return o.length?o.reduce((i,s)=>{const c=r[s];return delete i[s],i[s]=c,i},{...r}):r}function JC(n,r){return r==="@"||r.startsWith("@")&&(n.some(o=>r.startsWith(`@${o}`))||!!r.match(/^@\d/))}function t2(n,r){const o=r.match(/^@([^/]+)?\/?(.+)?$/);if(!o)return null;const[,i,s]=o,c=Number.isNaN(+i)?i||0:+i;return n.containerQueries(s).up(c)}function e2(n){const r=(c,d)=>c.replace("@media",d?`@container ${d}`:"@container");function o(c,d){c.up=(...p)=>r(n.breakpoints.up(...p),d),c.down=(...p)=>r(n.breakpoints.down(...p),d),c.between=(...p)=>r(n.breakpoints.between(...p),d),c.only=(...p)=>r(n.breakpoints.only(...p),d),c.not=(...p)=>{const m=r(n.breakpoints.not(...p),d);return m.includes("not all and")?m.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):m}}const i={},s=c=>(o(i,c),i);return o(s),{...n,containerQueries:s}}const n2={borderRadius:4};function Fl(n,r){return r?We(n,r,{clone:!1}):n}const ru={xs:0,sm:600,md:900,lg:1200,xl:1536},Ny={keys:["xs","sm","md","lg","xl"],up:n=>`@media (min-width:${ru[n]}px)`},a2={containerQueries:n=>({up:r=>{let o=typeof r=="number"?r:ru[r]||r;return typeof o=="number"&&(o=`${o}px`),n?`@container ${n} (min-width:${o})`:`@container (min-width:${o})`}})};function xa(n,r,o){const i=n.theme||{};if(Array.isArray(r)){const c=i.breakpoints||Ny;return r.reduce((d,p,m)=>(d[c.up(c.keys[m])]=o(r[m]),d),{})}if(typeof r=="object"){const c=i.breakpoints||Ny;return Object.keys(r).reduce((d,p)=>{if(JC(c.keys,p)){const m=t2(i.containerQueries?i:a2,p);m&&(d[m]=o(r[p],p))}else if(Object.keys(c.values||ru).includes(p)){const m=c.up(p);d[m]=o(r[p],p)}else{const m=p;d[m]=r[m]}return d},{})}return o(r)}function r2(n={}){var o;return((o=n.keys)==null?void 0:o.reduce((i,s)=>{const c=n.up(s);return i[c]={},i},{}))||{}}function zy(n,r){return n.reduce((o,i)=>{const s=o[i];return(!s||Object.keys(s).length===0)&&delete o[i],o},r)}function ft(n){if(typeof n!="string")throw new Error(Sa(7));return n.charAt(0).toUpperCase()+n.slice(1)}function ou(n,r,o=!0){if(!r||typeof r!="string")return null;if(n&&n.vars&&o){const i=`vars.${r}`.split(".").reduce((s,c)=>s&&s[c]?s[c]:null,n);if(i!=null)return i}return r.split(".").reduce((i,s)=>i&&i[s]!=null?i[s]:null,n)}function Fs(n,r,o,i=o){let s;return typeof n=="function"?s=n(o):Array.isArray(n)?s=n[o]||i:s=ou(n,o)||i,r&&(s=r(s,i,n)),s}function Ee(n){const{prop:r,cssProperty:o=n.prop,themeKey:i,transform:s}=n,c=d=>{if(d[r]==null)return null;const p=d[r],m=d.theme,h=ou(m,i)||{};return xa(d,p,b=>{let x=Fs(h,s,b);return b===x&&typeof b=="string"&&(x=Fs(h,s,`${r}${b==="default"?"":ft(b)}`,b)),o===!1?x:{[o]:x}})};return c.propTypes={},c.filterProps=[r],c}function o2(n){const r={};return o=>(r[o]===void 0&&(r[o]=n(o)),r[o])}const l2={m:"margin",p:"padding"},i2={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Dy={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},s2=o2(n=>{if(n.length>2)if(Dy[n])n=Dy[n];else return[n];const[r,o]=n.split(""),i=l2[r],s=i2[o]||"";return Array.isArray(s)?s.map(c=>i+c):[i+s]}),Ld=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Ud=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Ld,...Ud];function ci(n,r,o,i){const s=ou(n,r,!0)??o;return typeof s=="number"||typeof s=="string"?c=>typeof c=="string"?c:typeof s=="string"?s.startsWith("var(")&&c===0?0:s.startsWith("var(")&&c===1?s:`calc(${c} * ${s})`:s*c:Array.isArray(s)?c=>{if(typeof c=="string")return c;const d=Math.abs(c),p=s[d];return c>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof s=="function"?s:()=>{}}function Hd(n){return ci(n,"spacing",8)}function fi(n,r){return typeof r=="string"||r==null?r:n(r)}function u2(n,r){return o=>n.reduce((i,s)=>(i[s]=fi(r,o),i),{})}function c2(n,r,o,i){if(!r.includes(o))return null;const s=s2(o),c=u2(s,i),d=n[o];return xa(n,d,c)}function mv(n,r){const o=Hd(n.theme);return Object.keys(n).map(i=>c2(n,r,i,o)).reduce(Fl,{})}function be(n){return mv(n,Ld)}be.propTypes={};be.filterProps=Ld;function Se(n){return mv(n,Ud)}Se.propTypes={};Se.filterProps=Ud;function hv(n=8,r=Hd({spacing:n})){if(n.mui)return n;const o=(...i)=>(i.length===0?[1]:i).map(c=>{const d=r(c);return typeof d=="number"?`${d}px`:d}).join(" ");return o.mui=!0,o}function lu(...n){const r=n.reduce((i,s)=>(s.filterProps.forEach(c=>{i[c]=s}),i),{}),o=i=>Object.keys(i).reduce((s,c)=>r[c]?Fl(s,r[c](i)):s,{});return o.propTypes={},o.filterProps=n.reduce((i,s)=>i.concat(s.filterProps),[]),o}function zn(n){return typeof n!="number"?n:`${n}px solid`}function Dn(n,r){return Ee({prop:n,themeKey:"borders",transform:r})}const f2=Dn("border",zn),d2=Dn("borderTop",zn),p2=Dn("borderRight",zn),m2=Dn("borderBottom",zn),h2=Dn("borderLeft",zn),g2=Dn("borderColor"),y2=Dn("borderTopColor"),v2=Dn("borderRightColor"),b2=Dn("borderBottomColor"),S2=Dn("borderLeftColor"),x2=Dn("outline",zn),C2=Dn("outlineColor"),iu=n=>{if(n.borderRadius!==void 0&&n.borderRadius!==null){const r=ci(n.theme,"shape.borderRadius",4),o=i=>({borderRadius:fi(r,i)});return xa(n,n.borderRadius,o)}return null};iu.propTypes={};iu.filterProps=["borderRadius"];lu(f2,d2,p2,m2,h2,g2,y2,v2,b2,S2,iu,x2,C2);const su=n=>{if(n.gap!==void 0&&n.gap!==null){const r=ci(n.theme,"spacing",8),o=i=>({gap:fi(r,i)});return xa(n,n.gap,o)}return null};su.propTypes={};su.filterProps=["gap"];const uu=n=>{if(n.columnGap!==void 0&&n.columnGap!==null){const r=ci(n.theme,"spacing",8),o=i=>({columnGap:fi(r,i)});return xa(n,n.columnGap,o)}return null};uu.propTypes={};uu.filterProps=["columnGap"];const cu=n=>{if(n.rowGap!==void 0&&n.rowGap!==null){const r=ci(n.theme,"spacing",8),o=i=>({rowGap:fi(r,i)});return xa(n,n.rowGap,o)}return null};cu.propTypes={};cu.filterProps=["rowGap"];const E2=Ee({prop:"gridColumn"}),T2=Ee({prop:"gridRow"}),R2=Ee({prop:"gridAutoFlow"}),w2=Ee({prop:"gridAutoColumns"}),M2=Ee({prop:"gridAutoRows"}),A2=Ee({prop:"gridTemplateColumns"}),O2=Ee({prop:"gridTemplateRows"}),_2=Ee({prop:"gridTemplateAreas"}),j2=Ee({prop:"gridArea"});lu(su,uu,cu,E2,T2,R2,w2,M2,A2,O2,_2,j2);function Mo(n,r){return r==="grey"?r:n}const N2=Ee({prop:"color",themeKey:"palette",transform:Mo}),z2=Ee({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Mo}),D2=Ee({prop:"backgroundColor",themeKey:"palette",transform:Mo});lu(N2,z2,D2);function yn(n){return n<=1&&n!==0?`${n*100}%`:n}const k2=Ee({prop:"width",transform:yn}),Pd=n=>{if(n.maxWidth!==void 0&&n.maxWidth!==null){const r=o=>{var s,c,d,p,m;const i=((d=(c=(s=n.theme)==null?void 0:s.breakpoints)==null?void 0:c.values)==null?void 0:d[o])||ru[o];return i?((m=(p=n.theme)==null?void 0:p.breakpoints)==null?void 0:m.unit)!=="px"?{maxWidth:`${i}${n.theme.breakpoints.unit}`}:{maxWidth:i}:{maxWidth:yn(o)}};return xa(n,n.maxWidth,r)}return null};Pd.filterProps=["maxWidth"];const B2=Ee({prop:"minWidth",transform:yn}),$2=Ee({prop:"height",transform:yn}),L2=Ee({prop:"maxHeight",transform:yn}),U2=Ee({prop:"minHeight",transform:yn});Ee({prop:"size",cssProperty:"width",transform:yn});Ee({prop:"size",cssProperty:"height",transform:yn});const H2=Ee({prop:"boxSizing"});lu(k2,Pd,B2,$2,L2,U2,H2);const di={border:{themeKey:"borders",transform:zn},borderTop:{themeKey:"borders",transform:zn},borderRight:{themeKey:"borders",transform:zn},borderBottom:{themeKey:"borders",transform:zn},borderLeft:{themeKey:"borders",transform:zn},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:zn},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:iu},color:{themeKey:"palette",transform:Mo},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Mo},backgroundColor:{themeKey:"palette",transform:Mo},p:{style:Se},pt:{style:Se},pr:{style:Se},pb:{style:Se},pl:{style:Se},px:{style:Se},py:{style:Se},padding:{style:Se},paddingTop:{style:Se},paddingRight:{style:Se},paddingBottom:{style:Se},paddingLeft:{style:Se},paddingX:{style:Se},paddingY:{style:Se},paddingInline:{style:Se},paddingInlineStart:{style:Se},paddingInlineEnd:{style:Se},paddingBlock:{style:Se},paddingBlockStart:{style:Se},paddingBlockEnd:{style:Se},m:{style:be},mt:{style:be},mr:{style:be},mb:{style:be},ml:{style:be},mx:{style:be},my:{style:be},margin:{style:be},marginTop:{style:be},marginRight:{style:be},marginBottom:{style:be},marginLeft:{style:be},marginX:{style:be},marginY:{style:be},marginInline:{style:be},marginInlineStart:{style:be},marginInlineEnd:{style:be},marginBlock:{style:be},marginBlockStart:{style:be},marginBlockEnd:{style:be},displayPrint:{cssProperty:!1,transform:n=>({"@media print":{display:n}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:su},rowGap:{style:cu},columnGap:{style:uu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:yn},maxWidth:{style:Pd},minWidth:{transform:yn},height:{transform:yn},maxHeight:{transform:yn},minHeight:{transform:yn},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function P2(...n){const r=n.reduce((i,s)=>i.concat(Object.keys(s)),[]),o=new Set(r);return n.every(i=>o.size===Object.keys(i).length)}function q2(n,r){return typeof n=="function"?n(r):n}function G2(){function n(o,i,s,c){const d={[o]:i,theme:s},p=c[o];if(!p)return{[o]:i};const{cssProperty:m=o,themeKey:h,transform:y,style:b}=p;if(i==null)return null;if(h==="typography"&&i==="inherit")return{[o]:i};const x=ou(s,h)||{};return b?b(d):xa(d,i,C=>{let w=Fs(x,y,C);return C===w&&typeof C=="string"&&(w=Fs(x,y,`${o}${C==="default"?"":ft(C)}`,C)),m===!1?w:{[m]:w}})}function r(o){const{sx:i,theme:s={},nested:c}=o||{};if(!i)return null;const d=s.unstable_sxConfig??di;function p(m){let h=m;if(typeof m=="function")h=m(s);else if(typeof m!="object")return m;if(!h)return null;const y=r2(s.breakpoints),b=Object.keys(y);let x=y;return Object.keys(h).forEach(R=>{const C=q2(h[R],s);if(C!=null)if(typeof C=="object")if(d[R])x=Fl(x,n(R,C,s,d));else{const w=xa({theme:s},C,M=>({[R]:M}));P2(w,C)?x[R]=r({sx:C,theme:s,nested:!0}):x=Fl(x,w)}else x=Fl(x,n(R,C,s,d))}),!c&&s.modularCssLayers?{"@layer sx":jy(s,zy(b,x))}:jy(s,zy(b,x))}return Array.isArray(i)?i.map(p):p(i)}return r}const Wa=G2();Wa.filterProps=["sx"];function I2(n,r){var i;const o=this;if(o.vars){if(!((i=o.colorSchemes)!=null&&i[n])||typeof o.getColorSchemeSelector!="function")return{};let s=o.getColorSchemeSelector(n);return s==="&"?r:((s.includes("data-")||s.includes("."))&&(s=`*:where(${s.replace(/\s*&$/,"")}) &`),{[s]:r})}return o.palette.mode===n?r:{}}function pi(n={},...r){const{breakpoints:o={},palette:i={},spacing:s,shape:c={},...d}=n,p=ZC(o),m=hv(s);let h=We({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...i},spacing:m,shape:{...n2,...c}},d);return h=e2(h),h.applyStyles=I2,h=r.reduce((y,b)=>We(y,b),h),h.unstable_sxConfig={...di,...d==null?void 0:d.unstable_sxConfig},h.unstable_sx=function(b){return Wa({sx:b,theme:this})},h}function V2(n){return Object.keys(n).length===0}function qd(n=null){const r=S.useContext(si);return!r||V2(r)?n:r}const Y2=pi();function mi(n=Y2){return qd(n)}function Vf(n){const r=Qa(n);return n!==r&&r.styles?(r.styles.match(/^@layer\s+[^{]*$/)||(r.styles=`@layer global{${r.styles}}`),r):n}function gv({styles:n,themeId:r,defaultTheme:o={}}){const i=mi(o),s=r&&i[r]||i;let c=typeof n=="function"?n(s):n;return s.modularCssLayers&&(Array.isArray(c)?c=c.map(d=>Vf(typeof d=="function"?d(s):d)):c=Vf(c)),g.jsx(cv,{styles:c})}const F2=n=>{var i;const r={systemProps:{},otherProps:{}},o=((i=n==null?void 0:n.theme)==null?void 0:i.unstable_sxConfig)??di;return Object.keys(n).forEach(s=>{o[s]?r.systemProps[s]=n[s]:r.otherProps[s]=n[s]}),r};function Gd(n){const{sx:r,...o}=n,{systemProps:i,otherProps:s}=F2(o);let c;return Array.isArray(r)?c=[i,...r]:typeof r=="function"?c=(...d)=>{const p=r(...d);return Qn(p)?{...i,...p}:i}:c={...i,...r},{...s,sx:c}}const ky=n=>n,X2=()=>{let n=ky;return{configure(r){n=r},generate(r){return n(r)},reset(){n=ky}}},yv=X2();function vv(n){var r,o,i="";if(typeof n=="string"||typeof n=="number")i+=n;else if(typeof n=="object")if(Array.isArray(n)){var s=n.length;for(r=0;r<s;r++)n[r]&&(o=vv(n[r]))&&(i&&(i+=" "),i+=o)}else for(o in n)n[o]&&(i&&(i+=" "),i+=o);return i}function mt(){for(var n,r,o=0,i="",s=arguments.length;o<s;o++)(n=arguments[o])&&(r=vv(n))&&(i&&(i+=" "),i+=r);return i}function K2(n={}){const{themeId:r,defaultTheme:o,defaultClassName:i="MuiBox-root",generateClassName:s}=n,c=fv("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(Wa);return S.forwardRef(function(m,h){const y=mi(o),{className:b,component:x="div",...R}=Gd(m);return g.jsx(c,{as:x,ref:h,className:mt(b,s?s(i):i),theme:r&&y[r]||y,...R})})}const Q2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Mt(n,r,o="Mui"){const i=Q2[r];return i?`${o}-${i}`:`${yv.generate(n)}-${r}`}function Rt(n,r,o="Mui"){const i={};return r.forEach(s=>{i[s]=Mt(n,s,o)}),i}function bv(n){const{variants:r,...o}=n,i={variants:r,style:Qa(o),isProcessed:!0};return i.style===o||r&&r.forEach(s=>{typeof s.style!="function"&&(s.style=Qa(s.style))}),i}const W2=pi();function Yf(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}function Cr(n,r){return r&&n&&typeof n=="object"&&n.styles&&!n.styles.startsWith("@layer")&&(n.styles=`@layer ${r}{${String(n.styles)}}`),n}function Z2(n){return n?(r,o)=>o[n]:null}function J2(n,r,o){n.theme=eE(n.theme)?o:n.theme[r]||n.theme}function Us(n,r,o){const i=typeof r=="function"?r(n):r;if(Array.isArray(i))return i.flatMap(s=>Us(n,s,o));if(Array.isArray(i==null?void 0:i.variants)){let s;if(i.isProcessed)s=o?Cr(i.style,o):i.style;else{const{variants:c,...d}=i;s=o?Cr(Qa(d),o):d}return Sv(n,i.variants,[s],o)}return i!=null&&i.isProcessed?o?Cr(Qa(i.style),o):i.style:o?Cr(Qa(i),o):i}function Sv(n,r,o=[],i=void 0){var c;let s;t:for(let d=0;d<r.length;d+=1){const p=r[d];if(typeof p.props=="function"){if(s??(s={...n,...n.ownerState,ownerState:n.ownerState}),!p.props(s))continue}else for(const m in p.props)if(n[m]!==p.props[m]&&((c=n.ownerState)==null?void 0:c[m])!==p.props[m])continue t;typeof p.style=="function"?(s??(s={...n,...n.ownerState,ownerState:n.ownerState}),o.push(i?Cr(Qa(p.style(s)),i):p.style(s))):o.push(i?Cr(Qa(p.style),i):p.style)}return o}function xv(n={}){const{themeId:r,defaultTheme:o=W2,rootShouldForwardProp:i=Yf,slotShouldForwardProp:s=Yf}=n;function c(p){J2(p,r,o)}return(p,m={})=>{XC(p,L=>L.filter(Y=>Y!==Wa));const{name:h,slot:y,skipVariantsResolver:b,skipSx:x,overridesResolver:R=Z2(aE(y)),...C}=m,w=h&&h.startsWith("Mui")||y?"components":"custom",M=b!==void 0?b:y&&y!=="Root"&&y!=="root"||!1,j=x||!1;let D=Yf;y==="Root"||y==="root"?D=i:y?D=s:nE(p)&&(D=void 0);const A=fv(p,{shouldForwardProp:D,label:tE(),...C}),O=L=>{if(L.__emotion_real===L)return L;if(typeof L=="function")return function(F){return Us(F,L,F.theme.modularCssLayers?w:void 0)};if(Qn(L)){const Y=bv(L);return function(K){return Y.variants?Us(K,Y,K.theme.modularCssLayers?w:void 0):K.theme.modularCssLayers?Cr(Y.style,w):Y.style}}return L},_=(...L)=>{const Y=[],F=L.map(O),K=[];if(Y.push(c),h&&R&&K.push(function(at){var I,ot;const Q=(ot=(I=at.theme.components)==null?void 0:I[h])==null?void 0:ot.styleOverrides;if(!Q)return null;const k={};for(const Z in Q)k[Z]=Us(at,Q[Z],at.theme.modularCssLayers?"theme":void 0);return R(at,k)}),h&&!M&&K.push(function(at){var k,I;const it=at.theme,Q=(I=(k=it==null?void 0:it.components)==null?void 0:k[h])==null?void 0:I.variants;return Q?Sv(at,Q,[],at.theme.modularCssLayers?"theme":void 0):null}),j||K.push(Wa),Array.isArray(F[0])){const X=F.shift(),at=new Array(Y.length).fill(""),it=new Array(K.length).fill("");let Q;Q=[...at,...X,...it],Q.raw=[...at,...X.raw,...it],Y.unshift(Q)}const E=[...Y,...F,...K],U=A(...E);return p.muiName&&(U.muiName=p.muiName),U};return A.withConfig&&(_.withConfig=A.withConfig),_}}function tE(n,r){return void 0}function eE(n){for(const r in n)return!1;return!0}function nE(n){return typeof n=="string"&&n.charCodeAt(0)>96}function aE(n){return n&&n.charAt(0).toLowerCase()+n.slice(1)}const Cv=xv();function ai(n,r,o=!1){const i={...r};for(const s in n)if(Object.prototype.hasOwnProperty.call(n,s)){const c=s;if(c==="components"||c==="slots")i[c]={...n[c],...i[c]};else if(c==="componentsProps"||c==="slotProps"){const d=n[c],p=r[c];if(!p)i[c]=d||{};else if(!d)i[c]=p;else{i[c]={...p};for(const m in d)if(Object.prototype.hasOwnProperty.call(d,m)){const h=m;i[c][h]=ai(d[h],p[h],o)}}}else c==="className"&&o&&r.className?i.className=mt(n==null?void 0:n.className,r==null?void 0:r.className):c==="style"&&o&&r.style?i.style={...n==null?void 0:n.style,...r==null?void 0:r.style}:i[c]===void 0&&(i[c]=n[c])}return i}function rE(n){const{theme:r,name:o,props:i}=n;return!r||!r.components||!r.components[o]||!r.components[o].defaultProps?i:ai(r.components[o].defaultProps,i)}function Ev({props:n,name:r,defaultTheme:o,themeId:i}){let s=mi(o);return i&&(s=s[i]||s),rE({theme:s,name:r,props:n})}const ta=typeof window<"u"?S.useLayoutEffect:S.useEffect;function oE(n,r=Number.MIN_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(n,o))}function Id(n,r=0,o=1){return oE(n,r,o)}function lE(n){n=n.slice(1);const r=new RegExp(`.{1,${n.length>=6?2:1}}`,"g");let o=n.match(r);return o&&o[0].length===1&&(o=o.map(i=>i+i)),o?`rgb${o.length===4?"a":""}(${o.map((i,s)=>s<3?parseInt(i,16):Math.round(parseInt(i,16)/255*1e3)/1e3).join(", ")})`:""}function Za(n){if(n.type)return n;if(n.charAt(0)==="#")return Za(lE(n));const r=n.indexOf("("),o=n.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw new Error(Sa(9,n));let i=n.substring(r+1,n.length-1),s;if(o==="color"){if(i=i.split(" "),s=i.shift(),i.length===4&&i[3].charAt(0)==="/"&&(i[3]=i[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(s))throw new Error(Sa(10,s))}else i=i.split(",");return i=i.map(c=>parseFloat(c)),{type:o,values:i,colorSpace:s}}const iE=n=>{const r=Za(n);return r.values.slice(0,3).map((o,i)=>r.type.includes("hsl")&&i!==0?`${o}%`:o).join(" ")},Gl=(n,r)=>{try{return iE(n)}catch{return n}};function fu(n){const{type:r,colorSpace:o}=n;let{values:i}=n;return r.includes("rgb")?i=i.map((s,c)=>c<3?parseInt(s,10):s):r.includes("hsl")&&(i[1]=`${i[1]}%`,i[2]=`${i[2]}%`),r.includes("color")?i=`${o} ${i.join(" ")}`:i=`${i.join(", ")}`,`${r}(${i})`}function Tv(n){n=Za(n);const{values:r}=n,o=r[0],i=r[1]/100,s=r[2]/100,c=i*Math.min(s,1-s),d=(h,y=(h+o/30)%12)=>s-c*Math.max(Math.min(y-3,9-y,1),-1);let p="rgb";const m=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return n.type==="hsla"&&(p+="a",m.push(r[3])),fu({type:p,values:m})}function ud(n){n=Za(n);let r=n.type==="hsl"||n.type==="hsla"?Za(Tv(n)).values:n.values;return r=r.map(o=>(n.type!=="color"&&(o/=255),o<=.03928?o/12.92:((o+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function sE(n,r){const o=ud(n),i=ud(r);return(Math.max(o,i)+.05)/(Math.min(o,i)+.05)}function Oe(n,r){return n=Za(n),r=Id(r),(n.type==="rgb"||n.type==="hsl")&&(n.type+="a"),n.type==="color"?n.values[3]=`/${r}`:n.values[3]=r,fu(n)}function _s(n,r,o){try{return Oe(n,r)}catch{return n}}function wr(n,r){if(n=Za(n),r=Id(r),n.type.includes("hsl"))n.values[2]*=1-r;else if(n.type.includes("rgb")||n.type.includes("color"))for(let o=0;o<3;o+=1)n.values[o]*=1-r;return fu(n)}function re(n,r,o){try{return wr(n,r)}catch{return n}}function Mr(n,r){if(n=Za(n),r=Id(r),n.type.includes("hsl"))n.values[2]+=(100-n.values[2])*r;else if(n.type.includes("rgb"))for(let o=0;o<3;o+=1)n.values[o]+=(255-n.values[o])*r;else if(n.type.includes("color"))for(let o=0;o<3;o+=1)n.values[o]+=(1-n.values[o])*r;return fu(n)}function oe(n,r,o){try{return Mr(n,r)}catch{return n}}function uE(n,r=.15){return ud(n)>.5?wr(n,r):Mr(n,r)}function js(n,r,o){try{return uE(n,r)}catch{return n}}const Rv=S.createContext(null);function Vd(){return S.useContext(Rv)}const cE=typeof Symbol=="function"&&Symbol.for,fE=cE?Symbol.for("mui.nested"):"__THEME_NESTED__";function dE(n,r){return typeof r=="function"?r(n):{...n,...r}}function pE(n){const{children:r,theme:o}=n,i=Vd(),s=S.useMemo(()=>{const c=i===null?{...o}:dE(i,o);return c!=null&&(c[fE]=i!==null),c},[o,i]);return g.jsx(Rv.Provider,{value:s,children:r})}const wv=S.createContext();function mE({value:n,...r}){return g.jsx(wv.Provider,{value:n??!0,...r})}const Mv=()=>S.useContext(wv)??!1,Av=S.createContext(void 0);function hE({value:n,children:r}){return g.jsx(Av.Provider,{value:n,children:r})}function gE(n){const{theme:r,name:o,props:i}=n;if(!r||!r.components||!r.components[o])return i;const s=r.components[o];return s.defaultProps?ai(s.defaultProps,i,r.components.mergeClassNameAndStyle):!s.styleOverrides&&!s.variants?ai(s,i,r.components.mergeClassNameAndStyle):i}function yE({props:n,name:r}){const o=S.useContext(Av);return gE({props:n,name:r,theme:{components:o}})}let By=0;function vE(n){const[r,o]=S.useState(n),i=n||r;return S.useEffect(()=>{r==null&&(By+=1,o(`mui-${By}`))},[r]),i}const bE={...Gs},$y=bE.useId;function Ja(n){if($y!==void 0){const r=$y();return n??r}return vE(n)}function SE(n){const r=qd(),o=Ja()||"",{modularCssLayers:i}=n;let s="mui.global, mui.components, mui.theme, mui.custom, mui.sx";return!i||r!==null?s="":typeof i=="string"?s=i.replace(/mui(?!\.)/g,s):s=`@layer ${s};`,ta(()=>{var p,m;const c=document.querySelector("head");if(!c)return;const d=c.firstChild;if(s){if(d&&((p=d.hasAttribute)!=null&&p.call(d,"data-mui-layer-order"))&&d.getAttribute("data-mui-layer-order")===o)return;const h=document.createElement("style");h.setAttribute("data-mui-layer-order",o),h.textContent=s,c.prepend(h)}else(m=c.querySelector(`style[data-mui-layer-order="${o}"]`))==null||m.remove()},[s,o]),s?g.jsx(gv,{styles:s}):null}const Ly={};function Uy(n,r,o,i=!1){return S.useMemo(()=>{const s=n&&r[n]||r;if(typeof o=="function"){const c=o(s),d=n?{...r,[n]:c}:c;return i?()=>d:d}return n?{...r,[n]:o}:{...r,...o}},[n,r,o,i])}function Ov(n){const{children:r,theme:o,themeId:i}=n,s=qd(Ly),c=Vd()||Ly,d=Uy(i,s,o),p=Uy(i,c,o,!0),m=(i?d[i]:d).direction==="rtl",h=SE(d);return g.jsx(pE,{theme:p,children:g.jsx(si.Provider,{value:d,children:g.jsx(mE,{value:m,children:g.jsxs(hE,{value:i?d[i].components:d.components,children:[h,r]})})})})}const Hy={theme:void 0};function xE(n){let r,o;return function(s){let c=r;return(c===void 0||s.theme!==o)&&(Hy.theme=s.theme,c=bv(n(Hy)),r=c,o=s.theme),c}}const Yd="mode",Fd="color-scheme",CE="data-color-scheme";function EE(n){const{defaultMode:r="system",defaultLightColorScheme:o="light",defaultDarkColorScheme:i="dark",modeStorageKey:s=Yd,colorSchemeStorageKey:c=Fd,attribute:d=CE,colorSchemeNode:p="document.documentElement",nonce:m}=n||{};let h="",y=d;if(d==="class"&&(y=".%s"),d==="data"&&(y="[data-%s]"),y.startsWith(".")){const x=y.substring(1);h+=`${p}.classList.remove('${x}'.replace('%s', light), '${x}'.replace('%s', dark));
      ${p}.classList.add('${x}'.replace('%s', colorScheme));`}const b=y.match(/\[([^\]]+)\]/);if(b){const[x,R]=b[1].split("=");R||(h+=`${p}.removeAttribute('${x}'.replace('%s', light));
      ${p}.removeAttribute('${x}'.replace('%s', dark));`),h+=`
      ${p}.setAttribute('${x}'.replace('%s', colorScheme), ${R?`${R}.replace('%s', colorScheme)`:'""'});`}else h+=`${p}.setAttribute('${y}', colorScheme);`;return g.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?m:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${s}') || '${r}';
  const dark = localStorage.getItem('${c}-dark') || '${i}';
  const light = localStorage.getItem('${c}-light') || '${o}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${h}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function TE(){}const RE=({key:n,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(o){if(typeof window>"u")return;if(!r)return o;let i;try{i=r.localStorage.getItem(n)}catch{}return i||o},set:o=>{if(r)try{r.localStorage.setItem(n,o)}catch{}},subscribe:o=>{if(!r)return TE;const i=s=>{const c=s.newValue;s.key===n&&o(c)};return r.addEventListener("storage",i),()=>{r.removeEventListener("storage",i)}}});function Ff(){}function Py(n){if(typeof window<"u"&&typeof window.matchMedia=="function"&&n==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function _v(n,r){if(n.mode==="light"||n.mode==="system"&&n.systemMode==="light")return r("light");if(n.mode==="dark"||n.mode==="system"&&n.systemMode==="dark")return r("dark")}function wE(n){return _v(n,r=>{if(r==="light")return n.lightColorScheme;if(r==="dark")return n.darkColorScheme})}function ME(n){const{defaultMode:r="light",defaultLightColorScheme:o,defaultDarkColorScheme:i,supportedColorSchemes:s=[],modeStorageKey:c=Yd,colorSchemeStorageKey:d=Fd,storageWindow:p=typeof window>"u"?void 0:window,storageManager:m=RE,noSsr:h=!1}=n,y=s.join(","),b=s.length>1,x=S.useMemo(()=>m==null?void 0:m({key:c,storageWindow:p}),[m,c,p]),R=S.useMemo(()=>m==null?void 0:m({key:`${d}-light`,storageWindow:p}),[m,d,p]),C=S.useMemo(()=>m==null?void 0:m({key:`${d}-dark`,storageWindow:p}),[m,d,p]),[w,M]=S.useState(()=>{const F=(x==null?void 0:x.get(r))||r,K=(R==null?void 0:R.get(o))||o,E=(C==null?void 0:C.get(i))||i;return{mode:F,systemMode:Py(F),lightColorScheme:K,darkColorScheme:E}}),[j,D]=S.useState(h||!b);S.useEffect(()=>{D(!0)},[]);const A=wE(w),O=S.useCallback(F=>{M(K=>{if(F===K.mode)return K;const E=F??r;return x==null||x.set(E),{...K,mode:E,systemMode:Py(E)}})},[x,r]),_=S.useCallback(F=>{F?typeof F=="string"?F&&!y.includes(F)?console.error(`\`${F}\` does not exist in \`theme.colorSchemes\`.`):M(K=>{const E={...K};return _v(K,U=>{U==="light"&&(R==null||R.set(F),E.lightColorScheme=F),U==="dark"&&(C==null||C.set(F),E.darkColorScheme=F)}),E}):M(K=>{const E={...K},U=F.light===null?o:F.light,X=F.dark===null?i:F.dark;return U&&(y.includes(U)?(E.lightColorScheme=U,R==null||R.set(U)):console.error(`\`${U}\` does not exist in \`theme.colorSchemes\`.`)),X&&(y.includes(X)?(E.darkColorScheme=X,C==null||C.set(X)):console.error(`\`${X}\` does not exist in \`theme.colorSchemes\`.`)),E}):M(K=>(R==null||R.set(o),C==null||C.set(i),{...K,lightColorScheme:o,darkColorScheme:i}))},[y,R,C,o,i]),L=S.useCallback(F=>{w.mode==="system"&&M(K=>{const E=F!=null&&F.matches?"dark":"light";return K.systemMode===E?K:{...K,systemMode:E}})},[w.mode]),Y=S.useRef(L);return Y.current=L,S.useEffect(()=>{if(typeof window.matchMedia!="function"||!b)return;const F=(...E)=>Y.current(...E),K=window.matchMedia("(prefers-color-scheme: dark)");return K.addListener(F),F(K),()=>{K.removeListener(F)}},[b]),S.useEffect(()=>{if(b){const F=(x==null?void 0:x.subscribe(U=>{(!U||["light","dark","system"].includes(U))&&O(U||r)}))||Ff,K=(R==null?void 0:R.subscribe(U=>{(!U||y.match(U))&&_({light:U})}))||Ff,E=(C==null?void 0:C.subscribe(U=>{(!U||y.match(U))&&_({dark:U})}))||Ff;return()=>{F(),K(),E()}}},[_,O,y,r,p,b,x,R,C]),{...w,mode:j?w.mode:void 0,systemMode:j?w.systemMode:void 0,colorScheme:j?A:void 0,setMode:O,setColorScheme:_}}const AE="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function OE(n){const{themeId:r,theme:o={},modeStorageKey:i=Yd,colorSchemeStorageKey:s=Fd,disableTransitionOnChange:c=!1,defaultColorScheme:d,resolveTheme:p}=n,m={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},h=S.createContext(void 0),y=()=>S.useContext(h)||m,b={},x={};function R(j){var en,ge,De,pe;const{children:D,theme:A,modeStorageKey:O=i,colorSchemeStorageKey:_=s,disableTransitionOnChange:L=c,storageManager:Y,storageWindow:F=typeof window>"u"?void 0:window,documentNode:K=typeof document>"u"?void 0:document,colorSchemeNode:E=typeof document>"u"?void 0:document.documentElement,disableNestedContext:U=!1,disableStyleSheetGeneration:X=!1,defaultMode:at="system",forceThemeRerender:it=!1,noSsr:Q}=j,k=S.useRef(!1),I=Vd(),ot=S.useContext(h),Z=!!ot&&!U,N=S.useMemo(()=>A||(typeof o=="function"?o():o),[A]),G=N[r],et=G||N,{colorSchemes:tt=b,components:ut=x,cssVarPrefix:lt}=et,ct=Object.keys(tt).filter(ue=>!!tt[ue]).join(","),bt=S.useMemo(()=>ct.split(","),[ct]),wt=typeof d=="string"?d:d.light,zt=typeof d=="string"?d:d.dark,gt=tt[wt]&&tt[zt]?at:((ge=(en=tt[et.defaultColorScheme])==null?void 0:en.palette)==null?void 0:ge.mode)||((De=et.palette)==null?void 0:De.mode),{mode:_t,setMode:xt,systemMode:qt,lightColorScheme:jt,darkColorScheme:Xt,colorScheme:He,setColorScheme:Kt}=ME({supportedColorSchemes:bt,defaultLightColorScheme:wt,defaultDarkColorScheme:zt,modeStorageKey:O,colorSchemeStorageKey:_,defaultMode:gt,storageManager:Y,storageWindow:F,noSsr:Q});let se=_t,ie=He;Z&&(se=ot.mode,ie=ot.colorScheme);let de=ie||et.defaultColorScheme;et.vars&&!it&&(de=et.defaultColorScheme);const Yt=S.useMemo(()=>{var Ye;const ue=((Ye=et.generateThemeVars)==null?void 0:Ye.call(et))||et.vars,Et={...et,components:ut,colorSchemes:tt,cssVarPrefix:lt,vars:ue};if(typeof Et.generateSpacing=="function"&&(Et.spacing=Et.generateSpacing()),de){const Te=tt[de];Te&&typeof Te=="object"&&Object.keys(Te).forEach(je=>{Te[je]&&typeof Te[je]=="object"?Et[je]={...Et[je],...Te[je]}:Et[je]=Te[je]})}return p?p(Et):Et},[et,de,ut,tt,lt]),ht=et.colorSchemeSelector;ta(()=>{if(ie&&E&&ht&&ht!=="media"){const ue=ht;let Et=ht;if(ue==="class"&&(Et=".%s"),ue==="data"&&(Et="[data-%s]"),ue!=null&&ue.startsWith("data-")&&!ue.includes("%s")&&(Et=`[${ue}="%s"]`),Et.startsWith("."))E.classList.remove(...bt.map(Ye=>Et.substring(1).replace("%s",Ye))),E.classList.add(Et.substring(1).replace("%s",ie));else{const Ye=Et.replace("%s",ie).match(/\[([^\]]+)\]/);if(Ye){const[Te,je]=Ye[1].split("=");je||bt.forEach(xn=>{E.removeAttribute(Te.replace(ie,xn))}),E.setAttribute(Te,je?je.replace(/"|'/g,""):"")}else E.setAttribute(Et,ie)}}},[ie,ht,E,bt]),S.useEffect(()=>{let ue;if(L&&k.current&&K){const Et=K.createElement("style");Et.appendChild(K.createTextNode(AE)),K.head.appendChild(Et),window.getComputedStyle(K.body),ue=setTimeout(()=>{K.head.removeChild(Et)},1)}return()=>{clearTimeout(ue)}},[ie,L,K]),S.useEffect(()=>(k.current=!0,()=>{k.current=!1}),[]);const un=S.useMemo(()=>({allColorSchemes:bt,colorScheme:ie,darkColorScheme:Xt,lightColorScheme:jt,mode:se,setColorScheme:Kt,setMode:xt,systemMode:qt}),[bt,ie,Xt,jt,se,Kt,xt,qt,Yt.colorSchemeSelector]);let he=!0;(X||et.cssVariables===!1||Z&&(I==null?void 0:I.cssVarPrefix)===lt)&&(he=!1);const kn=g.jsxs(S.Fragment,{children:[g.jsx(Ov,{themeId:G?r:void 0,theme:Yt,children:D}),he&&g.jsx(cv,{styles:((pe=Yt.generateStyleSheets)==null?void 0:pe.call(Yt))||[]})]});return Z?kn:g.jsx(h.Provider,{value:un,children:kn})}const C=typeof d=="string"?d:d.light,w=typeof d=="string"?d:d.dark;return{CssVarsProvider:R,useColorScheme:y,getInitColorSchemeScript:j=>EE({colorSchemeStorageKey:s,defaultLightColorScheme:C,defaultDarkColorScheme:w,modeStorageKey:i,...j})}}function _E(n=""){function r(...i){if(!i.length)return"";const s=i[0];return typeof s=="string"&&!s.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${n?`${n}-`:""}${s}${r(...i.slice(1))})`:`, ${s}`}return(i,...s)=>`var(--${n?`${n}-`:""}${i}${r(...s)})`}const qy=(n,r,o,i=[])=>{let s=n;r.forEach((c,d)=>{d===r.length-1?Array.isArray(s)?s[Number(c)]=o:s&&typeof s=="object"&&(s[c]=o):s&&typeof s=="object"&&(s[c]||(s[c]=i.includes(c)?[]:{}),s=s[c])})},jE=(n,r,o)=>{function i(s,c=[],d=[]){Object.entries(s).forEach(([p,m])=>{(!o||o&&!o([...c,p]))&&m!=null&&(typeof m=="object"&&Object.keys(m).length>0?i(m,[...c,p],Array.isArray(m)?[...d,p]:d):r([...c,p],m,d))})}i(n)},NE=(n,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(i=>n.includes(i))||n[n.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function Xf(n,r){const{prefix:o,shouldSkipGeneratingVar:i}=r||{},s={},c={},d={};return jE(n,(p,m,h)=>{if((typeof m=="string"||typeof m=="number")&&(!i||!i(p,m))){const y=`--${o?`${o}-`:""}${p.join("-")}`,b=NE(p,m);Object.assign(s,{[y]:b}),qy(c,p,`var(${y})`,h),qy(d,p,`var(${y}, ${b})`,h)}},p=>p[0]==="vars"),{css:s,vars:c,varsWithDefaults:d}}function zE(n,r={}){const{getSelector:o=M,disableCssColorScheme:i,colorSchemeSelector:s}=r,{colorSchemes:c={},components:d,defaultColorScheme:p="light",...m}=n,{vars:h,css:y,varsWithDefaults:b}=Xf(m,r);let x=b;const R={},{[p]:C,...w}=c;if(Object.entries(w||{}).forEach(([A,O])=>{const{vars:_,css:L,varsWithDefaults:Y}=Xf(O,r);x=We(x,Y),R[A]={css:L,vars:_}}),C){const{css:A,vars:O,varsWithDefaults:_}=Xf(C,r);x=We(x,_),R[p]={css:A,vars:O}}function M(A,O){var L,Y;let _=s;if(s==="class"&&(_=".%s"),s==="data"&&(_="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(_=`[${s}="%s"]`),A){if(_==="media")return n.defaultColorScheme===A?":root":{[`@media (prefers-color-scheme: ${((Y=(L=c[A])==null?void 0:L.palette)==null?void 0:Y.mode)||A})`]:{":root":O}};if(_)return n.defaultColorScheme===A?`:root, ${_.replace("%s",String(A))}`:_.replace("%s",String(A))}return":root"}return{vars:x,generateThemeVars:()=>{let A={...h};return Object.entries(R).forEach(([,{vars:O}])=>{A=We(A,O)}),A},generateStyleSheets:()=>{var F,K;const A=[],O=n.defaultColorScheme||"light";function _(E,U){Object.keys(U).length&&A.push(typeof E=="string"?{[E]:{...U}}:E)}_(o(void 0,{...y}),y);const{[O]:L,...Y}=R;if(L){const{css:E}=L,U=(K=(F=c[O])==null?void 0:F.palette)==null?void 0:K.mode,X=!i&&U?{colorScheme:U,...E}:{...E};_(o(O,{...X}),X)}return Object.entries(Y).forEach(([E,{css:U}])=>{var it,Q;const X=(Q=(it=c[E])==null?void 0:it.palette)==null?void 0:Q.mode,at=!i&&X?{colorScheme:X,...U}:{...U};_(o(E,{...at}),at)}),A}}}function DE(n){return function(o){return n==="media"?`@media (prefers-color-scheme: ${o})`:n?n.startsWith("data-")&&!n.includes("%s")?`[${n}="${o}"] &`:n==="class"?`.${o} &`:n==="data"?`[data-${o}] &`:`${n.replace("%s",o)} &`:"&"}}function At(n,r,o=void 0){const i={};for(const s in n){const c=n[s];let d="",p=!0;for(let m=0;m<c.length;m+=1){const h=c[m];h&&(d+=(p===!0?"":" ")+r(h),p=!1,o&&o[h]&&(d+=" "+o[h]))}i[s]=d}return i}const kE=pi(),BE=Cv("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`maxWidth${ft(String(o.maxWidth))}`],o.fixed&&r.fixed,o.disableGutters&&r.disableGutters]}}),$E=n=>Ev({props:n,name:"MuiContainer",defaultTheme:kE}),LE=(n,r)=>{const o=m=>Mt(r,m),{classes:i,fixed:s,disableGutters:c,maxWidth:d}=n,p={root:["root",d&&`maxWidth${ft(String(d))}`,s&&"fixed",c&&"disableGutters"]};return At(p,o,i)};function UE(n={}){const{createStyledComponent:r=BE,useThemeProps:o=$E,componentName:i="MuiContainer"}=n,s=r(({theme:d,ownerState:p})=>({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!p.disableGutters&&{paddingLeft:d.spacing(2),paddingRight:d.spacing(2),[d.breakpoints.up("sm")]:{paddingLeft:d.spacing(3),paddingRight:d.spacing(3)}}}),({theme:d,ownerState:p})=>p.fixed&&Object.keys(d.breakpoints.values).reduce((m,h)=>{const y=h,b=d.breakpoints.values[y];return b!==0&&(m[d.breakpoints.up(y)]={maxWidth:`${b}${d.breakpoints.unit}`}),m},{}),({theme:d,ownerState:p})=>({...p.maxWidth==="xs"&&{[d.breakpoints.up("xs")]:{maxWidth:Math.max(d.breakpoints.values.xs,444)}},...p.maxWidth&&p.maxWidth!=="xs"&&{[d.breakpoints.up(p.maxWidth)]:{maxWidth:`${d.breakpoints.values[p.maxWidth]}${d.breakpoints.unit}`}}}));return S.forwardRef(function(p,m){const h=o(p),{className:y,component:b="div",disableGutters:x=!1,fixed:R=!1,maxWidth:C="lg",classes:w,...M}=h,j={...h,component:b,disableGutters:x,fixed:R,maxWidth:C},D=LE(j,i);return g.jsx(s,{as:b,ownerState:j,className:mt(D.root,y),ref:m,...M})})}function Hs(n,r){var o,i,s;return S.isValidElement(n)&&r.indexOf(n.type.muiName??((s=(i=(o=n.type)==null?void 0:o._payload)==null?void 0:i.value)==null?void 0:s.muiName))!==-1}const HE=(n,r)=>n.filter(o=>r.includes(o)),Bo=(n,r,o)=>{const i=n.keys[0];Array.isArray(r)?r.forEach((s,c)=>{o((d,p)=>{c<=n.keys.length-1&&(c===0?Object.assign(d,p):d[n.up(n.keys[c])]=p)},s)}):r&&typeof r=="object"?(Object.keys(r).length>n.keys.length?n.keys:HE(n.keys,Object.keys(r))).forEach(c=>{if(n.keys.includes(c)){const d=r[c];d!==void 0&&o((p,m)=>{i===c?Object.assign(p,m):p[n.up(c)]=m},d)}}):(typeof r=="number"||typeof r=="string")&&o((s,c)=>{Object.assign(s,c)},r)};function Xs(n){return`--Grid-${n}Spacing`}function du(n){return`--Grid-parent-${n}Spacing`}const Gy="--Grid-columns",Ao="--Grid-parent-columns",PE=({theme:n,ownerState:r})=>{const o={};return Bo(n.breakpoints,r.size,(i,s)=>{let c={};s==="grow"&&(c={flexBasis:0,flexGrow:1,maxWidth:"100%"}),s==="auto"&&(c={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof s=="number"&&(c={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${s} / var(${Ao}) - (var(${Ao}) - ${s}) * (var(${du("column")}) / var(${Ao})))`}),i(o,c)}),o},qE=({theme:n,ownerState:r})=>{const o={};return Bo(n.breakpoints,r.offset,(i,s)=>{let c={};s==="auto"&&(c={marginLeft:"auto"}),typeof s=="number"&&(c={marginLeft:s===0?"0px":`calc(100% * ${s} / var(${Ao}) + var(${du("column")}) * ${s} / var(${Ao}))`}),i(o,c)}),o},GE=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={[Gy]:12};return Bo(n.breakpoints,r.columns,(i,s)=>{const c=s??12;i(o,{[Gy]:c,"> *":{[Ao]:c}})}),o},IE=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={};return Bo(n.breakpoints,r.rowSpacing,(i,s)=>{var d;const c=typeof s=="string"?s:(d=n.spacing)==null?void 0:d.call(n,s);i(o,{[Xs("row")]:c,"> *":{[du("row")]:c}})}),o},VE=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={};return Bo(n.breakpoints,r.columnSpacing,(i,s)=>{var d;const c=typeof s=="string"?s:(d=n.spacing)==null?void 0:d.call(n,s);i(o,{[Xs("column")]:c,"> *":{[du("column")]:c}})}),o},YE=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={};return Bo(n.breakpoints,r.direction,(i,s)=>{i(o,{flexDirection:s})}),o},FE=({ownerState:n})=>({minWidth:0,boxSizing:"border-box",...n.container&&{display:"flex",flexWrap:"wrap",...n.wrap&&n.wrap!=="wrap"&&{flexWrap:n.wrap},gap:`var(${Xs("row")}) var(${Xs("column")})`}}),XE=n=>{const r=[];return Object.entries(n).forEach(([o,i])=>{i!==!1&&i!==void 0&&r.push(`grid-${o}-${String(i)}`)}),r},KE=(n,r="xs")=>{function o(i){return i===void 0?!1:typeof i=="string"&&!Number.isNaN(Number(i))||typeof i=="number"&&i>0}if(o(n))return[`spacing-${r}-${String(n)}`];if(typeof n=="object"&&!Array.isArray(n)){const i=[];return Object.entries(n).forEach(([s,c])=>{o(c)&&i.push(`spacing-${s}-${String(c)}`)}),i}return[]},QE=n=>n===void 0?[]:typeof n=="object"?Object.entries(n).map(([r,o])=>`direction-${r}-${o}`):[`direction-xs-${String(n)}`];function WE(n,r){n.item!==void 0&&delete n.item,n.zeroMinWidth!==void 0&&delete n.zeroMinWidth,r.keys.forEach(o=>{n[o]!==void 0&&delete n[o]})}const ZE=pi(),JE=Cv("div",{name:"MuiGrid",slot:"Root"});function tT(n){return Ev({props:n,name:"MuiGrid",defaultTheme:ZE})}function eT(n={}){const{createStyledComponent:r=JE,useThemeProps:o=tT,useTheme:i=mi,componentName:s="MuiGrid"}=n,c=(h,y)=>{const{container:b,direction:x,spacing:R,wrap:C,size:w}=h,M={root:["root",b&&"container",C!=="wrap"&&`wrap-xs-${String(C)}`,...QE(x),...XE(w),...b?KE(R,y.breakpoints.keys[0]):[]]};return At(M,j=>Mt(s,j),{})};function d(h,y,b=()=>!0){const x={};return h===null||(Array.isArray(h)?h.forEach((R,C)=>{R!==null&&b(R)&&y.keys[C]&&(x[y.keys[C]]=R)}):typeof h=="object"?Object.keys(h).forEach(R=>{const C=h[R];C!=null&&b(C)&&(x[R]=C)}):x[y.keys[0]]=h),x}const p=r(GE,VE,IE,PE,YE,FE,qE),m=S.forwardRef(function(y,b){const x=i(),R=o(y),C=Gd(R);WE(C,x.breakpoints);const{className:w,children:M,columns:j=12,container:D=!1,component:A="div",direction:O="row",wrap:_="wrap",size:L={},offset:Y={},spacing:F=0,rowSpacing:K=F,columnSpacing:E=F,unstable_level:U=0,...X}=C,at=d(L,x.breakpoints,G=>G!==!1),it=d(Y,x.breakpoints),Q=y.columns??(U?void 0:j),k=y.spacing??(U?void 0:F),I=y.rowSpacing??y.spacing??(U?void 0:K),ot=y.columnSpacing??y.spacing??(U?void 0:E),Z={...C,level:U,columns:Q,container:D,direction:O,wrap:_,spacing:k,rowSpacing:I,columnSpacing:ot,size:at,offset:it},N=c(Z,x);return g.jsx(p,{ref:b,as:A,ownerState:Z,className:mt(N.root,w),...X,children:S.Children.map(M,G=>{var et;return S.isValidElement(G)&&Hs(G,["Grid"])&&D&&G.props.container?S.cloneElement(G,{unstable_level:((et=G.props)==null?void 0:et.unstable_level)??U+1}):G})})});return m.muiName="Grid",m}function jv(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Jl.white,default:Jl.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const nT=jv();function Nv(){return{text:{primary:Jl.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Jl.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Iy=Nv();function Vy(n,r,o,i){const s=i.light||i,c=i.dark||i*1.5;n[r]||(n.hasOwnProperty(o)?n[r]=n[o]:r==="light"?n.light=Mr(n.main,s):r==="dark"&&(n.dark=wr(n.main,c)))}function aT(n="light"){return n==="dark"?{main:xo[200],light:xo[50],dark:xo[400]}:{main:xo[700],light:xo[400],dark:xo[800]}}function rT(n="light"){return n==="dark"?{main:So[200],light:So[50],dark:So[400]}:{main:So[500],light:So[300],dark:So[700]}}function oT(n="light"){return n==="dark"?{main:bo[500],light:bo[300],dark:bo[700]}:{main:bo[700],light:bo[400],dark:bo[800]}}function lT(n="light"){return n==="dark"?{main:Co[400],light:Co[300],dark:Co[700]}:{main:Co[700],light:Co[500],dark:Co[900]}}function iT(n="light"){return n==="dark"?{main:Eo[400],light:Eo[300],dark:Eo[700]}:{main:Eo[800],light:Eo[500],dark:Eo[900]}}function sT(n="light"){return n==="dark"?{main:$l[400],light:$l[300],dark:$l[700]}:{main:"#ed6c02",light:$l[500],dark:$l[900]}}function Xd(n){const{mode:r="light",contrastThreshold:o=3,tonalOffset:i=.2,...s}=n,c=n.primary||aT(r),d=n.secondary||rT(r),p=n.error||oT(r),m=n.info||lT(r),h=n.success||iT(r),y=n.warning||sT(r);function b(w){return sE(w,Iy.text.primary)>=o?Iy.text.primary:nT.text.primary}const x=({color:w,name:M,mainShade:j=500,lightShade:D=300,darkShade:A=700})=>{if(w={...w},!w.main&&w[j]&&(w.main=w[j]),!w.hasOwnProperty("main"))throw new Error(Sa(11,M?` (${M})`:"",j));if(typeof w.main!="string")throw new Error(Sa(12,M?` (${M})`:"",JSON.stringify(w.main)));return Vy(w,"light",D,i),Vy(w,"dark",A,i),w.contrastText||(w.contrastText=b(w.main)),w};let R;return r==="light"?R=jv():r==="dark"&&(R=Nv()),We({common:{...Jl},mode:r,primary:x({color:c,name:"primary"}),secondary:x({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:x({color:p,name:"error"}),warning:x({color:y,name:"warning"}),info:x({color:m,name:"info"}),success:x({color:h,name:"success"}),grey:Kx,contrastThreshold:o,getContrastText:b,augmentColor:x,tonalOffset:i,...R},s)}function uT(n){const r={};return Object.entries(n).forEach(i=>{const[s,c]=i;typeof c=="object"&&(r[s]=`${c.fontStyle?`${c.fontStyle} `:""}${c.fontVariant?`${c.fontVariant} `:""}${c.fontWeight?`${c.fontWeight} `:""}${c.fontStretch?`${c.fontStretch} `:""}${c.fontSize||""}${c.lineHeight?`/${c.lineHeight} `:""}${c.fontFamily||""}`)}),r}function cT(n,r){return{toolbar:{minHeight:56,[n.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[n.up("sm")]:{minHeight:64}},...r}}function fT(n){return Math.round(n*1e5)/1e5}const Yy={textTransform:"uppercase"},Fy='"Roboto", "Helvetica", "Arial", sans-serif';function zv(n,r){const{fontFamily:o=Fy,fontSize:i=14,fontWeightLight:s=300,fontWeightRegular:c=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:m=16,allVariants:h,pxToRem:y,...b}=typeof r=="function"?r(n):r,x=i/14,R=y||(M=>`${M/m*x}rem`),C=(M,j,D,A,O)=>({fontFamily:o,fontWeight:M,fontSize:R(j),lineHeight:D,...o===Fy?{letterSpacing:`${fT(A/j)}em`}:{},...O,...h}),w={h1:C(s,96,1.167,-1.5),h2:C(s,60,1.2,-.5),h3:C(c,48,1.167,0),h4:C(c,34,1.235,.25),h5:C(c,24,1.334,0),h6:C(d,20,1.6,.15),subtitle1:C(c,16,1.75,.15),subtitle2:C(d,14,1.57,.1),body1:C(c,16,1.5,.15),body2:C(c,14,1.43,.15),button:C(d,14,1.75,.4,Yy),caption:C(c,12,1.66,.4),overline:C(c,12,2.66,1,Yy),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return We({htmlFontSize:m,pxToRem:R,fontFamily:o,fontSize:i,fontWeightLight:s,fontWeightRegular:c,fontWeightMedium:d,fontWeightBold:p,...w},b,{clone:!1})}const dT=.2,pT=.14,mT=.12;function fe(...n){return[`${n[0]}px ${n[1]}px ${n[2]}px ${n[3]}px rgba(0,0,0,${dT})`,`${n[4]}px ${n[5]}px ${n[6]}px ${n[7]}px rgba(0,0,0,${pT})`,`${n[8]}px ${n[9]}px ${n[10]}px ${n[11]}px rgba(0,0,0,${mT})`].join(",")}const hT=["none",fe(0,2,1,-1,0,1,1,0,0,1,3,0),fe(0,3,1,-2,0,2,2,0,0,1,5,0),fe(0,3,3,-2,0,3,4,0,0,1,8,0),fe(0,2,4,-1,0,4,5,0,0,1,10,0),fe(0,3,5,-1,0,5,8,0,0,1,14,0),fe(0,3,5,-1,0,6,10,0,0,1,18,0),fe(0,4,5,-2,0,7,10,1,0,2,16,1),fe(0,5,5,-3,0,8,10,1,0,3,14,2),fe(0,5,6,-3,0,9,12,1,0,3,16,2),fe(0,6,6,-3,0,10,14,1,0,4,18,3),fe(0,6,7,-4,0,11,15,1,0,4,20,3),fe(0,7,8,-4,0,12,17,2,0,5,22,4),fe(0,7,8,-4,0,13,19,2,0,5,24,4),fe(0,7,9,-4,0,14,21,2,0,5,26,4),fe(0,8,9,-5,0,15,22,2,0,6,28,5),fe(0,8,10,-5,0,16,24,2,0,6,30,5),fe(0,8,11,-5,0,17,26,2,0,6,32,5),fe(0,9,11,-5,0,18,28,2,0,7,34,6),fe(0,9,12,-6,0,19,29,2,0,7,36,6),fe(0,10,13,-6,0,20,31,3,0,8,38,7),fe(0,10,13,-6,0,21,33,3,0,8,40,7),fe(0,10,14,-6,0,22,35,3,0,8,42,7),fe(0,11,14,-7,0,23,36,3,0,9,44,8),fe(0,11,15,-7,0,24,38,3,0,9,46,8)],gT={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},yT={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Xy(n){return`${Math.round(n)}ms`}function vT(n){if(!n)return 0;const r=n/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function bT(n){const r={...gT,...n.easing},o={...yT,...n.duration};return{getAutoHeightDuration:vT,create:(s=["all"],c={})=>{const{duration:d=o.standard,easing:p=r.easeInOut,delay:m=0,...h}=c;return(Array.isArray(s)?s:[s]).map(y=>`${y} ${typeof d=="string"?d:Xy(d)} ${p} ${typeof m=="string"?m:Xy(m)}`).join(",")},...n,easing:r,duration:o}}const ST={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function xT(n){return Qn(n)||typeof n>"u"||typeof n=="string"||typeof n=="boolean"||typeof n=="number"||Array.isArray(n)}function Dv(n={}){const r={...n};function o(i){const s=Object.entries(i);for(let c=0;c<s.length;c++){const[d,p]=s[c];!xT(p)||d.startsWith("unstable_")?delete i[d]:Qn(p)&&(i[d]={...p},o(i[d]))}}return o(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function cd(n={},...r){const{breakpoints:o,mixins:i={},spacing:s,palette:c={},transitions:d={},typography:p={},shape:m,...h}=n;if(n.vars&&n.generateThemeVars===void 0)throw new Error(Sa(20));const y=Xd(c),b=pi(n);let x=We(b,{mixins:cT(b.breakpoints,i),palette:y,shadows:hT.slice(),typography:zv(y,p),transitions:bT(d),zIndex:{...ST}});return x=We(x,h),x=r.reduce((R,C)=>We(R,C),x),x.unstable_sxConfig={...di,...h==null?void 0:h.unstable_sxConfig},x.unstable_sx=function(C){return Wa({sx:C,theme:this})},x.toRuntimeSource=Dv,x}function fd(n){let r;return n<1?r=5.11916*n**2:r=4.5*Math.log(n+1)+2,Math.round(r*10)/1e3}const CT=[...Array(25)].map((n,r)=>{if(r===0)return"none";const o=fd(r);return`linear-gradient(rgba(255 255 255 / ${o}), rgba(255 255 255 / ${o}))`});function kv(n){return{inputPlaceholder:n==="dark"?.5:.42,inputUnderline:n==="dark"?.7:.42,switchTrackDisabled:n==="dark"?.2:.12,switchTrack:n==="dark"?.3:.38}}function Bv(n){return n==="dark"?CT:[]}function ET(n){const{palette:r={mode:"light"},opacity:o,overlays:i,...s}=n,c=Xd(r);return{palette:c,opacity:{...kv(c.mode),...o},overlays:i||Bv(c.mode),...s}}function TT(n){var r;return!!n[0].match(/(cssVarPrefix|colorSchemeSelector|modularCssLayers|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!n[0].match(/sxConfig$/)||n[0]==="palette"&&!!((r=n[1])!=null&&r.match(/(mode|contrastThreshold|tonalOffset)/))}const RT=n=>[...[...Array(25)].map((r,o)=>`--${n?`${n}-`:""}overlays-${o}`),`--${n?`${n}-`:""}palette-AppBar-darkBg`,`--${n?`${n}-`:""}palette-AppBar-darkColor`],wT=n=>(r,o)=>{const i=n.rootSelector||":root",s=n.colorSchemeSelector;let c=s;if(s==="class"&&(c=".%s"),s==="data"&&(c="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(c=`[${s}="%s"]`),n.defaultColorScheme===r){if(r==="dark"){const d={};return RT(n.cssVarPrefix).forEach(p=>{d[p]=o[p],delete o[p]}),c==="media"?{[i]:o,"@media (prefers-color-scheme: dark)":{[i]:d}}:c?{[c.replace("%s",r)]:d,[`${i}, ${c.replace("%s",r)}`]:o}:{[i]:{...o,...d}}}if(c&&c!=="media")return`${i}, ${c.replace("%s",String(r))}`}else if(r){if(c==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[i]:o}};if(c)return c.replace("%s",String(r))}return i};function MT(n,r){r.forEach(o=>{n[o]||(n[o]={})})}function W(n,r,o){!n[r]&&o&&(n[r]=o)}function Il(n){return typeof n!="string"||!n.startsWith("hsl")?n:Tv(n)}function va(n,r){`${r}Channel`in n||(n[`${r}Channel`]=Gl(Il(n[r])))}function AT(n){return typeof n=="number"?`${n}px`:typeof n=="string"||typeof n=="function"||Array.isArray(n)?n:"8px"}const Yn=n=>{try{return n()}catch{}},OT=(n="mui")=>_E(n);function Kf(n,r,o,i){if(!r)return;r=r===!0?{}:r;const s=i==="dark"?"dark":"light";if(!o){n[i]=ET({...r,palette:{mode:s,...r==null?void 0:r.palette}});return}const{palette:c,...d}=cd({...o,palette:{mode:s,...r==null?void 0:r.palette}});return n[i]={...r,palette:c,opacity:{...kv(s),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||Bv(s)},d}function _T(n={},...r){const{colorSchemes:o={light:!0},defaultColorScheme:i,disableCssColorScheme:s=!1,cssVarPrefix:c="mui",shouldSkipGeneratingVar:d=TT,colorSchemeSelector:p=o.light&&o.dark?"media":void 0,rootSelector:m=":root",...h}=n,y=Object.keys(o)[0],b=i||(o.light&&y!=="light"?"light":y),x=OT(c),{[b]:R,light:C,dark:w,...M}=o,j={...M};let D=R;if((b==="dark"&&!("dark"in o)||b==="light"&&!("light"in o))&&(D=!0),!D)throw new Error(Sa(21,b));const A=Kf(j,D,h,b);C&&!j.light&&Kf(j,C,void 0,"light"),w&&!j.dark&&Kf(j,w,void 0,"dark");let O={defaultColorScheme:b,...A,cssVarPrefix:c,colorSchemeSelector:p,rootSelector:m,getCssVar:x,colorSchemes:j,font:{...uT(A.typography),...A.font},spacing:AT(h.spacing)};Object.keys(O.colorSchemes).forEach(K=>{const E=O.colorSchemes[K].palette,U=X=>{const at=X.split("-"),it=at[1],Q=at[2];return x(X,E[it][Q])};if(E.mode==="light"&&(W(E.common,"background","#fff"),W(E.common,"onBackground","#000")),E.mode==="dark"&&(W(E.common,"background","#000"),W(E.common,"onBackground","#fff")),MT(E,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),E.mode==="light"){W(E.Alert,"errorColor",re(E.error.light,.6)),W(E.Alert,"infoColor",re(E.info.light,.6)),W(E.Alert,"successColor",re(E.success.light,.6)),W(E.Alert,"warningColor",re(E.warning.light,.6)),W(E.Alert,"errorFilledBg",U("palette-error-main")),W(E.Alert,"infoFilledBg",U("palette-info-main")),W(E.Alert,"successFilledBg",U("palette-success-main")),W(E.Alert,"warningFilledBg",U("palette-warning-main")),W(E.Alert,"errorFilledColor",Yn(()=>E.getContrastText(E.error.main))),W(E.Alert,"infoFilledColor",Yn(()=>E.getContrastText(E.info.main))),W(E.Alert,"successFilledColor",Yn(()=>E.getContrastText(E.success.main))),W(E.Alert,"warningFilledColor",Yn(()=>E.getContrastText(E.warning.main))),W(E.Alert,"errorStandardBg",oe(E.error.light,.9)),W(E.Alert,"infoStandardBg",oe(E.info.light,.9)),W(E.Alert,"successStandardBg",oe(E.success.light,.9)),W(E.Alert,"warningStandardBg",oe(E.warning.light,.9)),W(E.Alert,"errorIconColor",U("palette-error-main")),W(E.Alert,"infoIconColor",U("palette-info-main")),W(E.Alert,"successIconColor",U("palette-success-main")),W(E.Alert,"warningIconColor",U("palette-warning-main")),W(E.AppBar,"defaultBg",U("palette-grey-100")),W(E.Avatar,"defaultBg",U("palette-grey-400")),W(E.Button,"inheritContainedBg",U("palette-grey-300")),W(E.Button,"inheritContainedHoverBg",U("palette-grey-A100")),W(E.Chip,"defaultBorder",U("palette-grey-400")),W(E.Chip,"defaultAvatarColor",U("palette-grey-700")),W(E.Chip,"defaultIconColor",U("palette-grey-700")),W(E.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),W(E.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),W(E.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),W(E.LinearProgress,"primaryBg",oe(E.primary.main,.62)),W(E.LinearProgress,"secondaryBg",oe(E.secondary.main,.62)),W(E.LinearProgress,"errorBg",oe(E.error.main,.62)),W(E.LinearProgress,"infoBg",oe(E.info.main,.62)),W(E.LinearProgress,"successBg",oe(E.success.main,.62)),W(E.LinearProgress,"warningBg",oe(E.warning.main,.62)),W(E.Skeleton,"bg",`rgba(${U("palette-text-primaryChannel")} / 0.11)`),W(E.Slider,"primaryTrack",oe(E.primary.main,.62)),W(E.Slider,"secondaryTrack",oe(E.secondary.main,.62)),W(E.Slider,"errorTrack",oe(E.error.main,.62)),W(E.Slider,"infoTrack",oe(E.info.main,.62)),W(E.Slider,"successTrack",oe(E.success.main,.62)),W(E.Slider,"warningTrack",oe(E.warning.main,.62));const X=js(E.background.default,.8);W(E.SnackbarContent,"bg",X),W(E.SnackbarContent,"color",Yn(()=>E.getContrastText(X))),W(E.SpeedDialAction,"fabHoverBg",js(E.background.paper,.15)),W(E.StepConnector,"border",U("palette-grey-400")),W(E.StepContent,"border",U("palette-grey-400")),W(E.Switch,"defaultColor",U("palette-common-white")),W(E.Switch,"defaultDisabledColor",U("palette-grey-100")),W(E.Switch,"primaryDisabledColor",oe(E.primary.main,.62)),W(E.Switch,"secondaryDisabledColor",oe(E.secondary.main,.62)),W(E.Switch,"errorDisabledColor",oe(E.error.main,.62)),W(E.Switch,"infoDisabledColor",oe(E.info.main,.62)),W(E.Switch,"successDisabledColor",oe(E.success.main,.62)),W(E.Switch,"warningDisabledColor",oe(E.warning.main,.62)),W(E.TableCell,"border",oe(_s(E.divider,1),.88)),W(E.Tooltip,"bg",_s(E.grey[700],.92))}if(E.mode==="dark"){W(E.Alert,"errorColor",oe(E.error.light,.6)),W(E.Alert,"infoColor",oe(E.info.light,.6)),W(E.Alert,"successColor",oe(E.success.light,.6)),W(E.Alert,"warningColor",oe(E.warning.light,.6)),W(E.Alert,"errorFilledBg",U("palette-error-dark")),W(E.Alert,"infoFilledBg",U("palette-info-dark")),W(E.Alert,"successFilledBg",U("palette-success-dark")),W(E.Alert,"warningFilledBg",U("palette-warning-dark")),W(E.Alert,"errorFilledColor",Yn(()=>E.getContrastText(E.error.dark))),W(E.Alert,"infoFilledColor",Yn(()=>E.getContrastText(E.info.dark))),W(E.Alert,"successFilledColor",Yn(()=>E.getContrastText(E.success.dark))),W(E.Alert,"warningFilledColor",Yn(()=>E.getContrastText(E.warning.dark))),W(E.Alert,"errorStandardBg",re(E.error.light,.9)),W(E.Alert,"infoStandardBg",re(E.info.light,.9)),W(E.Alert,"successStandardBg",re(E.success.light,.9)),W(E.Alert,"warningStandardBg",re(E.warning.light,.9)),W(E.Alert,"errorIconColor",U("palette-error-main")),W(E.Alert,"infoIconColor",U("palette-info-main")),W(E.Alert,"successIconColor",U("palette-success-main")),W(E.Alert,"warningIconColor",U("palette-warning-main")),W(E.AppBar,"defaultBg",U("palette-grey-900")),W(E.AppBar,"darkBg",U("palette-background-paper")),W(E.AppBar,"darkColor",U("palette-text-primary")),W(E.Avatar,"defaultBg",U("palette-grey-600")),W(E.Button,"inheritContainedBg",U("palette-grey-800")),W(E.Button,"inheritContainedHoverBg",U("palette-grey-700")),W(E.Chip,"defaultBorder",U("palette-grey-700")),W(E.Chip,"defaultAvatarColor",U("palette-grey-300")),W(E.Chip,"defaultIconColor",U("palette-grey-300")),W(E.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),W(E.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),W(E.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),W(E.LinearProgress,"primaryBg",re(E.primary.main,.5)),W(E.LinearProgress,"secondaryBg",re(E.secondary.main,.5)),W(E.LinearProgress,"errorBg",re(E.error.main,.5)),W(E.LinearProgress,"infoBg",re(E.info.main,.5)),W(E.LinearProgress,"successBg",re(E.success.main,.5)),W(E.LinearProgress,"warningBg",re(E.warning.main,.5)),W(E.Skeleton,"bg",`rgba(${U("palette-text-primaryChannel")} / 0.13)`),W(E.Slider,"primaryTrack",re(E.primary.main,.5)),W(E.Slider,"secondaryTrack",re(E.secondary.main,.5)),W(E.Slider,"errorTrack",re(E.error.main,.5)),W(E.Slider,"infoTrack",re(E.info.main,.5)),W(E.Slider,"successTrack",re(E.success.main,.5)),W(E.Slider,"warningTrack",re(E.warning.main,.5));const X=js(E.background.default,.98);W(E.SnackbarContent,"bg",X),W(E.SnackbarContent,"color",Yn(()=>E.getContrastText(X))),W(E.SpeedDialAction,"fabHoverBg",js(E.background.paper,.15)),W(E.StepConnector,"border",U("palette-grey-600")),W(E.StepContent,"border",U("palette-grey-600")),W(E.Switch,"defaultColor",U("palette-grey-300")),W(E.Switch,"defaultDisabledColor",U("palette-grey-600")),W(E.Switch,"primaryDisabledColor",re(E.primary.main,.55)),W(E.Switch,"secondaryDisabledColor",re(E.secondary.main,.55)),W(E.Switch,"errorDisabledColor",re(E.error.main,.55)),W(E.Switch,"infoDisabledColor",re(E.info.main,.55)),W(E.Switch,"successDisabledColor",re(E.success.main,.55)),W(E.Switch,"warningDisabledColor",re(E.warning.main,.55)),W(E.TableCell,"border",re(_s(E.divider,1),.68)),W(E.Tooltip,"bg",_s(E.grey[700],.92))}va(E.background,"default"),va(E.background,"paper"),va(E.common,"background"),va(E.common,"onBackground"),va(E,"divider"),Object.keys(E).forEach(X=>{const at=E[X];X!=="tonalOffset"&&at&&typeof at=="object"&&(at.main&&W(E[X],"mainChannel",Gl(Il(at.main))),at.light&&W(E[X],"lightChannel",Gl(Il(at.light))),at.dark&&W(E[X],"darkChannel",Gl(Il(at.dark))),at.contrastText&&W(E[X],"contrastTextChannel",Gl(Il(at.contrastText))),X==="text"&&(va(E[X],"primary"),va(E[X],"secondary")),X==="action"&&(at.active&&va(E[X],"active"),at.selected&&va(E[X],"selected")))})}),O=r.reduce((K,E)=>We(K,E),O);const _={prefix:c,disableCssColorScheme:s,shouldSkipGeneratingVar:d,getSelector:wT(O)},{vars:L,generateThemeVars:Y,generateStyleSheets:F}=zE(O,_);return O.vars=L,Object.entries(O.colorSchemes[O.defaultColorScheme]).forEach(([K,E])=>{O[K]=E}),O.generateThemeVars=Y,O.generateStyleSheets=F,O.generateSpacing=function(){return hv(h.spacing,Hd(this))},O.getColorSchemeSelector=DE(p),O.spacing=O.generateSpacing(),O.shouldSkipGeneratingVar=d,O.unstable_sxConfig={...di,...h==null?void 0:h.unstable_sxConfig},O.unstable_sx=function(E){return Wa({sx:E,theme:this})},O.toRuntimeSource=Dv,O}function Ky(n,r,o){n.colorSchemes&&o&&(n.colorSchemes[r]={...o!==!0&&o,palette:Xd({...o===!0?{}:o.palette,mode:r})})}function hi(n={},...r){const{palette:o,cssVariables:i=!1,colorSchemes:s=o?void 0:{light:!0},defaultColorScheme:c=o==null?void 0:o.mode,...d}=n,p=c||"light",m=s==null?void 0:s[p],h={...s,...o?{[p]:{...typeof m!="boolean"&&m,palette:o}}:void 0};if(i===!1){if(!("colorSchemes"in n))return cd(n,...r);let y=o;"palette"in n||h[p]&&(h[p]!==!0?y=h[p].palette:p==="dark"&&(y={mode:"dark"}));const b=cd({...n,palette:y},...r);return b.defaultColorScheme=p,b.colorSchemes=h,b.palette.mode==="light"&&(b.colorSchemes.light={...h.light!==!0&&h.light,palette:b.palette},Ky(b,"dark",h.dark)),b.palette.mode==="dark"&&(b.colorSchemes.dark={...h.dark!==!0&&h.dark,palette:b.palette},Ky(b,"light",h.light)),b}return!o&&!("light"in h)&&p==="light"&&(h.light=!0),_T({...d,colorSchemes:h,defaultColorScheme:p,...typeof i!="boolean"&&i},...r)}const Kd=hi();function gi(){const n=mi(Kd);return n[Zn]||n}function $v(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}const Sn=n=>$v(n)&&n!=="classes",st=xv({themeId:Zn,defaultTheme:Kd,rootShouldForwardProp:Sn});function jT({theme:n,...r}){const o=Zn in n?n[Zn]:void 0;return g.jsx(Ov,{...r,themeId:o?Zn:void 0,theme:o||n})}const Ns={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:NT}=OE({themeId:Zn,theme:()=>hi({cssVariables:!0}),colorSchemeStorageKey:Ns.colorSchemeStorageKey,modeStorageKey:Ns.modeStorageKey,defaultColorScheme:{light:Ns.defaultLightColorScheme,dark:Ns.defaultDarkColorScheme},resolveTheme:n=>{const r={...n,typography:zv(n.palette,n.typography)};return r.unstable_sx=function(i){return Wa({sx:i,theme:this})},r}}),zT=NT;function DT({theme:n,...r}){const o=S.useMemo(()=>{if(typeof n=="function")return n;const i=Zn in n?n[Zn]:n;return"colorSchemes"in i?null:"vars"in i?n:{...n,vars:null}},[n]);return o?g.jsx(jT,{theme:o,...r}):g.jsx(zT,{theme:n,...r})}function Qy(...n){return n.reduce((r,o)=>o==null?r:function(...s){r.apply(this,s),o.apply(this,s)},()=>{})}function kT(n){return g.jsx(gv,{...n,defaultTheme:Kd,themeId:Zn})}function Qd(n){return function(o){return g.jsx(kT,{styles:typeof n=="function"?i=>n({theme:i,...o}):n})}}function BT(){return Gd}const $t=xE;function Ot(n){return yE(n)}function $T(n){return Mt("MuiSvgIcon",n)}Rt("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const LT=n=>{const{color:r,fontSize:o,classes:i}=n,s={root:["root",r!=="inherit"&&`color${ft(r)}`,`fontSize${ft(o)}`]};return At(s,$T,i)},UT=st("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.color!=="inherit"&&r[`color${ft(o.color)}`],r[`fontSize${ft(o.fontSize)}`]]}})($t(({theme:n})=>{var r,o,i,s,c,d,p,m,h,y,b,x,R,C;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(s=(r=n.transitions)==null?void 0:r.create)==null?void 0:s.call(r,"fill",{duration:(i=(o=(n.vars??n).transitions)==null?void 0:o.duration)==null?void 0:i.shorter}),variants:[{props:w=>!w.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((d=(c=n.typography)==null?void 0:c.pxToRem)==null?void 0:d.call(c,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((m=(p=n.typography)==null?void 0:p.pxToRem)==null?void 0:m.call(p,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((y=(h=n.typography)==null?void 0:h.pxToRem)==null?void 0:y.call(h,35))||"2.1875rem"}},...Object.entries((n.vars??n).palette).filter(([,w])=>w&&w.main).map(([w])=>{var M,j;return{props:{color:w},style:{color:(j=(M=(n.vars??n).palette)==null?void 0:M[w])==null?void 0:j.main}}}),{props:{color:"action"},style:{color:(x=(b=(n.vars??n).palette)==null?void 0:b.action)==null?void 0:x.active}},{props:{color:"disabled"},style:{color:(C=(R=(n.vars??n).palette)==null?void 0:R.action)==null?void 0:C.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),dd=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiSvgIcon"}),{children:s,className:c,color:d="inherit",component:p="svg",fontSize:m="medium",htmlColor:h,inheritViewBox:y=!1,titleAccess:b,viewBox:x="0 0 24 24",...R}=i,C=S.isValidElement(s)&&s.type==="svg",w={...i,color:d,component:p,fontSize:m,instanceFontSize:r.fontSize,inheritViewBox:y,viewBox:x,hasSvgAsChild:C},M={};y||(M.viewBox=x);const j=LT(w);return g.jsxs(UT,{as:p,className:mt(j.root,c),focusable:"false",color:h,"aria-hidden":b?void 0:!0,role:b?"img":void 0,ref:o,...M,...R,...C&&s.props,ownerState:w,children:[C?s.props.children:s,b?g.jsx("title",{children:b}):null]})});dd.muiName="SvgIcon";function Ue(n,r){function o(i,s){return g.jsx(dd,{"data-testid":void 0,ref:s,...i,children:n})}return o.muiName=dd.muiName,S.memo(S.forwardRef(o))}function Lv(n,r=166){let o;function i(...s){const c=()=>{n.apply(this,s)};clearTimeout(o),o=setTimeout(c,r)}return i.clear=()=>{clearTimeout(o)},i}function Hn(n){return n&&n.ownerDocument||document}function Ca(n){return Hn(n).defaultView||window}function Wy(n,r){typeof n=="function"?n(r):n&&(n.current=r)}function pd(n){const{controlled:r,default:o,name:i,state:s="value"}=n,{current:c}=S.useRef(r!==void 0),[d,p]=S.useState(o),m=c?r:d,h=S.useCallback(y=>{c||p(y)},[]);return[m,h]}function Rr(n){const r=S.useRef(n);return ta(()=>{r.current=n}),S.useRef((...o)=>(0,r.current)(...o)).current}function tn(...n){const r=S.useRef(void 0),o=S.useCallback(i=>{const s=n.map(c=>{if(c==null)return null;if(typeof c=="function"){const d=c,p=d(i);return typeof p=="function"?p:()=>{d(null)}}return c.current=i,()=>{c.current=null}});return()=>{s.forEach(c=>c==null?void 0:c())}},n);return S.useMemo(()=>n.every(i=>i==null)?null:i=>{r.current&&(r.current(),r.current=void 0),i!=null&&(r.current=o(i))},n)}function HT(n,r){const o=n.charCodeAt(2);return n[0]==="o"&&n[1]==="n"&&o>=65&&o<=90&&typeof r=="function"}function PT(n,r){if(!n)return r;function o(d,p){const m={};return Object.keys(p).forEach(h=>{HT(h,p[h])&&typeof d[h]=="function"&&(m[h]=(...y)=>{d[h](...y),p[h](...y)})}),m}if(typeof n=="function"||typeof r=="function")return d=>{const p=typeof r=="function"?r(d):r,m=typeof n=="function"?n({...d,...p}):n,h=mt(d==null?void 0:d.className,p==null?void 0:p.className,m==null?void 0:m.className),y=o(m,p);return{...p,...m,...y,...!!h&&{className:h},...(p==null?void 0:p.style)&&(m==null?void 0:m.style)&&{style:{...p.style,...m.style}},...(p==null?void 0:p.sx)&&(m==null?void 0:m.sx)&&{sx:[...Array.isArray(p.sx)?p.sx:[p.sx],...Array.isArray(m.sx)?m.sx:[m.sx]]}}};const i=r,s=o(n,i),c=mt(i==null?void 0:i.className,n==null?void 0:n.className);return{...r,...n,...s,...!!c&&{className:c},...(i==null?void 0:i.style)&&(n==null?void 0:n.style)&&{style:{...i.style,...n.style}},...(i==null?void 0:i.sx)&&(n==null?void 0:n.sx)&&{sx:[...Array.isArray(i.sx)?i.sx:[i.sx],...Array.isArray(n.sx)?n.sx:[n.sx]]}}}function Uv(n,r){if(n==null)return{};var o={};for(var i in n)if({}.hasOwnProperty.call(n,i)){if(r.indexOf(i)!==-1)continue;o[i]=n[i]}return o}function md(n,r){return md=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,i){return o.__proto__=i,o},md(n,r)}function Hv(n,r){n.prototype=Object.create(r.prototype),n.prototype.constructor=n,md(n,r)}const Zy={disabled:!1},Ks=Wn.createContext(null);var qT=function(r){return r.scrollTop},Vl="unmounted",Sr="exited",xr="entering",Ro="entered",hd="exiting",ea=function(n){Hv(r,n);function r(i,s){var c;c=n.call(this,i,s)||this;var d=s,p=d&&!d.isMounting?i.enter:i.appear,m;return c.appearStatus=null,i.in?p?(m=Sr,c.appearStatus=xr):m=Ro:i.unmountOnExit||i.mountOnEnter?m=Vl:m=Sr,c.state={status:m},c.nextCallback=null,c}r.getDerivedStateFromProps=function(s,c){var d=s.in;return d&&c.status===Vl?{status:Sr}:null};var o=r.prototype;return o.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},o.componentDidUpdate=function(s){var c=null;if(s!==this.props){var d=this.state.status;this.props.in?d!==xr&&d!==Ro&&(c=xr):(d===xr||d===Ro)&&(c=hd)}this.updateStatus(!1,c)},o.componentWillUnmount=function(){this.cancelNextCallback()},o.getTimeouts=function(){var s=this.props.timeout,c,d,p;return c=d=p=s,s!=null&&typeof s!="number"&&(c=s.exit,d=s.enter,p=s.appear!==void 0?s.appear:d),{exit:c,enter:d,appear:p}},o.updateStatus=function(s,c){if(s===void 0&&(s=!1),c!==null)if(this.cancelNextCallback(),c===xr){if(this.props.unmountOnExit||this.props.mountOnEnter){var d=this.props.nodeRef?this.props.nodeRef.current:As.findDOMNode(this);d&&qT(d)}this.performEnter(s)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Sr&&this.setState({status:Vl})},o.performEnter=function(s){var c=this,d=this.props.enter,p=this.context?this.context.isMounting:s,m=this.props.nodeRef?[p]:[As.findDOMNode(this),p],h=m[0],y=m[1],b=this.getTimeouts(),x=p?b.appear:b.enter;if(!s&&!d||Zy.disabled){this.safeSetState({status:Ro},function(){c.props.onEntered(h)});return}this.props.onEnter(h,y),this.safeSetState({status:xr},function(){c.props.onEntering(h,y),c.onTransitionEnd(x,function(){c.safeSetState({status:Ro},function(){c.props.onEntered(h,y)})})})},o.performExit=function(){var s=this,c=this.props.exit,d=this.getTimeouts(),p=this.props.nodeRef?void 0:As.findDOMNode(this);if(!c||Zy.disabled){this.safeSetState({status:Sr},function(){s.props.onExited(p)});return}this.props.onExit(p),this.safeSetState({status:hd},function(){s.props.onExiting(p),s.onTransitionEnd(d.exit,function(){s.safeSetState({status:Sr},function(){s.props.onExited(p)})})})},o.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},o.safeSetState=function(s,c){c=this.setNextCallback(c),this.setState(s,c)},o.setNextCallback=function(s){var c=this,d=!0;return this.nextCallback=function(p){d&&(d=!1,c.nextCallback=null,s(p))},this.nextCallback.cancel=function(){d=!1},this.nextCallback},o.onTransitionEnd=function(s,c){this.setNextCallback(c);var d=this.props.nodeRef?this.props.nodeRef.current:As.findDOMNode(this),p=s==null&&!this.props.addEndListener;if(!d||p){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var m=this.props.nodeRef?[this.nextCallback]:[d,this.nextCallback],h=m[0],y=m[1];this.props.addEndListener(h,y)}s!=null&&setTimeout(this.nextCallback,s)},o.render=function(){var s=this.state.status;if(s===Vl)return null;var c=this.props,d=c.children;c.in,c.mountOnEnter,c.unmountOnExit,c.appear,c.enter,c.exit,c.timeout,c.addEndListener,c.onEnter,c.onEntering,c.onEntered,c.onExit,c.onExiting,c.onExited,c.nodeRef;var p=Uv(c,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Wn.createElement(Ks.Provider,{value:null},typeof d=="function"?d(s,p):Wn.cloneElement(Wn.Children.only(d),p))},r}(Wn.Component);ea.contextType=Ks;ea.propTypes={};function To(){}ea.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:To,onEntering:To,onEntered:To,onExit:To,onExiting:To,onExited:To};ea.UNMOUNTED=Vl;ea.EXITED=Sr;ea.ENTERING=xr;ea.ENTERED=Ro;ea.EXITING=hd;function GT(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function Wd(n,r){var o=function(c){return r&&S.isValidElement(c)?r(c):c},i=Object.create(null);return n&&S.Children.map(n,function(s){return s}).forEach(function(s){i[s.key]=o(s)}),i}function IT(n,r){n=n||{},r=r||{};function o(y){return y in r?r[y]:n[y]}var i=Object.create(null),s=[];for(var c in n)c in r?s.length&&(i[c]=s,s=[]):s.push(c);var d,p={};for(var m in r){if(i[m])for(d=0;d<i[m].length;d++){var h=i[m][d];p[i[m][d]]=o(h)}p[m]=o(m)}for(d=0;d<s.length;d++)p[s[d]]=o(s[d]);return p}function Er(n,r,o){return o[r]!=null?o[r]:n.props[r]}function VT(n,r){return Wd(n.children,function(o){return S.cloneElement(o,{onExited:r.bind(null,o),in:!0,appear:Er(o,"appear",n),enter:Er(o,"enter",n),exit:Er(o,"exit",n)})})}function YT(n,r,o){var i=Wd(n.children),s=IT(r,i);return Object.keys(s).forEach(function(c){var d=s[c];if(S.isValidElement(d)){var p=c in r,m=c in i,h=r[c],y=S.isValidElement(h)&&!h.props.in;m&&(!p||y)?s[c]=S.cloneElement(d,{onExited:o.bind(null,d),in:!0,exit:Er(d,"exit",n),enter:Er(d,"enter",n)}):!m&&p&&!y?s[c]=S.cloneElement(d,{in:!1}):m&&p&&S.isValidElement(h)&&(s[c]=S.cloneElement(d,{onExited:o.bind(null,d),in:h.props.in,exit:Er(d,"exit",n),enter:Er(d,"enter",n)}))}}),s}var FT=Object.values||function(n){return Object.keys(n).map(function(r){return n[r]})},XT={component:"div",childFactory:function(r){return r}},Zd=function(n){Hv(r,n);function r(i,s){var c;c=n.call(this,i,s)||this;var d=c.handleExited.bind(GT(c));return c.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},c}var o=r.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(s,c){var d=c.children,p=c.handleExited,m=c.firstRender;return{children:m?VT(s,p):YT(s,d,p),firstRender:!1}},o.handleExited=function(s,c){var d=Wd(this.props.children);s.key in d||(s.props.onExited&&s.props.onExited(c),this.mounted&&this.setState(function(p){var m=Vs({},p.children);return delete m[s.key],{children:m}}))},o.render=function(){var s=this.props,c=s.component,d=s.childFactory,p=Uv(s,["component","childFactory"]),m=this.state.contextValue,h=FT(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,c===null?Wn.createElement(Ks.Provider,{value:m},h):Wn.createElement(Ks.Provider,{value:m},Wn.createElement(c,p,h))},r}(Wn.Component);Zd.propTypes={};Zd.defaultProps=XT;const Jy={};function Pv(n,r){const o=S.useRef(Jy);return o.current===Jy&&(o.current=n(r)),o}const KT=[];function QT(n){S.useEffect(n,KT)}class Jd{constructor(){Dl(this,"currentId",null);Dl(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Dl(this,"disposeEffect",()=>this.clear)}static create(){return new Jd}start(r,o){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,o()},r)}}function qv(){const n=Pv(Jd.create).current;return QT(n.disposeEffect),n}const Gv=n=>n.scrollTop;function Qs(n,r){const{timeout:o,easing:i,style:s={}}=n;return{duration:s.transitionDuration??(typeof o=="number"?o:o[r.mode]||0),easing:s.transitionTimingFunction??(typeof i=="object"?i[r.mode]:i),delay:s.transitionDelay}}function WT(n){return Mt("MuiPaper",n)}Rt("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const ZT=n=>{const{square:r,elevation:o,variant:i,classes:s}=n,c={root:["root",i,!r&&"rounded",i==="elevation"&&`elevation${o}`]};return At(c,WT,s)},JT=st("div",{name:"MuiPaper",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],!o.square&&r.rounded,o.variant==="elevation"&&r[`elevation${o.elevation}`]]}})($t(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,color:(n.vars||n).palette.text.primary,transition:n.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:n.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(n.vars||n).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),_r=S.forwardRef(function(r,o){var R;const i=Ot({props:r,name:"MuiPaper"}),s=gi(),{className:c,component:d="div",elevation:p=1,square:m=!1,variant:h="elevation",...y}=i,b={...i,component:d,elevation:p,square:m,variant:h},x=ZT(b);return g.jsx(JT,{as:d,ownerState:b,className:mt(x.root,c),ref:o,...y,style:{...h==="elevation"&&{"--Paper-shadow":(s.vars||s).shadows[p],...s.vars&&{"--Paper-overlay":(R=s.vars.overlays)==null?void 0:R[p]},...!s.vars&&s.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${Oe("#fff",fd(p))}, ${Oe("#fff",fd(p))})`}},...y.style}})});function tR(n){return typeof n=="string"}function Iv(n,r,o){return n===void 0||tR(n)?r:{...r,ownerState:{...r.ownerState,...o}}}function Vv(n,r,o){return typeof n=="function"?n(r,o):n}function Yv(n,r=[]){if(n===void 0)return{};const o={};return Object.keys(n).filter(i=>i.match(/^on[A-Z]/)&&typeof n[i]=="function"&&!r.includes(i)).forEach(i=>{o[i]=n[i]}),o}function t0(n){if(n===void 0)return{};const r={};return Object.keys(n).filter(o=>!(o.match(/^on[A-Z]/)&&typeof n[o]=="function")).forEach(o=>{r[o]=n[o]}),r}function Fv(n){const{getSlotProps:r,additionalProps:o,externalSlotProps:i,externalForwardedProps:s,className:c}=n;if(!r){const R=mt(o==null?void 0:o.className,c,s==null?void 0:s.className,i==null?void 0:i.className),C={...o==null?void 0:o.style,...s==null?void 0:s.style,...i==null?void 0:i.style},w={...o,...s,...i};return R.length>0&&(w.className=R),Object.keys(C).length>0&&(w.style=C),{props:w,internalRef:void 0}}const d=Yv({...s,...i}),p=t0(i),m=t0(s),h=r(d),y=mt(h==null?void 0:h.className,o==null?void 0:o.className,c,s==null?void 0:s.className,i==null?void 0:i.className),b={...h==null?void 0:h.style,...o==null?void 0:o.style,...s==null?void 0:s.style,...i==null?void 0:i.style},x={...h,...o,...m,...p};return y.length>0&&(x.className=y),Object.keys(b).length>0&&(x.style=b),{props:x,internalRef:h.ref}}function kt(n,r){const{className:o,elementType:i,ownerState:s,externalForwardedProps:c,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...m}=r,{component:h,slots:y={[n]:void 0},slotProps:b={[n]:void 0},...x}=c,R=y[n]||i,C=Vv(b[n],s),{props:{component:w,...M},internalRef:j}=Fv({className:o,...m,externalForwardedProps:n==="root"?x:void 0,externalSlotProps:C}),D=tn(j,C==null?void 0:C.ref,r.ref),A=n==="root"?w||h:w,O=Iv(R,{...n==="root"&&!h&&!y[n]&&d,...n!=="root"&&!y[n]&&d,...M,...A&&!p&&{as:A},...A&&p&&{component:A},ref:D},s);return[R,O]}function e0(n){try{return n.matches(":focus-visible")}catch{}return!1}class Ws{constructor(){Dl(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new Ws}static use(){const r=Pv(Ws.create).current,[o,i]=S.useState(!1);return r.shouldMount=o,r.setShouldMount=i,S.useEffect(r.mountEffect,[o]),r}mount(){return this.mounted||(this.mounted=nR(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.start(...r)})}stop(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.stop(...r)})}pulsate(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.pulsate(...r)})}}function eR(){return Ws.use()}function nR(){let n,r;const o=new Promise((i,s)=>{n=i,r=s});return o.resolve=n,o.reject=r,o}function aR(n){const{className:r,classes:o,pulsate:i=!1,rippleX:s,rippleY:c,rippleSize:d,in:p,onExited:m,timeout:h}=n,[y,b]=S.useState(!1),x=mt(r,o.ripple,o.rippleVisible,i&&o.ripplePulsate),R={width:d,height:d,top:-(d/2)+c,left:-(d/2)+s},C=mt(o.child,y&&o.childLeaving,i&&o.childPulsate);return!p&&!y&&b(!0),S.useEffect(()=>{if(!p&&m!=null){const w=setTimeout(m,h);return()=>{clearTimeout(w)}}},[m,p,h]),g.jsx("span",{className:x,style:R,children:g.jsx("span",{className:C})})}const Nn=Rt("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),gd=550,rR=80,oR=ui`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,lR=ui`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,iR=ui`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,sR=st("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),uR=st(aR,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${Nn.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${oR};
    animation-duration: ${gd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  &.${Nn.ripplePulsate} {
    animation-duration: ${({theme:n})=>n.transitions.duration.shorter}ms;
  }

  & .${Nn.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${Nn.childLeaving} {
    opacity: 0;
    animation-name: ${lR};
    animation-duration: ${gd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  & .${Nn.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${iR};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,cR=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTouchRipple"}),{center:s=!1,classes:c={},className:d,...p}=i,[m,h]=S.useState([]),y=S.useRef(0),b=S.useRef(null);S.useEffect(()=>{b.current&&(b.current(),b.current=null)},[m]);const x=S.useRef(!1),R=qv(),C=S.useRef(null),w=S.useRef(null),M=S.useCallback(O=>{const{pulsate:_,rippleX:L,rippleY:Y,rippleSize:F,cb:K}=O;h(E=>[...E,g.jsx(uR,{classes:{ripple:mt(c.ripple,Nn.ripple),rippleVisible:mt(c.rippleVisible,Nn.rippleVisible),ripplePulsate:mt(c.ripplePulsate,Nn.ripplePulsate),child:mt(c.child,Nn.child),childLeaving:mt(c.childLeaving,Nn.childLeaving),childPulsate:mt(c.childPulsate,Nn.childPulsate)},timeout:gd,pulsate:_,rippleX:L,rippleY:Y,rippleSize:F},y.current)]),y.current+=1,b.current=K},[c]),j=S.useCallback((O={},_={},L=()=>{})=>{const{pulsate:Y=!1,center:F=s||_.pulsate,fakeElement:K=!1}=_;if((O==null?void 0:O.type)==="mousedown"&&x.current){x.current=!1;return}(O==null?void 0:O.type)==="touchstart"&&(x.current=!0);const E=K?null:w.current,U=E?E.getBoundingClientRect():{width:0,height:0,left:0,top:0};let X,at,it;if(F||O===void 0||O.clientX===0&&O.clientY===0||!O.clientX&&!O.touches)X=Math.round(U.width/2),at=Math.round(U.height/2);else{const{clientX:Q,clientY:k}=O.touches&&O.touches.length>0?O.touches[0]:O;X=Math.round(Q-U.left),at=Math.round(k-U.top)}if(F)it=Math.sqrt((2*U.width**2+U.height**2)/3),it%2===0&&(it+=1);else{const Q=Math.max(Math.abs((E?E.clientWidth:0)-X),X)*2+2,k=Math.max(Math.abs((E?E.clientHeight:0)-at),at)*2+2;it=Math.sqrt(Q**2+k**2)}O!=null&&O.touches?C.current===null&&(C.current=()=>{M({pulsate:Y,rippleX:X,rippleY:at,rippleSize:it,cb:L})},R.start(rR,()=>{C.current&&(C.current(),C.current=null)})):M({pulsate:Y,rippleX:X,rippleY:at,rippleSize:it,cb:L})},[s,M,R]),D=S.useCallback(()=>{j({},{pulsate:!0})},[j]),A=S.useCallback((O,_)=>{if(R.clear(),(O==null?void 0:O.type)==="touchend"&&C.current){C.current(),C.current=null,R.start(0,()=>{A(O,_)});return}C.current=null,h(L=>L.length>0?L.slice(1):L),b.current=_},[R]);return S.useImperativeHandle(o,()=>({pulsate:D,start:j,stop:A}),[D,j,A]),g.jsx(sR,{className:mt(Nn.root,c.root,d),ref:w,...p,children:g.jsx(Zd,{component:null,exit:!0,children:m})})});function fR(n){return Mt("MuiButtonBase",n)}const dR=Rt("MuiButtonBase",["root","disabled","focusVisible"]),pR=n=>{const{disabled:r,focusVisible:o,focusVisibleClassName:i,classes:s}=n,d=At({root:["root",r&&"disabled",o&&"focusVisible"]},fR,s);return o&&i&&(d.root+=` ${i}`),d},mR=st("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${dR.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),pu=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiButtonBase"}),{action:s,centerRipple:c=!1,children:d,className:p,component:m="button",disabled:h=!1,disableRipple:y=!1,disableTouchRipple:b=!1,focusRipple:x=!1,focusVisibleClassName:R,LinkComponent:C="a",onBlur:w,onClick:M,onContextMenu:j,onDragLeave:D,onFocus:A,onFocusVisible:O,onKeyDown:_,onKeyUp:L,onMouseDown:Y,onMouseLeave:F,onMouseUp:K,onTouchEnd:E,onTouchMove:U,onTouchStart:X,tabIndex:at=0,TouchRippleProps:it,touchRippleRef:Q,type:k,...I}=i,ot=S.useRef(null),Z=eR(),N=tn(Z.ref,Q),[G,et]=S.useState(!1);h&&G&&et(!1),S.useImperativeHandle(s,()=>({focusVisible:()=>{et(!0),ot.current.focus()}}),[]);const tt=Z.shouldMount&&!y&&!h;S.useEffect(()=>{G&&x&&!y&&Z.pulsate()},[y,x,G,Z]);const ut=ba(Z,"start",Y,b),lt=ba(Z,"stop",j,b),ct=ba(Z,"stop",D,b),bt=ba(Z,"stop",K,b),wt=ba(Z,"stop",ht=>{G&&ht.preventDefault(),F&&F(ht)},b),zt=ba(Z,"start",X,b),gt=ba(Z,"stop",E,b),_t=ba(Z,"stop",U,b),xt=ba(Z,"stop",ht=>{e0(ht.target)||et(!1),w&&w(ht)},!1),qt=Rr(ht=>{ot.current||(ot.current=ht.currentTarget),e0(ht.target)&&(et(!0),O&&O(ht)),A&&A(ht)}),jt=()=>{const ht=ot.current;return m&&m!=="button"&&!(ht.tagName==="A"&&ht.href)},Xt=Rr(ht=>{x&&!ht.repeat&&G&&ht.key===" "&&Z.stop(ht,()=>{Z.start(ht)}),ht.target===ht.currentTarget&&jt()&&ht.key===" "&&ht.preventDefault(),_&&_(ht),ht.target===ht.currentTarget&&jt()&&ht.key==="Enter"&&!h&&(ht.preventDefault(),M&&M(ht))}),He=Rr(ht=>{x&&ht.key===" "&&G&&!ht.defaultPrevented&&Z.stop(ht,()=>{Z.pulsate(ht)}),L&&L(ht),M&&ht.target===ht.currentTarget&&jt()&&ht.key===" "&&!ht.defaultPrevented&&M(ht)});let Kt=m;Kt==="button"&&(I.href||I.to)&&(Kt=C);const se={};Kt==="button"?(se.type=k===void 0?"button":k,se.disabled=h):(!I.href&&!I.to&&(se.role="button"),h&&(se["aria-disabled"]=h));const ie=tn(o,ot),de={...i,centerRipple:c,component:m,disabled:h,disableRipple:y,disableTouchRipple:b,focusRipple:x,tabIndex:at,focusVisible:G},Yt=pR(de);return g.jsxs(mR,{as:Kt,className:mt(Yt.root,p),ownerState:de,onBlur:xt,onClick:M,onContextMenu:lt,onFocus:qt,onKeyDown:Xt,onKeyUp:He,onMouseDown:ut,onMouseLeave:wt,onMouseUp:bt,onDragLeave:ct,onTouchEnd:gt,onTouchMove:_t,onTouchStart:zt,ref:ie,tabIndex:h?-1:at,type:k,...se,...I,children:[d,tt?g.jsx(cR,{ref:N,center:c,...it}):null]})});function ba(n,r,o,i=!1){return Rr(s=>(o&&o(s),i||n[r](s),!0))}function hR(n){return typeof n.main=="string"}function gR(n,r=[]){if(!hR(n))return!1;for(const o of r)if(!n.hasOwnProperty(o)||typeof n[o]!="string")return!1;return!0}function bn(n=[]){return([,r])=>r&&gR(r,n)}function yR(n){return Mt("MuiAlert",n)}const n0=Rt("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);function vR(n){return Mt("MuiCircularProgress",n)}Rt("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const Fa=44,yd=ui`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,vd=ui`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,bR=typeof yd!="string"?$d`
        animation: ${yd} 1.4s linear infinite;
      `:null,SR=typeof vd!="string"?$d`
        animation: ${vd} 1.4s ease-in-out infinite;
      `:null,xR=n=>{const{classes:r,variant:o,color:i,disableShrink:s}=n,c={root:["root",o,`color${ft(i)}`],svg:["svg"],circle:["circle",`circle${ft(o)}`,s&&"circleDisableShrink"]};return At(c,vR,r)},CR=st("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`color${ft(o.color)}`]]}})($t(({theme:n})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("transform")}},{props:{variant:"indeterminate"},style:bR||{animation:`${yd} 1.4s linear infinite`}},...Object.entries(n.palette).filter(bn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}}))]}))),ER=st("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),TR=st("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.circle,r[`circle${ft(o.variant)}`],o.disableShrink&&r.circleDisableShrink]}})($t(({theme:n})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:SR||{animation:`${vd} 1.4s ease-in-out infinite`}}]}))),Xv=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiCircularProgress"}),{className:s,color:c="primary",disableShrink:d=!1,size:p=40,style:m,thickness:h=3.6,value:y=0,variant:b="indeterminate",...x}=i,R={...i,color:c,disableShrink:d,size:p,thickness:h,value:y,variant:b},C=xR(R),w={},M={},j={};if(b==="determinate"){const D=2*Math.PI*((Fa-h)/2);w.strokeDasharray=D.toFixed(3),j["aria-valuenow"]=Math.round(y),w.strokeDashoffset=`${((100-y)/100*D).toFixed(3)}px`,M.transform="rotate(-90deg)"}return g.jsx(CR,{className:mt(C.root,s),style:{width:p,height:p,...M,...m},ownerState:R,ref:o,role:"progressbar",...j,...x,children:g.jsx(ER,{className:C.svg,ownerState:R,viewBox:`${Fa/2} ${Fa/2} ${Fa} ${Fa}`,children:g.jsx(TR,{className:C.circle,style:w,ownerState:R,cx:Fa,cy:Fa,r:(Fa-h)/2,fill:"none",strokeWidth:h})})})});function RR(n){return Mt("MuiIconButton",n)}const a0=Rt("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),wR=n=>{const{classes:r,disabled:o,color:i,edge:s,size:c,loading:d}=n,p={root:["root",d&&"loading",o&&"disabled",i!=="default"&&`color${ft(i)}`,s&&`edge${ft(s)}`,`size${ft(c)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return At(p,RR,r)},MR=st(pu,{name:"MuiIconButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.loading&&r.loading,o.color!=="default"&&r[`color${ft(o.color)}`],o.edge&&r[`edge${ft(o.edge)}`],r[`size${ft(o.size)}`]]}})($t(({theme:n})=>({textAlign:"center",flex:"0 0 auto",fontSize:n.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(n.vars||n).palette.action.active,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette.action.active,n.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),$t(({theme:n})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(n.palette).filter(bn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette).filter(bn()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":n.vars?`rgba(${(n.vars||n).palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe((n.vars||n).palette[r].main,n.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:n.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:n.typography.pxToRem(28)}}],[`&.${a0.disabled}`]:{backgroundColor:"transparent",color:(n.vars||n).palette.action.disabled},[`&.${a0.loading}`]:{color:"transparent"}}))),AR=st("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(n.vars||n).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),Tr=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiIconButton"}),{edge:s=!1,children:c,className:d,color:p="default",disabled:m=!1,disableFocusRipple:h=!1,size:y="medium",id:b,loading:x=null,loadingIndicator:R,...C}=i,w=Ja(b),M=R??g.jsx(Xv,{"aria-labelledby":w,color:"inherit",size:16}),j={...i,edge:s,color:p,disabled:m,disableFocusRipple:h,loading:x,loadingIndicator:M,size:y},D=wR(j);return g.jsxs(MR,{id:x?w:b,className:mt(D.root,d),centerRipple:!0,focusRipple:!h,disabled:m||x,ref:o,...C,ownerState:j,children:[typeof x=="boolean"&&g.jsx("span",{className:D.loadingWrapper,style:{display:"contents"},children:g.jsx(AR,{className:D.loadingIndicator,ownerState:j,children:x&&M})}),c]})}),OR=Ue(g.jsx("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"})),_R=Ue(g.jsx("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"})),jR=Ue(g.jsx("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"})),NR=Ue(g.jsx("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"})),zR=Ue(g.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),DR=n=>{const{variant:r,color:o,severity:i,classes:s}=n,c={root:["root",`color${ft(o||i)}`,`${r}${ft(o||i)}`,`${r}`],icon:["icon"],message:["message"],action:["action"]};return At(c,yR,s)},kR=st(_r,{name:"MuiAlert",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`${o.variant}${ft(o.color||o.severity)}`]]}})($t(({theme:n})=>{const r=n.palette.mode==="light"?wr:Mr,o=n.palette.mode==="light"?Mr:wr;return{...n.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(n.palette).filter(bn(["light"])).map(([i])=>({props:{colorSeverity:i,variant:"standard"},style:{color:n.vars?n.vars.palette.Alert[`${i}Color`]:r(n.palette[i].light,.6),backgroundColor:n.vars?n.vars.palette.Alert[`${i}StandardBg`]:o(n.palette[i].light,.9),[`& .${n0.icon}`]:n.vars?{color:n.vars.palette.Alert[`${i}IconColor`]}:{color:n.palette[i].main}}})),...Object.entries(n.palette).filter(bn(["light"])).map(([i])=>({props:{colorSeverity:i,variant:"outlined"},style:{color:n.vars?n.vars.palette.Alert[`${i}Color`]:r(n.palette[i].light,.6),border:`1px solid ${(n.vars||n).palette[i].light}`,[`& .${n0.icon}`]:n.vars?{color:n.vars.palette.Alert[`${i}IconColor`]}:{color:n.palette[i].main}}})),...Object.entries(n.palette).filter(bn(["dark"])).map(([i])=>({props:{colorSeverity:i,variant:"filled"},style:{fontWeight:n.typography.fontWeightMedium,...n.vars?{color:n.vars.palette.Alert[`${i}FilledColor`],backgroundColor:n.vars.palette.Alert[`${i}FilledBg`]}:{backgroundColor:n.palette.mode==="dark"?n.palette[i].dark:n.palette[i].main,color:n.palette.getContrastText(n.palette[i].main)}}}))]}})),BR=st("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),$R=st("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),LR=st("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),r0={success:g.jsx(OR,{fontSize:"inherit"}),warning:g.jsx(_R,{fontSize:"inherit"}),error:g.jsx(jR,{fontSize:"inherit"}),info:g.jsx(NR,{fontSize:"inherit"})},UR=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiAlert"}),{action:s,children:c,className:d,closeText:p="Close",color:m,components:h={},componentsProps:y={},icon:b,iconMapping:x=r0,onClose:R,role:C="alert",severity:w="success",slotProps:M={},slots:j={},variant:D="standard",...A}=i,O={...i,color:m,severity:w,variant:D,colorSeverity:m||w},_=DR(O),L={slots:{closeButton:h.CloseButton,closeIcon:h.CloseIcon,...j},slotProps:{...y,...M}},[Y,F]=kt("root",{ref:o,shouldForwardComponentProp:!0,className:mt(_.root,d),elementType:kR,externalForwardedProps:{...L,...A},ownerState:O,additionalProps:{role:C,elevation:0}}),[K,E]=kt("icon",{className:_.icon,elementType:BR,externalForwardedProps:L,ownerState:O}),[U,X]=kt("message",{className:_.message,elementType:$R,externalForwardedProps:L,ownerState:O}),[at,it]=kt("action",{className:_.action,elementType:LR,externalForwardedProps:L,ownerState:O}),[Q,k]=kt("closeButton",{elementType:Tr,externalForwardedProps:L,ownerState:O}),[I,ot]=kt("closeIcon",{elementType:zR,externalForwardedProps:L,ownerState:O});return g.jsxs(Y,{...F,children:[b!==!1?g.jsx(K,{...E,children:b||x[w]||r0[w]}):null,g.jsx(U,{...X,children:c}),s!=null?g.jsx(at,{...it,children:s}):null,s==null&&R?g.jsx(at,{...it,children:g.jsx(Q,{size:"small","aria-label":p,title:p,color:"inherit",onClick:R,...k,children:g.jsx(I,{fontSize:"small",...ot})})}):null]})});function HR(n){return Mt("MuiTypography",n)}Rt("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const PR={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},qR=BT(),GR=n=>{const{align:r,gutterBottom:o,noWrap:i,paragraph:s,variant:c,classes:d}=n,p={root:["root",c,n.align!=="inherit"&&`align${ft(r)}`,o&&"gutterBottom",i&&"noWrap",s&&"paragraph"]};return At(p,HR,d)},IR=st("span",{name:"MuiTypography",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.variant&&r[o.variant],o.align!=="inherit"&&r[`align${ft(o.align)}`],o.noWrap&&r.noWrap,o.gutterBottom&&r.gutterBottom,o.paragraph&&r.paragraph]}})($t(({theme:n})=>{var r;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter(([o,i])=>o!=="inherit"&&i&&typeof i=="object").map(([o,i])=>({props:{variant:o},style:i})),...Object.entries(n.palette).filter(bn()).map(([o])=>({props:{color:o},style:{color:(n.vars||n).palette[o].main}})),...Object.entries(((r=n.palette)==null?void 0:r.text)||{}).filter(([,o])=>typeof o=="string").map(([o])=>({props:{color:`text${ft(o)}`},style:{color:(n.vars||n).palette.text[o]}})),{props:({ownerState:o})=>o.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:o})=>o.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:o})=>o.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:o})=>o.paragraph,style:{marginBottom:16}}]}})),o0={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},mu=S.forwardRef(function(r,o){const{color:i,...s}=Ot({props:r,name:"MuiTypography"}),c=!PR[i],d=qR({...s,...c&&{color:i}}),{align:p="inherit",className:m,component:h,gutterBottom:y=!1,noWrap:b=!1,paragraph:x=!1,variant:R="body1",variantMapping:C=o0,...w}=d,M={...d,align:p,color:i,className:m,component:h,gutterBottom:y,noWrap:b,paragraph:x,variant:R,variantMapping:C},j=h||(x?"p":C[R]||o0[R])||"span",D=GR(M);return g.jsx(IR,{as:j,ref:o,className:mt(D.root,m),...w,ownerState:M,style:{...p!=="inherit"&&{"--Typography-textAlign":p},...w.style}})});function VR(n){var b;const{elementType:r,externalSlotProps:o,ownerState:i,skipResolvingSlotProps:s=!1,...c}=n,d=s?{}:Vv(o,i),{props:p,internalRef:m}=Fv({...c,externalSlotProps:d}),h=tn(m,d==null?void 0:d.ref,(b=n.additionalProps)==null?void 0:b.ref);return Iv(r,{...p,ref:h},i)}function yi(n){var r;return parseInt(S.version,10)>=19?((r=n==null?void 0:n.props)==null?void 0:r.ref)||null:(n==null?void 0:n.ref)||null}function YR(n){return typeof n=="function"?n():n}const FR=S.forwardRef(function(r,o){const{children:i,container:s,disablePortal:c=!1}=r,[d,p]=S.useState(null),m=tn(S.isValidElement(i)?yi(i):null,o);if(ta(()=>{c||p(YR(s)||document.body)},[s,c]),ta(()=>{if(d&&!c)return Wy(o,d),()=>{Wy(o,null)}},[o,d,c]),c){if(S.isValidElement(i)){const h={ref:m};return S.cloneElement(i,h)}return i}return d&&L0.createPortal(i,d)});function zs(n){return parseInt(n,10)||0}const XR={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function KR(n){for(const r in n)return!1;return!0}function l0(n){return KR(n)||n.outerHeightStyle===0&&!n.overflowing}const QR=S.forwardRef(function(r,o){const{onChange:i,maxRows:s,minRows:c=1,style:d,value:p,...m}=r,{current:h}=S.useRef(p!=null),y=S.useRef(null),b=tn(o,y),x=S.useRef(null),R=S.useRef(null),C=S.useCallback(()=>{const A=y.current,O=R.current;if(!A||!O)return;const L=Ca(A).getComputedStyle(A);if(L.width==="0px")return{outerHeightStyle:0,overflowing:!1};O.style.width=L.width,O.value=A.value||r.placeholder||"x",O.value.slice(-1)===`
`&&(O.value+=" ");const Y=L.boxSizing,F=zs(L.paddingBottom)+zs(L.paddingTop),K=zs(L.borderBottomWidth)+zs(L.borderTopWidth),E=O.scrollHeight;O.value="x";const U=O.scrollHeight;let X=E;c&&(X=Math.max(Number(c)*U,X)),s&&(X=Math.min(Number(s)*U,X)),X=Math.max(X,U);const at=X+(Y==="border-box"?F+K:0),it=Math.abs(X-E)<=1;return{outerHeightStyle:at,overflowing:it}},[s,c,r.placeholder]),w=Rr(()=>{const A=y.current,O=C();if(!A||!O||l0(O))return!1;const _=O.outerHeightStyle;return x.current!=null&&x.current!==_}),M=S.useCallback(()=>{const A=y.current,O=C();if(!A||!O||l0(O))return;const _=O.outerHeightStyle;x.current!==_&&(x.current=_,A.style.height=`${_}px`),A.style.overflow=O.overflowing?"hidden":""},[C]),j=S.useRef(-1);ta(()=>{const A=Lv(M),O=y==null?void 0:y.current;if(!O)return;const _=Ca(O);_.addEventListener("resize",A);let L;return typeof ResizeObserver<"u"&&(L=new ResizeObserver(()=>{w()&&(L.unobserve(O),cancelAnimationFrame(j.current),M(),j.current=requestAnimationFrame(()=>{L.observe(O)}))}),L.observe(O)),()=>{A.clear(),cancelAnimationFrame(j.current),_.removeEventListener("resize",A),L&&L.disconnect()}},[C,M,w]),ta(()=>{M()});const D=A=>{h||M();const O=A.target,_=O.value.length,L=O.value.endsWith(`
`),Y=O.selectionStart===_;L&&Y&&O.setSelectionRange(_,_),i&&i(A)};return g.jsxs(S.Fragment,{children:[g.jsx("textarea",{value:p,onChange:D,ref:b,rows:c,style:d,...m}),g.jsx("textarea",{"aria-hidden":!0,className:r.className,readOnly:!0,ref:R,tabIndex:-1,style:{...XR.shadow,...d,paddingTop:0,paddingBottom:0}})]})});function bd(n){return typeof n=="string"}function $o({props:n,states:r,muiFormControl:o}){return r.reduce((i,s)=>(i[s]=n[s],o&&typeof n[s]>"u"&&(i[s]=o[s]),i),{})}const tp=S.createContext(void 0);function jr(){return S.useContext(tp)}function i0(n){return n!=null&&!(Array.isArray(n)&&n.length===0)}function Zs(n,r=!1){return n&&(i0(n.value)&&n.value!==""||r&&i0(n.defaultValue)&&n.defaultValue!=="")}function WR(n){return n.startAdornment}function ZR(n){return Mt("MuiInputBase",n)}const jo=Rt("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var s0;const hu=(n,r)=>{const{ownerState:o}=n;return[r.root,o.formControl&&r.formControl,o.startAdornment&&r.adornedStart,o.endAdornment&&r.adornedEnd,o.error&&r.error,o.size==="small"&&r.sizeSmall,o.multiline&&r.multiline,o.color&&r[`color${ft(o.color)}`],o.fullWidth&&r.fullWidth,o.hiddenLabel&&r.hiddenLabel]},gu=(n,r)=>{const{ownerState:o}=n;return[r.input,o.size==="small"&&r.inputSizeSmall,o.multiline&&r.inputMultiline,o.type==="search"&&r.inputTypeSearch,o.startAdornment&&r.inputAdornedStart,o.endAdornment&&r.inputAdornedEnd,o.hiddenLabel&&r.inputHiddenLabel]},JR=n=>{const{classes:r,color:o,disabled:i,error:s,endAdornment:c,focused:d,formControl:p,fullWidth:m,hiddenLabel:h,multiline:y,readOnly:b,size:x,startAdornment:R,type:C}=n,w={root:["root",`color${ft(o)}`,i&&"disabled",s&&"error",m&&"fullWidth",d&&"focused",p&&"formControl",x&&x!=="medium"&&`size${ft(x)}`,y&&"multiline",R&&"adornedStart",c&&"adornedEnd",h&&"hiddenLabel",b&&"readOnly"],input:["input",i&&"disabled",C==="search"&&"inputTypeSearch",y&&"inputMultiline",x==="small"&&"inputSizeSmall",h&&"inputHiddenLabel",R&&"inputAdornedStart",c&&"inputAdornedEnd",b&&"readOnly"]};return At(w,ZR,r)},yu=st("div",{name:"MuiInputBase",slot:"Root",overridesResolver:hu})($t(({theme:n})=>({...n.typography.body1,color:(n.vars||n).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${jo.disabled}`]:{color:(n.vars||n).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:r})=>r.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:r,size:o})=>r.multiline&&o==="small",style:{paddingTop:1}},{props:({ownerState:r})=>r.fullWidth,style:{width:"100%"}}]}))),vu=st("input",{name:"MuiInputBase",slot:"Input",overridesResolver:gu})($t(({theme:n})=>{const r=n.palette.mode==="light",o={color:"currentColor",...n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:n.transitions.create("opacity",{duration:n.transitions.duration.shorter})},i={opacity:"0 !important"},s=n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${jo.formControl} &`]:{"&::-webkit-input-placeholder":i,"&::-moz-placeholder":i,"&::-ms-input-placeholder":i,"&:focus::-webkit-input-placeholder":s,"&:focus::-moz-placeholder":s,"&:focus::-ms-input-placeholder":s},[`&.${jo.disabled}`]:{opacity:1,WebkitTextFillColor:(n.vars||n).palette.text.disabled},variants:[{props:({ownerState:c})=>!c.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:c})=>c.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),u0=Qd({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),bu=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiInputBase"}),{"aria-describedby":s,autoComplete:c,autoFocus:d,className:p,color:m,components:h={},componentsProps:y={},defaultValue:b,disabled:x,disableInjectingGlobalStyles:R,endAdornment:C,error:w,fullWidth:M=!1,id:j,inputComponent:D="input",inputProps:A={},inputRef:O,margin:_,maxRows:L,minRows:Y,multiline:F=!1,name:K,onBlur:E,onChange:U,onClick:X,onFocus:at,onKeyDown:it,onKeyUp:Q,placeholder:k,readOnly:I,renderSuffix:ot,rows:Z,size:N,slotProps:G={},slots:et={},startAdornment:tt,type:ut="text",value:lt,...ct}=i,bt=A.value!=null?A.value:lt,{current:wt}=S.useRef(bt!=null),zt=S.useRef(),gt=S.useCallback(Et=>{},[]),_t=tn(zt,O,A.ref,gt),[xt,qt]=S.useState(!1),jt=jr(),Xt=$o({props:i,muiFormControl:jt,states:["color","disabled","error","hiddenLabel","size","required","filled"]});Xt.focused=jt?jt.focused:xt,S.useEffect(()=>{!jt&&x&&xt&&(qt(!1),E&&E())},[jt,x,xt,E]);const He=jt&&jt.onFilled,Kt=jt&&jt.onEmpty,se=S.useCallback(Et=>{Zs(Et)?He&&He():Kt&&Kt()},[He,Kt]);ta(()=>{wt&&se({value:bt})},[bt,se,wt]);const ie=Et=>{at&&at(Et),A.onFocus&&A.onFocus(Et),jt&&jt.onFocus?jt.onFocus(Et):qt(!0)},de=Et=>{E&&E(Et),A.onBlur&&A.onBlur(Et),jt&&jt.onBlur?jt.onBlur(Et):qt(!1)},Yt=(Et,...Ye)=>{if(!wt){const Te=Et.target||zt.current;if(Te==null)throw new Error(Sa(1));se({value:Te.value})}A.onChange&&A.onChange(Et,...Ye),U&&U(Et,...Ye)};S.useEffect(()=>{se(zt.current)},[]);const ht=Et=>{zt.current&&Et.currentTarget===Et.target&&zt.current.focus(),X&&X(Et)};let un=D,he=A;F&&un==="input"&&(Z?he={type:void 0,minRows:Z,maxRows:Z,...he}:he={type:void 0,maxRows:L,minRows:Y,...he},un=QR);const kn=Et=>{se(Et.animationName==="mui-auto-fill-cancel"?zt.current:{value:"x"})};S.useEffect(()=>{jt&&jt.setAdornedStart(!!tt)},[jt,tt]);const en={...i,color:Xt.color||"primary",disabled:Xt.disabled,endAdornment:C,error:Xt.error,focused:Xt.focused,formControl:jt,fullWidth:M,hiddenLabel:Xt.hiddenLabel,multiline:F,size:Xt.size,startAdornment:tt,type:ut},ge=JR(en),De=et.root||h.Root||yu,pe=G.root||y.root||{},ue=et.input||h.Input||vu;return he={...he,...G.input??y.input},g.jsxs(S.Fragment,{children:[!R&&typeof u0=="function"&&(s0||(s0=g.jsx(u0,{}))),g.jsxs(De,{...pe,ref:o,onClick:ht,...ct,...!bd(De)&&{ownerState:{...en,...pe.ownerState}},className:mt(ge.root,pe.className,p,I&&"MuiInputBase-readOnly"),children:[tt,g.jsx(tp.Provider,{value:null,children:g.jsx(ue,{"aria-invalid":Xt.error,"aria-describedby":s,autoComplete:c,autoFocus:d,defaultValue:b,disabled:Xt.disabled,id:j,onAnimationStart:kn,name:K,placeholder:k,readOnly:I,required:Xt.required,rows:Z,value:bt,onKeyDown:it,onKeyUp:Q,type:ut,...he,...!bd(ue)&&{as:un,ownerState:{...en,...he.ownerState}},ref:_t,className:mt(ge.input,he.className,I&&"MuiInputBase-readOnly"),onBlur:de,onChange:Yt,onFocus:ie})}),C,ot?ot({...Xt,startAdornment:tt}):null]})]})});function tw(n){return Mt("MuiInput",n)}const Ul={...jo,...Rt("MuiInput",["root","underline","input"])};function ew(n){return Mt("MuiOutlinedInput",n)}const Fn={...jo,...Rt("MuiOutlinedInput",["root","notchedOutline","input"])};function nw(n){return Mt("MuiFilledInput",n)}const vr={...jo,...Rt("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},aw=Ue(g.jsx("path",{d:"M7 10l5 5 5-5z"})),rw={entering:{opacity:1},entered:{opacity:1}},Sd=S.forwardRef(function(r,o){const i=gi(),s={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{addEndListener:c,appear:d=!0,children:p,easing:m,in:h,onEnter:y,onEntered:b,onEntering:x,onExit:R,onExited:C,onExiting:w,style:M,timeout:j=s,TransitionComponent:D=ea,...A}=r,O=S.useRef(null),_=tn(O,yi(p),o),L=it=>Q=>{if(it){const k=O.current;Q===void 0?it(k):it(k,Q)}},Y=L(x),F=L((it,Q)=>{Gv(it);const k=Qs({style:M,timeout:j,easing:m},{mode:"enter"});it.style.webkitTransition=i.transitions.create("opacity",k),it.style.transition=i.transitions.create("opacity",k),y&&y(it,Q)}),K=L(b),E=L(w),U=L(it=>{const Q=Qs({style:M,timeout:j,easing:m},{mode:"exit"});it.style.webkitTransition=i.transitions.create("opacity",Q),it.style.transition=i.transitions.create("opacity",Q),R&&R(it)}),X=L(C),at=it=>{c&&c(O.current,it)};return g.jsx(D,{appear:d,in:h,nodeRef:O,onEnter:F,onEntered:K,onEntering:Y,onExit:U,onExited:X,onExiting:E,addEndListener:at,timeout:j,...A,children:(it,{ownerState:Q,...k})=>S.cloneElement(p,{style:{opacity:0,visibility:it==="exited"&&!h?"hidden":void 0,...rw[it],...M,...p.props.style},ref:_,...k})})});function ow(n){return Mt("MuiBackdrop",n)}Rt("MuiBackdrop",["root","invisible"]);const lw=n=>{const{classes:r,invisible:o}=n;return At({root:["root",o&&"invisible"]},ow,r)},iw=st("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),Kv=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiBackdrop"}),{children:s,className:c,component:d="div",invisible:p=!1,open:m,components:h={},componentsProps:y={},slotProps:b={},slots:x={},TransitionComponent:R,transitionDuration:C,...w}=i,M={...i,component:d,invisible:p},j=lw(M),D={transition:R,root:h.Root,...x},A={...y,...b},O={component:d,slots:D,slotProps:A},[_,L]=kt("root",{elementType:iw,externalForwardedProps:O,className:mt(j.root,c),ownerState:M}),[Y,F]=kt("transition",{elementType:Sd,externalForwardedProps:O,ownerState:M});return g.jsx(Y,{in:m,timeout:C,...w,...F,children:g.jsx(_,{"aria-hidden":!0,...L,classes:j,ref:o,children:s})})}),sw=Rt("MuiBox",["root"]),uw=hi(),Js=K2({themeId:Zn,defaultTheme:uw,defaultClassName:sw.root,generateClassName:yv.generate});function cw(n){return Mt("MuiButton",n)}const br=Rt("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),fw=S.createContext({}),dw=S.createContext(void 0),pw=n=>{const{color:r,disableElevation:o,fullWidth:i,size:s,variant:c,loading:d,loadingPosition:p,classes:m}=n,h={root:["root",d&&"loading",c,`${c}${ft(r)}`,`size${ft(s)}`,`${c}Size${ft(s)}`,`color${ft(r)}`,o&&"disableElevation",i&&"fullWidth",d&&`loadingPosition${ft(p)}`],startIcon:["icon","startIcon",`iconSize${ft(s)}`],endIcon:["icon","endIcon",`iconSize${ft(s)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},y=At(h,cw,m);return{...m,...y}},Qv=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],mw=st(pu,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`${o.variant}${ft(o.color)}`],r[`size${ft(o.size)}`],r[`${o.variant}Size${ft(o.size)}`],o.color==="inherit"&&r.colorInherit,o.disableElevation&&r.disableElevation,o.fullWidth&&r.fullWidth,o.loading&&r.loading]}})($t(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[300]:n.palette.grey[800],o=n.palette.mode==="light"?n.palette.grey.A100:n.palette.grey[700];return{...n.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create(["background-color","box-shadow","border-color","color"],{duration:n.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${br.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(n.vars||n).shadows[2],"&:hover":{boxShadow:(n.vars||n).shadows[4],"@media (hover: none)":{boxShadow:(n.vars||n).shadows[2]}},"&:active":{boxShadow:(n.vars||n).shadows[8]},[`&.${br.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},[`&.${br.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${br.disabled}`]:{border:`1px solid ${(n.vars||n).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(n.palette).filter(bn()).map(([i])=>({props:{color:i},style:{"--variant-textColor":(n.vars||n).palette[i].main,"--variant-outlinedColor":(n.vars||n).palette[i].main,"--variant-outlinedBorder":n.vars?`rgba(${n.vars.palette[i].mainChannel} / 0.5)`:Oe(n.palette[i].main,.5),"--variant-containedColor":(n.vars||n).palette[i].contrastText,"--variant-containedBg":(n.vars||n).palette[i].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(n.vars||n).palette[i].dark,"--variant-textBg":n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette[i].main,n.palette.action.hoverOpacity),"--variant-outlinedBorder":(n.vars||n).palette[i].main,"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette[i].main,n.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedHoverBg:o,"--variant-textBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette.text.primary,n.palette.action.hoverOpacity),"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette.text.primary,n.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:n.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${br.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${br.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),[`&.${br.loading}`]:{color:"transparent"}}}]}})),hw=st("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.startIcon,o.loading&&r.startIconLoadingStart,r[`iconSize${ft(o.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...Qv]})),gw=st("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.endIcon,o.loading&&r.endIconLoadingEnd,r[`iconSize${ft(o.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...Qv]})),yw=st("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(n.vars||n).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),c0=st("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),Ps=S.forwardRef(function(r,o){const i=S.useContext(fw),s=S.useContext(dw),c=ai(i,r),d=Ot({props:c,name:"MuiButton"}),{children:p,color:m="primary",component:h="button",className:y,disabled:b=!1,disableElevation:x=!1,disableFocusRipple:R=!1,endIcon:C,focusVisibleClassName:w,fullWidth:M=!1,id:j,loading:D=null,loadingIndicator:A,loadingPosition:O="center",size:_="medium",startIcon:L,type:Y,variant:F="text",...K}=d,E=Ja(j),U=A??g.jsx(Xv,{"aria-labelledby":E,color:"inherit",size:16}),X={...d,color:m,component:h,disabled:b,disableElevation:x,disableFocusRipple:R,fullWidth:M,loading:D,loadingIndicator:U,loadingPosition:O,size:_,type:Y,variant:F},at=pw(X),it=(L||D&&O==="start")&&g.jsx(hw,{className:at.startIcon,ownerState:X,children:L||g.jsx(c0,{className:at.loadingIconPlaceholder,ownerState:X})}),Q=(C||D&&O==="end")&&g.jsx(gw,{className:at.endIcon,ownerState:X,children:C||g.jsx(c0,{className:at.loadingIconPlaceholder,ownerState:X})}),k=s||"",I=typeof D=="boolean"?g.jsx("span",{className:at.loadingWrapper,style:{display:"contents"},children:D&&g.jsx(yw,{className:at.loadingIndicator,ownerState:X,children:U})}):null;return g.jsxs(mw,{ownerState:X,className:mt(i.className,at.root,y,k),component:h,disabled:b||D,focusRipple:!R,focusVisibleClassName:mt(at.focusVisible,w),ref:o,type:Y,id:D?E:j,...K,classes:at,children:[it,O!=="end"&&I,p,O==="end"&&I,Q]})});function vw(n){return Mt("MuiCard",n)}Rt("MuiCard",["root"]);const bw=n=>{const{classes:r}=n;return At({root:["root"]},vw,r)},Sw=st(_r,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),xw=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiCard"}),{className:s,raised:c=!1,...d}=i,p={...i,raised:c},m=bw(p);return g.jsx(Sw,{className:mt(m.root,s),elevation:c?8:void 0,ref:o,ownerState:p,...d})});function Cw(n){return Mt("MuiCardContent",n)}Rt("MuiCardContent",["root"]);const Ew=n=>{const{classes:r}=n;return At({root:["root"]},Cw,r)},Tw=st("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),Rw=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiCardContent"}),{className:s,component:c="div",...d}=i,p={...i,component:c},m=Ew(p);return g.jsx(Tw,{as:c,className:mt(m.root,s),ownerState:p,ref:o,...d})});function ww(n){return Mt("PrivateSwitchBase",n)}Rt("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const Mw=n=>{const{classes:r,checked:o,disabled:i,edge:s}=n,c={root:["root",o&&"checked",i&&"disabled",s&&`edge${ft(s)}`],input:["input"]};return At(c,ww,r)},Aw=st(pu,{name:"MuiSwitchBase"})({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:n,ownerState:r})=>n==="start"&&r.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:n,ownerState:r})=>n==="end"&&r.size!=="small",style:{marginRight:-12}}]}),Ow=st("input",{name:"MuiSwitchBase",shouldForwardProp:Sn})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),_w=S.forwardRef(function(r,o){const{autoFocus:i,checked:s,checkedIcon:c,defaultChecked:d,disabled:p,disableFocusRipple:m=!1,edge:h=!1,icon:y,id:b,inputProps:x,inputRef:R,name:C,onBlur:w,onChange:M,onFocus:j,readOnly:D,required:A=!1,tabIndex:O,type:_,value:L,slots:Y={},slotProps:F={},...K}=r,[E,U]=pd({controlled:s,default:!!d,name:"SwitchBase",state:"checked"}),X=jr(),at=lt=>{j&&j(lt),X&&X.onFocus&&X.onFocus(lt)},it=lt=>{w&&w(lt),X&&X.onBlur&&X.onBlur(lt)},Q=lt=>{if(lt.nativeEvent.defaultPrevented)return;const ct=lt.target.checked;U(ct),M&&M(lt,ct)};let k=p;X&&typeof k>"u"&&(k=X.disabled);const I=_==="checkbox"||_==="radio",ot={...r,checked:E,disabled:k,disableFocusRipple:m,edge:h},Z=Mw(ot),N={slots:Y,slotProps:{input:x,...F}},[G,et]=kt("root",{ref:o,elementType:Aw,className:Z.root,shouldForwardComponentProp:!0,externalForwardedProps:{...N,component:"span",...K},getSlotProps:lt=>({...lt,onFocus:ct=>{var bt;(bt=lt.onFocus)==null||bt.call(lt,ct),at(ct)},onBlur:ct=>{var bt;(bt=lt.onBlur)==null||bt.call(lt,ct),it(ct)}}),ownerState:ot,additionalProps:{centerRipple:!0,focusRipple:!m,disabled:k,role:void 0,tabIndex:null}}),[tt,ut]=kt("input",{ref:R,elementType:Ow,className:Z.input,externalForwardedProps:N,getSlotProps:lt=>({...lt,onChange:ct=>{var bt;(bt=lt.onChange)==null||bt.call(lt,ct),Q(ct)}}),ownerState:ot,additionalProps:{autoFocus:i,checked:s,defaultChecked:d,disabled:k,id:I?b:void 0,name:C,readOnly:D,required:A,tabIndex:O,type:_,..._==="checkbox"&&L===void 0?{}:{value:L}}});return g.jsxs(G,{...et,children:[g.jsx(tt,{...ut}),E?c:y]})}),jw=UE({createStyledComponent:st("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`maxWidth${ft(String(o.maxWidth))}`],o.fixed&&r.fixed,o.disableGutters&&r.disableGutters]}}),useThemeProps:n=>Ot({props:n,name:"MuiContainer"})}),xd=typeof Qd({})=="function",Nw=(n,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!n.vars&&{colorScheme:n.palette.mode}}),zw=n=>({color:(n.vars||n).palette.text.primary,...n.typography.body1,backgroundColor:(n.vars||n).palette.background.default,"@media print":{backgroundColor:(n.vars||n).palette.common.white}}),Wv=(n,r=!1)=>{var c,d;const o={};r&&n.colorSchemes&&typeof n.getColorSchemeSelector=="function"&&Object.entries(n.colorSchemes).forEach(([p,m])=>{var y,b;const h=n.getColorSchemeSelector(p);h.startsWith("@")?o[h]={":root":{colorScheme:(y=m.palette)==null?void 0:y.mode}}:o[h.replace(/\s*&/,"")]={colorScheme:(b=m.palette)==null?void 0:b.mode}});let i={html:Nw(n,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:n.typography.fontWeightBold},body:{margin:0,...zw(n),"&::backdrop":{backgroundColor:(n.vars||n).palette.background.default}},...o};const s=(d=(c=n.components)==null?void 0:c.MuiCssBaseline)==null?void 0:d.styleOverrides;return s&&(i=[i,s]),i},qs="mui-ecs",Dw=n=>{const r=Wv(n,!1),o=Array.isArray(r)?r[0]:r;return!n.vars&&o&&(o.html[`:root:has(${qs})`]={colorScheme:n.palette.mode}),n.colorSchemes&&Object.entries(n.colorSchemes).forEach(([i,s])=>{var d,p;const c=n.getColorSchemeSelector(i);c.startsWith("@")?o[c]={[`:root:not(:has(.${qs}))`]:{colorScheme:(d=s.palette)==null?void 0:d.mode}}:o[c.replace(/\s*&/,"")]={[`&:not(:has(.${qs}))`]:{colorScheme:(p=s.palette)==null?void 0:p.mode}}}),r},kw=Qd(xd?({theme:n,enableColorScheme:r})=>Wv(n,r):({theme:n})=>Dw(n));function Bw(n){const r=Ot({props:n,name:"MuiCssBaseline"}),{children:o,enableColorScheme:i=!1}=r;return g.jsxs(S.Fragment,{children:[xd&&g.jsx(kw,{enableColorScheme:i}),!xd&&!i&&g.jsx("span",{className:qs,style:{display:"none"}}),o]})}function Zv(n=window){const r=n.document.documentElement.clientWidth;return n.innerWidth-r}function $w(n){const r=Hn(n);return r.body===n?Ca(n).innerWidth>r.documentElement.clientWidth:n.scrollHeight>n.clientHeight}function Xl(n,r){r?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden")}function f0(n){return parseInt(Ca(n).getComputedStyle(n).paddingRight,10)||0}function Lw(n){const o=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(n.tagName),i=n.tagName==="INPUT"&&n.getAttribute("type")==="hidden";return o||i}function d0(n,r,o,i,s){const c=[r,o,...i];[].forEach.call(n.children,d=>{const p=!c.includes(d),m=!Lw(d);p&&m&&Xl(d,s)})}function Qf(n,r){let o=-1;return n.some((i,s)=>r(i)?(o=s,!0):!1),o}function Uw(n,r){const o=[],i=n.container;if(!r.disableScrollLock){if($w(i)){const d=Zv(Ca(i));o.push({value:i.style.paddingRight,property:"padding-right",el:i}),i.style.paddingRight=`${f0(i)+d}px`;const p=Hn(i).querySelectorAll(".mui-fixed");[].forEach.call(p,m=>{o.push({value:m.style.paddingRight,property:"padding-right",el:m}),m.style.paddingRight=`${f0(m)+d}px`})}let c;if(i.parentNode instanceof DocumentFragment)c=Hn(i).body;else{const d=i.parentElement,p=Ca(i);c=(d==null?void 0:d.nodeName)==="HTML"&&p.getComputedStyle(d).overflowY==="scroll"?d:i}o.push({value:c.style.overflow,property:"overflow",el:c},{value:c.style.overflowX,property:"overflow-x",el:c},{value:c.style.overflowY,property:"overflow-y",el:c}),c.style.overflow="hidden"}return()=>{o.forEach(({value:c,el:d,property:p})=>{c?d.style.setProperty(p,c):d.style.removeProperty(p)})}}function Hw(n){const r=[];return[].forEach.call(n.children,o=>{o.getAttribute("aria-hidden")==="true"&&r.push(o)}),r}class Pw{constructor(){this.modals=[],this.containers=[]}add(r,o){let i=this.modals.indexOf(r);if(i!==-1)return i;i=this.modals.length,this.modals.push(r),r.modalRef&&Xl(r.modalRef,!1);const s=Hw(o);d0(o,r.mount,r.modalRef,s,!0);const c=Qf(this.containers,d=>d.container===o);return c!==-1?(this.containers[c].modals.push(r),i):(this.containers.push({modals:[r],container:o,restore:null,hiddenSiblings:s}),i)}mount(r,o){const i=Qf(this.containers,c=>c.modals.includes(r)),s=this.containers[i];s.restore||(s.restore=Uw(s,o))}remove(r,o=!0){const i=this.modals.indexOf(r);if(i===-1)return i;const s=Qf(this.containers,d=>d.modals.includes(r)),c=this.containers[s];if(c.modals.splice(c.modals.indexOf(r),1),this.modals.splice(i,1),c.modals.length===0)c.restore&&c.restore(),r.modalRef&&Xl(r.modalRef,o),d0(c.container,r.mount,r.modalRef,c.hiddenSiblings,!1),this.containers.splice(s,1);else{const d=c.modals[c.modals.length-1];d.modalRef&&Xl(d.modalRef,!1)}return i}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const qw=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function Gw(n){const r=parseInt(n.getAttribute("tabindex")||"",10);return Number.isNaN(r)?n.contentEditable==="true"||(n.nodeName==="AUDIO"||n.nodeName==="VIDEO"||n.nodeName==="DETAILS")&&n.getAttribute("tabindex")===null?0:n.tabIndex:r}function Iw(n){if(n.tagName!=="INPUT"||n.type!=="radio"||!n.name)return!1;const r=i=>n.ownerDocument.querySelector(`input[type="radio"]${i}`);let o=r(`[name="${n.name}"]:checked`);return o||(o=r(`[name="${n.name}"]`)),o!==n}function Vw(n){return!(n.disabled||n.tagName==="INPUT"&&n.type==="hidden"||Iw(n))}function Yw(n){const r=[],o=[];return Array.from(n.querySelectorAll(qw)).forEach((i,s)=>{const c=Gw(i);c===-1||!Vw(i)||(c===0?r.push(i):o.push({documentOrder:s,tabIndex:c,node:i}))}),o.sort((i,s)=>i.tabIndex===s.tabIndex?i.documentOrder-s.documentOrder:i.tabIndex-s.tabIndex).map(i=>i.node).concat(r)}function Fw(){return!0}function Xw(n){const{children:r,disableAutoFocus:o=!1,disableEnforceFocus:i=!1,disableRestoreFocus:s=!1,getTabbable:c=Yw,isEnabled:d=Fw,open:p}=n,m=S.useRef(!1),h=S.useRef(null),y=S.useRef(null),b=S.useRef(null),x=S.useRef(null),R=S.useRef(!1),C=S.useRef(null),w=tn(yi(r),C),M=S.useRef(null);S.useEffect(()=>{!p||!C.current||(R.current=!o)},[o,p]),S.useEffect(()=>{if(!p||!C.current)return;const A=Hn(C.current);return C.current.contains(A.activeElement)||(C.current.hasAttribute("tabIndex")||C.current.setAttribute("tabIndex","-1"),R.current&&C.current.focus()),()=>{s||(b.current&&b.current.focus&&(m.current=!0,b.current.focus()),b.current=null)}},[p]),S.useEffect(()=>{if(!p||!C.current)return;const A=Hn(C.current),O=Y=>{M.current=Y,!(i||!d()||Y.key!=="Tab")&&A.activeElement===C.current&&Y.shiftKey&&(m.current=!0,y.current&&y.current.focus())},_=()=>{var K,E;const Y=C.current;if(Y===null)return;if(!A.hasFocus()||!d()||m.current){m.current=!1;return}if(Y.contains(A.activeElement)||i&&A.activeElement!==h.current&&A.activeElement!==y.current)return;if(A.activeElement!==x.current)x.current=null;else if(x.current!==null)return;if(!R.current)return;let F=[];if((A.activeElement===h.current||A.activeElement===y.current)&&(F=c(C.current)),F.length>0){const U=!!((K=M.current)!=null&&K.shiftKey&&((E=M.current)==null?void 0:E.key)==="Tab"),X=F[0],at=F[F.length-1];typeof X!="string"&&typeof at!="string"&&(U?at.focus():X.focus())}else Y.focus()};A.addEventListener("focusin",_),A.addEventListener("keydown",O,!0);const L=setInterval(()=>{A.activeElement&&A.activeElement.tagName==="BODY"&&_()},50);return()=>{clearInterval(L),A.removeEventListener("focusin",_),A.removeEventListener("keydown",O,!0)}},[o,i,s,d,p,c]);const j=A=>{b.current===null&&(b.current=A.relatedTarget),R.current=!0,x.current=A.target;const O=r.props.onFocus;O&&O(A)},D=A=>{b.current===null&&(b.current=A.relatedTarget),R.current=!0};return g.jsxs(S.Fragment,{children:[g.jsx("div",{tabIndex:p?0:-1,onFocus:D,ref:h,"data-testid":"sentinelStart"}),S.cloneElement(r,{ref:w,onFocus:j}),g.jsx("div",{tabIndex:p?0:-1,onFocus:D,ref:y,"data-testid":"sentinelEnd"})]})}function Kw(n){return typeof n=="function"?n():n}function Qw(n){return n?n.props.hasOwnProperty("in"):!1}const p0=()=>{},Ds=new Pw;function Ww(n){const{container:r,disableEscapeKeyDown:o=!1,disableScrollLock:i=!1,closeAfterTransition:s=!1,onTransitionEnter:c,onTransitionExited:d,children:p,onClose:m,open:h,rootRef:y}=n,b=S.useRef({}),x=S.useRef(null),R=S.useRef(null),C=tn(R,y),[w,M]=S.useState(!h),j=Qw(p);let D=!0;(n["aria-hidden"]==="false"||n["aria-hidden"]===!1)&&(D=!1);const A=()=>Hn(x.current),O=()=>(b.current.modalRef=R.current,b.current.mount=x.current,b.current),_=()=>{Ds.mount(O(),{disableScrollLock:i}),R.current&&(R.current.scrollTop=0)},L=Rr(()=>{const Q=Kw(r)||A().body;Ds.add(O(),Q),R.current&&_()}),Y=()=>Ds.isTopModal(O()),F=Rr(Q=>{x.current=Q,Q&&(h&&Y()?_():R.current&&Xl(R.current,D))}),K=S.useCallback(()=>{Ds.remove(O(),D)},[D]);S.useEffect(()=>()=>{K()},[K]),S.useEffect(()=>{h?L():(!j||!s)&&K()},[h,K,j,s,L]);const E=Q=>k=>{var I;(I=Q.onKeyDown)==null||I.call(Q,k),!(k.key!=="Escape"||k.which===229||!Y())&&(o||(k.stopPropagation(),m&&m(k,"escapeKeyDown")))},U=Q=>k=>{var I;(I=Q.onClick)==null||I.call(Q,k),k.target===k.currentTarget&&m&&m(k,"backdropClick")};return{getRootProps:(Q={})=>{const k=Yv(n);delete k.onTransitionEnter,delete k.onTransitionExited;const I={...k,...Q};return{role:"presentation",...I,onKeyDown:E(I),ref:C}},getBackdropProps:(Q={})=>{const k=Q;return{"aria-hidden":!0,...k,onClick:U(k),open:h}},getTransitionProps:()=>{const Q=()=>{M(!1),c&&c()},k=()=>{M(!0),d&&d(),s&&K()};return{onEnter:Qy(Q,(p==null?void 0:p.props.onEnter)??p0),onExited:Qy(k,(p==null?void 0:p.props.onExited)??p0)}},rootRef:C,portalRef:F,isTopModal:Y,exited:w,hasTransition:j}}function Zw(n){return Mt("MuiModal",n)}Rt("MuiModal",["root","hidden","backdrop"]);const Jw=n=>{const{open:r,exited:o,classes:i}=n;return At({root:["root",!r&&o&&"hidden"],backdrop:["backdrop"]},Zw,i)},tM=st("div",{name:"MuiModal",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.open&&o.exited&&r.hidden]}})($t(({theme:n})=>({position:"fixed",zIndex:(n.vars||n).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),eM=st(Kv,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),Jv=S.forwardRef(function(r,o){const i=Ot({name:"MuiModal",props:r}),{BackdropComponent:s=eM,BackdropProps:c,classes:d,className:p,closeAfterTransition:m=!1,children:h,container:y,component:b,components:x={},componentsProps:R={},disableAutoFocus:C=!1,disableEnforceFocus:w=!1,disableEscapeKeyDown:M=!1,disablePortal:j=!1,disableRestoreFocus:D=!1,disableScrollLock:A=!1,hideBackdrop:O=!1,keepMounted:_=!1,onClose:L,onTransitionEnter:Y,onTransitionExited:F,open:K,slotProps:E={},slots:U={},theme:X,...at}=i,it={...i,closeAfterTransition:m,disableAutoFocus:C,disableEnforceFocus:w,disableEscapeKeyDown:M,disablePortal:j,disableRestoreFocus:D,disableScrollLock:A,hideBackdrop:O,keepMounted:_},{getRootProps:Q,getBackdropProps:k,getTransitionProps:I,portalRef:ot,isTopModal:Z,exited:N,hasTransition:G}=Ww({...it,rootRef:o}),et={...it,exited:N},tt=Jw(et),ut={};if(h.props.tabIndex===void 0&&(ut.tabIndex="-1"),G){const{onEnter:gt,onExited:_t}=I();ut.onEnter=gt,ut.onExited=_t}const lt={slots:{root:x.Root,backdrop:x.Backdrop,...U},slotProps:{...R,...E}},[ct,bt]=kt("root",{ref:o,elementType:tM,externalForwardedProps:{...lt,...at,component:b},getSlotProps:Q,ownerState:et,className:mt(p,tt==null?void 0:tt.root,!et.open&&et.exited&&(tt==null?void 0:tt.hidden))}),[wt,zt]=kt("backdrop",{ref:c==null?void 0:c.ref,elementType:s,externalForwardedProps:lt,shouldForwardComponentProp:!0,additionalProps:c,getSlotProps:gt=>k({...gt,onClick:_t=>{gt!=null&&gt.onClick&&gt.onClick(_t)}}),className:mt(c==null?void 0:c.className,tt==null?void 0:tt.backdrop),ownerState:et});return!_&&!K&&(!G||N)?null:g.jsx(FR,{ref:ot,container:y,disablePortal:j,children:g.jsxs(ct,{...bt,children:[!O&&s?g.jsx(wt,{...zt}):null,g.jsx(Xw,{disableEnforceFocus:w,disableAutoFocus:C,disableRestoreFocus:D,isEnabled:Z,open:K,children:S.cloneElement(h,ut)})]})})});function nM(n){return Mt("MuiDialog",n)}const Wf=Rt("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),tb=S.createContext({}),aM=st(Kv,{name:"MuiDialog",slot:"Backdrop",overrides:(n,r)=>r.backdrop})({zIndex:-1}),rM=n=>{const{classes:r,scroll:o,maxWidth:i,fullWidth:s,fullScreen:c}=n,d={root:["root"],container:["container",`scroll${ft(o)}`],paper:["paper",`paperScroll${ft(o)}`,`paperWidth${ft(String(i))}`,s&&"paperFullWidth",c&&"paperFullScreen"]};return At(d,nM,r)},oM=st(Jv,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),lM=st("div",{name:"MuiDialog",slot:"Container",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.container,r[`scroll${ft(o.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),iM=st(_r,{name:"MuiDialog",slot:"Paper",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.paper,r[`scrollPaper${ft(o.scroll)}`],r[`paperWidth${ft(String(o.maxWidth))}`],o.fullWidth&&r.paperFullWidth,o.fullScreen&&r.paperFullScreen]}})($t(({theme:n})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:r})=>!r.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:n.breakpoints.unit==="px"?Math.max(n.breakpoints.values.xs,444):`max(${n.breakpoints.values.xs}${n.breakpoints.unit}, 444px)`,[`&.${Wf.paperScrollBody}`]:{[n.breakpoints.down(Math.max(n.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(n.breakpoints.values).filter(r=>r!=="xs").map(r=>({props:{maxWidth:r},style:{maxWidth:`${n.breakpoints.values[r]}${n.breakpoints.unit}`,[`&.${Wf.paperScrollBody}`]:{[n.breakpoints.down(n.breakpoints.values[r]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:r})=>r.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:r})=>r.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${Wf.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),sM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiDialog"}),s=gi(),c={enter:s.transitions.duration.enteringScreen,exit:s.transitions.duration.leavingScreen},{"aria-describedby":d,"aria-labelledby":p,"aria-modal":m=!0,BackdropComponent:h,BackdropProps:y,children:b,className:x,disableEscapeKeyDown:R=!1,fullScreen:C=!1,fullWidth:w=!1,maxWidth:M="sm",onClick:j,onClose:D,open:A,PaperComponent:O=_r,PaperProps:_={},scroll:L="paper",slots:Y={},slotProps:F={},TransitionComponent:K=Sd,transitionDuration:E=c,TransitionProps:U,...X}=i,at={...i,disableEscapeKeyDown:R,fullScreen:C,fullWidth:w,maxWidth:M,scroll:L},it=rM(at),Q=S.useRef(),k=qt=>{Q.current=qt.target===qt.currentTarget},I=qt=>{j&&j(qt),Q.current&&(Q.current=null,D&&D(qt,"backdropClick"))},ot=Ja(p),Z=S.useMemo(()=>({titleId:ot}),[ot]),N={transition:K,...Y},G={transition:U,paper:_,backdrop:y,...F},et={slots:N,slotProps:G},[tt,ut]=kt("root",{elementType:oM,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:at,className:mt(it.root,x),ref:o}),[lt,ct]=kt("backdrop",{elementType:aM,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:at}),[bt,wt]=kt("paper",{elementType:iM,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:at,className:mt(it.paper,_.className)}),[zt,gt]=kt("container",{elementType:lM,externalForwardedProps:et,ownerState:at,className:it.container}),[_t,xt]=kt("transition",{elementType:Sd,externalForwardedProps:et,ownerState:at,additionalProps:{appear:!0,in:A,timeout:E,role:"presentation"}});return g.jsx(tt,{closeAfterTransition:!0,slots:{backdrop:lt},slotProps:{backdrop:{transitionDuration:E,as:h,...ct}},disableEscapeKeyDown:R,onClose:D,open:A,onClick:I,...ut,...X,children:g.jsx(_t,{...xt,children:g.jsx(zt,{onMouseDown:k,...gt,children:g.jsx(bt,{as:O,elevation:24,role:"dialog","aria-describedby":d,"aria-labelledby":ot,"aria-modal":m,...wt,children:g.jsx(tb.Provider,{value:Z,children:b})})})})})});function uM(n){return Mt("MuiDialogActions",n)}Rt("MuiDialogActions",["root","spacing"]);const cM=n=>{const{classes:r,disableSpacing:o}=n;return At({root:["root",!o&&"spacing"]},uM,r)},fM=st("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disableSpacing&&r.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:n})=>!n.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),dM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiDialogActions"}),{className:s,disableSpacing:c=!1,...d}=i,p={...i,disableSpacing:c},m=cM(p);return g.jsx(fM,{className:mt(m.root,s),ownerState:p,ref:o,...d})});function pM(n){return Mt("MuiDialogContent",n)}Rt("MuiDialogContent",["root","dividers"]);function mM(n){return Mt("MuiDialogTitle",n)}const hM=Rt("MuiDialogTitle",["root"]),gM=n=>{const{classes:r,dividers:o}=n;return At({root:["root",o&&"dividers"]},pM,r)},yM=st("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.dividers&&r.dividers]}})($t(({theme:n})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:r})=>r.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(n.vars||n).palette.divider}`,borderBottom:`1px solid ${(n.vars||n).palette.divider}`}},{props:({ownerState:r})=>!r.dividers,style:{[`.${hM.root} + &`]:{paddingTop:0}}}]}))),vM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiDialogContent"}),{className:s,dividers:c=!1,...d}=i,p={...i,dividers:c},m=gM(p);return g.jsx(yM,{className:mt(m.root,s),ownerState:p,ref:o,...d})}),bM=n=>{const{classes:r}=n;return At({root:["root"]},mM,r)},SM=st(mu,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),xM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiDialogTitle"}),{className:s,id:c,...d}=i,p=i,m=bM(p),{titleId:h=c}=S.useContext(tb);return g.jsx(SM,{component:"h2",className:mt(m.root,s),ownerState:p,ref:o,variant:"h6",id:c??h,...d})}),m0=Rt("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]),CM=n=>{const{classes:r,disableUnderline:o,startAdornment:i,endAdornment:s,size:c,hiddenLabel:d,multiline:p}=n,m={root:["root",!o&&"underline",i&&"adornedStart",s&&"adornedEnd",c==="small"&&`size${ft(c)}`,d&&"hiddenLabel",p&&"multiline"],input:["input"]},h=At(m,nw,r);return{...r,...h}},EM=st(yu,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[...hu(n,r),!o.disableUnderline&&r.underline]}})($t(({theme:n})=>{const r=n.palette.mode==="light",o=r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",i=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",s=r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",c=r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i,borderTopLeftRadius:(n.vars||n).shape.borderRadius,borderTopRightRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),"&:hover":{backgroundColor:n.vars?n.vars.palette.FilledInput.hoverBg:s,"@media (hover: none)":{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i}},[`&.${vr.focused}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:i},[`&.${vr.disabled}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.disabledBg:c},variants:[{props:({ownerState:d})=>!d.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${vr.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${vr.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`:o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${vr.disabled}, .${vr.error}):before`]:{borderBottom:`1px solid ${(n.vars||n).palette.text.primary}`},[`&.${vr.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(bn()).map(([d])=>{var p;return{props:{disableUnderline:!1,color:d},style:{"&::after":{borderBottom:`2px solid ${(p=(n.vars||n).palette[d])==null?void 0:p.main}`}}}}),{props:({ownerState:d})=>d.startAdornment,style:{paddingLeft:12}},{props:({ownerState:d})=>d.endAdornment,style:{paddingRight:12}},{props:({ownerState:d})=>d.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:d,size:p})=>d.multiline&&p==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel&&d.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),TM=st(vu,{name:"MuiFilledInput",slot:"Input",overridesResolver:gu})($t(({theme:n})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:r})=>r.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}},{props:({ownerState:r})=>r.hiddenLabel&&r.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:r})=>r.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),ep=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiFilledInput"}),{disableUnderline:s=!1,components:c={},componentsProps:d,fullWidth:p=!1,hiddenLabel:m,inputComponent:h="input",multiline:y=!1,slotProps:b,slots:x={},type:R="text",...C}=i,w={...i,disableUnderline:s,fullWidth:p,inputComponent:h,multiline:y,type:R},M=CM(i),j={root:{ownerState:w},input:{ownerState:w}},D=b??d?We(j,b??d):j,A=x.root??c.Root??EM,O=x.input??c.Input??TM;return g.jsx(bu,{slots:{root:A,input:O},slotProps:D,fullWidth:p,inputComponent:h,multiline:y,ref:o,type:R,...C,classes:M})});ep.muiName="Input";function RM(n){return Mt("MuiFormControl",n)}Rt("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const wM=n=>{const{classes:r,margin:o,fullWidth:i}=n,s={root:["root",o!=="none"&&`margin${ft(o)}`,i&&"fullWidth"]};return At(s,RM,r)},MM=st("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`margin${ft(o.margin)}`],o.fullWidth&&r.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),eb=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiFormControl"}),{children:s,className:c,color:d="primary",component:p="div",disabled:m=!1,error:h=!1,focused:y,fullWidth:b=!1,hiddenLabel:x=!1,margin:R="none",required:C=!1,size:w="medium",variant:M="outlined",...j}=i,D={...i,color:d,component:p,disabled:m,error:h,fullWidth:b,hiddenLabel:x,margin:R,required:C,size:w,variant:M},A=wM(D),[O,_]=S.useState(()=>{let Q=!1;return s&&S.Children.forEach(s,k=>{if(!Hs(k,["Input","Select"]))return;const I=Hs(k,["Select"])?k.props.input:k;I&&WR(I.props)&&(Q=!0)}),Q}),[L,Y]=S.useState(()=>{let Q=!1;return s&&S.Children.forEach(s,k=>{Hs(k,["Input","Select"])&&(Zs(k.props,!0)||Zs(k.props.inputProps,!0))&&(Q=!0)}),Q}),[F,K]=S.useState(!1);m&&F&&K(!1);const E=y!==void 0&&!m?y:F;let U;S.useRef(!1);const X=S.useCallback(()=>{Y(!0)},[]),at=S.useCallback(()=>{Y(!1)},[]),it=S.useMemo(()=>({adornedStart:O,setAdornedStart:_,color:d,disabled:m,error:h,filled:L,focused:E,fullWidth:b,hiddenLabel:x,size:w,onBlur:()=>{K(!1)},onFocus:()=>{K(!0)},onEmpty:at,onFilled:X,registerEffect:U,required:C,variant:M}),[O,d,m,h,L,E,b,x,U,at,X,C,w,M]);return g.jsx(tp.Provider,{value:it,children:g.jsx(MM,{as:p,ownerState:D,className:mt(A.root,c),ref:o,...j,children:s})})});function AM(n){return Mt("MuiFormHelperText",n)}const h0=Rt("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var g0;const OM=n=>{const{classes:r,contained:o,size:i,disabled:s,error:c,filled:d,focused:p,required:m}=n,h={root:["root",s&&"disabled",c&&"error",i&&`size${ft(i)}`,o&&"contained",p&&"focused",d&&"filled",m&&"required"]};return At(h,AM,r)},_M=st("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.size&&r[`size${ft(o.size)}`],o.contained&&r.contained,o.filled&&r.filled]}})($t(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${h0.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${h0.error}`]:{color:(n.vars||n).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:r})=>r.contained,style:{marginLeft:14,marginRight:14}}]}))),jM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiFormHelperText"}),{children:s,className:c,component:d="p",disabled:p,error:m,filled:h,focused:y,margin:b,required:x,variant:R,...C}=i,w=jr(),M=$o({props:i,muiFormControl:w,states:["variant","size","disabled","error","filled","focused","required"]}),j={...i,component:d,contained:M.variant==="filled"||M.variant==="outlined",variant:M.variant,size:M.size,disabled:M.disabled,error:M.error,filled:M.filled,focused:M.focused,required:M.required};delete j.ownerState;const D=OM(j);return g.jsx(_M,{as:d,className:mt(D.root,c),ref:o,...C,ownerState:j,children:s===" "?g0||(g0=g.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):s})});function NM(n){return Mt("MuiFormLabel",n)}const Kl=Rt("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),zM=n=>{const{classes:r,color:o,focused:i,disabled:s,error:c,filled:d,required:p}=n,m={root:["root",`color${ft(o)}`,s&&"disabled",c&&"error",d&&"filled",i&&"focused",p&&"required"],asterisk:["asterisk",c&&"error"]};return At(m,NM,r)},DM=st("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.color==="secondary"&&r.colorSecondary,o.filled&&r.filled]}})($t(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(n.palette).filter(bn()).map(([r])=>({props:{color:r},style:{[`&.${Kl.focused}`]:{color:(n.vars||n).palette[r].main}}})),{props:{},style:{[`&.${Kl.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${Kl.error}`]:{color:(n.vars||n).palette.error.main}}}]}))),kM=st("span",{name:"MuiFormLabel",slot:"Asterisk"})($t(({theme:n})=>({[`&.${Kl.error}`]:{color:(n.vars||n).palette.error.main}}))),BM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiFormLabel"}),{children:s,className:c,color:d,component:p="label",disabled:m,error:h,filled:y,focused:b,required:x,...R}=i,C=jr(),w=$o({props:i,muiFormControl:C,states:["color","required","focused","disabled","error","filled"]}),M={...i,color:w.color||"primary",component:p,disabled:w.disabled,error:w.error,filled:w.filled,focused:w.focused,required:w.required},j=zM(M);return g.jsxs(DM,{as:p,ownerState:M,className:mt(j.root,c),ref:o,...R,children:[s,w.required&&g.jsxs(kM,{ownerState:M,"aria-hidden":!0,className:j.asterisk,children:[" ","*"]})]})}),y0=eT({createStyledComponent:st("div",{name:"MuiGrid",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.container&&r.container]}}),componentName:"MuiGrid",useThemeProps:n=>Ot({props:n,name:"MuiGrid"}),useTheme:gi});function Cd(n){return`scale(${n}, ${n**2})`}const $M={entering:{opacity:1,transform:Cd(1)},entered:{opacity:1,transform:"none"}},Zf=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),Ed=S.forwardRef(function(r,o){const{addEndListener:i,appear:s=!0,children:c,easing:d,in:p,onEnter:m,onEntered:h,onEntering:y,onExit:b,onExited:x,onExiting:R,style:C,timeout:w="auto",TransitionComponent:M=ea,...j}=r,D=qv(),A=S.useRef(),O=gi(),_=S.useRef(null),L=tn(_,yi(c),o),Y=Q=>k=>{if(Q){const I=_.current;k===void 0?Q(I):Q(I,k)}},F=Y(y),K=Y((Q,k)=>{Gv(Q);const{duration:I,delay:ot,easing:Z}=Qs({style:C,timeout:w,easing:d},{mode:"enter"});let N;w==="auto"?(N=O.transitions.getAutoHeightDuration(Q.clientHeight),A.current=N):N=I,Q.style.transition=[O.transitions.create("opacity",{duration:N,delay:ot}),O.transitions.create("transform",{duration:Zf?N:N*.666,delay:ot,easing:Z})].join(","),m&&m(Q,k)}),E=Y(h),U=Y(R),X=Y(Q=>{const{duration:k,delay:I,easing:ot}=Qs({style:C,timeout:w,easing:d},{mode:"exit"});let Z;w==="auto"?(Z=O.transitions.getAutoHeightDuration(Q.clientHeight),A.current=Z):Z=k,Q.style.transition=[O.transitions.create("opacity",{duration:Z,delay:I}),O.transitions.create("transform",{duration:Zf?Z:Z*.666,delay:Zf?I:I||Z*.333,easing:ot})].join(","),Q.style.opacity=0,Q.style.transform=Cd(.75),b&&b(Q)}),at=Y(x),it=Q=>{w==="auto"&&D.start(A.current||0,Q),i&&i(_.current,Q)};return g.jsx(M,{appear:s,in:p,nodeRef:_,onEnter:K,onEntered:E,onEntering:F,onExit:X,onExited:at,onExiting:U,addEndListener:it,timeout:w==="auto"?null:w,...j,children:(Q,{ownerState:k,...I})=>S.cloneElement(c,{style:{opacity:0,transform:Cd(.75),visibility:Q==="exited"&&!p?"hidden":void 0,...$M[Q],...C,...c.props.style},ref:L,...I})})});Ed&&(Ed.muiSupportAuto=!0);const LM=n=>{const{classes:r,disableUnderline:o}=n,s=At({root:["root",!o&&"underline"],input:["input"]},tw,r);return{...r,...s}},UM=st(yu,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[...hu(n,r),!o.disableUnderline&&r.underline]}})($t(({theme:n})=>{let o=n.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return n.vars&&(o=`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:i})=>i.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:i})=>!i.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Ul.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Ul.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Ul.disabled}, .${Ul.error}):before`]:{borderBottom:`2px solid ${(n.vars||n).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${o}`}},[`&.${Ul.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(bn()).map(([i])=>({props:{color:i,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[i].main}`}}}))]}})),HM=st(vu,{name:"MuiInput",slot:"Input",overridesResolver:gu})({}),np=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiInput"}),{disableUnderline:s=!1,components:c={},componentsProps:d,fullWidth:p=!1,inputComponent:m="input",multiline:h=!1,slotProps:y,slots:b={},type:x="text",...R}=i,C=LM(i),M={root:{ownerState:{disableUnderline:s}}},j=y??d?We(y??d,M):M,D=b.root??c.Root??UM,A=b.input??c.Input??HM;return g.jsx(bu,{slots:{root:D,input:A},slotProps:j,fullWidth:p,inputComponent:m,multiline:h,ref:o,type:x,...R,classes:C})});np.muiName="Input";function PM(n){return Mt("MuiInputLabel",n)}Rt("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const qM=n=>{const{classes:r,formControl:o,size:i,shrink:s,disableAnimation:c,variant:d,required:p}=n,m={root:["root",o&&"formControl",!c&&"animated",s&&"shrink",i&&i!=="medium"&&`size${ft(i)}`,d],asterisk:[p&&"asterisk"]},h=At(m,PM,r);return{...r,...h}},GM=st(BM,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`& .${Kl.asterisk}`]:r.asterisk},r.root,o.formControl&&r.formControl,o.size==="small"&&r.sizeSmall,o.shrink&&r.shrink,!o.disableAnimation&&r.animated,o.focused&&r.focused,r[o.variant]]}})($t(({theme:n})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:r})=>r.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:r})=>r.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:r})=>!r.disableAnimation,style:{transition:n.transitions.create(["color","transform","max-width"],{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:r,ownerState:o})=>r==="filled"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:r,ownerState:o,size:i})=>r==="filled"&&o.shrink&&i==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:r,ownerState:o})=>r==="outlined"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),nb=S.forwardRef(function(r,o){const i=Ot({name:"MuiInputLabel",props:r}),{disableAnimation:s=!1,margin:c,shrink:d,variant:p,className:m,...h}=i,y=jr();let b=d;typeof b>"u"&&y&&(b=y.filled||y.focused||y.adornedStart);const x=$o({props:i,muiFormControl:y,states:["size","variant","required","focused"]}),R={...i,disableAnimation:s,formControl:y,shrink:b,size:x.size,variant:x.variant,required:x.required,focused:x.focused},C=qM(R);return g.jsx(GM,{"data-shrink":b,ref:o,className:mt(C.root,m),...h,ownerState:R,classes:C})}),Td=S.createContext({});function IM(n){return Mt("MuiList",n)}Rt("MuiList",["root","padding","dense","subheader"]);const VM=n=>{const{classes:r,disablePadding:o,dense:i,subheader:s}=n;return At({root:["root",!o&&"padding",i&&"dense",s&&"subheader"]},IM,r)},YM=st("ul",{name:"MuiList",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disablePadding&&r.padding,o.dense&&r.dense,o.subheader&&r.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:n})=>!n.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:n})=>n.subheader,style:{paddingTop:0}}]}),FM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiList"}),{children:s,className:c,component:d="ul",dense:p=!1,disablePadding:m=!1,subheader:h,...y}=i,b=S.useMemo(()=>({dense:p}),[p]),x={...i,component:d,dense:p,disablePadding:m},R=VM(x);return g.jsx(Td.Provider,{value:b,children:g.jsxs(YM,{as:d,className:mt(R.root,c),ref:o,ownerState:x,...y,children:[h,s]})})}),v0=Rt("MuiListItemIcon",["root","alignItemsFlexStart"]),b0=Rt("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);function Jf(n,r,o){return n===r?n.firstChild:r&&r.nextElementSibling?r.nextElementSibling:o?null:n.firstChild}function S0(n,r,o){return n===r?o?n.firstChild:n.lastChild:r&&r.previousElementSibling?r.previousElementSibling:o?null:n.lastChild}function ab(n,r){if(r===void 0)return!0;let o=n.innerText;return o===void 0&&(o=n.textContent),o=o.trim().toLowerCase(),o.length===0?!1:r.repeating?o[0]===r.keys[0]:o.startsWith(r.keys.join(""))}function Hl(n,r,o,i,s,c){let d=!1,p=s(n,r,r?o:!1);for(;p;){if(p===n.firstChild){if(d)return!1;d=!0}const m=i?!1:p.disabled||p.getAttribute("aria-disabled")==="true";if(!p.hasAttribute("tabindex")||!ab(p,c)||m)p=s(n,p,o);else return p.focus(),!0}return!1}const XM=S.forwardRef(function(r,o){const{actions:i,autoFocus:s=!1,autoFocusItem:c=!1,children:d,className:p,disabledItemsFocusable:m=!1,disableListWrap:h=!1,onKeyDown:y,variant:b="selectedMenu",...x}=r,R=S.useRef(null),C=S.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});ta(()=>{s&&R.current.focus()},[s]),S.useImperativeHandle(i,()=>({adjustStyleForScrollbar:(A,{direction:O})=>{const _=!R.current.style.width;if(A.clientHeight<R.current.clientHeight&&_){const L=`${Zv(Ca(A))}px`;R.current.style[O==="rtl"?"paddingLeft":"paddingRight"]=L,R.current.style.width=`calc(100% + ${L})`}return R.current}}),[]);const w=A=>{const O=R.current,_=A.key;if(A.ctrlKey||A.metaKey||A.altKey){y&&y(A);return}const Y=Hn(O).activeElement;if(_==="ArrowDown")A.preventDefault(),Hl(O,Y,h,m,Jf);else if(_==="ArrowUp")A.preventDefault(),Hl(O,Y,h,m,S0);else if(_==="Home")A.preventDefault(),Hl(O,null,h,m,Jf);else if(_==="End")A.preventDefault(),Hl(O,null,h,m,S0);else if(_.length===1){const F=C.current,K=_.toLowerCase(),E=performance.now();F.keys.length>0&&(E-F.lastTime>500?(F.keys=[],F.repeating=!0,F.previousKeyMatched=!0):F.repeating&&K!==F.keys[0]&&(F.repeating=!1)),F.lastTime=E,F.keys.push(K);const U=Y&&!F.repeating&&ab(Y,F);F.previousKeyMatched&&(U||Hl(O,Y,!1,m,Jf,F))?A.preventDefault():F.previousKeyMatched=!1}y&&y(A)},M=tn(R,o);let j=-1;S.Children.forEach(d,(A,O)=>{if(!S.isValidElement(A)){j===O&&(j+=1,j>=d.length&&(j=-1));return}A.props.disabled||(b==="selectedMenu"&&A.props.selected||j===-1)&&(j=O),j===O&&(A.props.disabled||A.props.muiSkipListHighlight||A.type.muiSkipListHighlight)&&(j+=1,j>=d.length&&(j=-1))});const D=S.Children.map(d,(A,O)=>{if(O===j){const _={};return c&&(_.autoFocus=!0),A.props.tabIndex===void 0&&b==="selectedMenu"&&(_.tabIndex=0),S.cloneElement(A,_)}return A});return g.jsx(FM,{role:"menu",ref:M,className:p,onKeyDown:w,tabIndex:s?0:-1,...x,children:D})});function KM(n){return Mt("MuiPopover",n)}Rt("MuiPopover",["root","paper"]);function x0(n,r){let o=0;return typeof r=="number"?o=r:r==="center"?o=n.height/2:r==="bottom"&&(o=n.height),o}function C0(n,r){let o=0;return typeof r=="number"?o=r:r==="center"?o=n.width/2:r==="right"&&(o=n.width),o}function E0(n){return[n.horizontal,n.vertical].map(r=>typeof r=="number"?`${r}px`:r).join(" ")}function ks(n){return typeof n=="function"?n():n}const QM=n=>{const{classes:r}=n;return At({root:["root"],paper:["paper"]},KM,r)},WM=st(Jv,{name:"MuiPopover",slot:"Root"})({}),rb=st(_r,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),ZM=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiPopover"}),{action:s,anchorEl:c,anchorOrigin:d={vertical:"top",horizontal:"left"},anchorPosition:p,anchorReference:m="anchorEl",children:h,className:y,container:b,elevation:x=8,marginThreshold:R=16,open:C,PaperProps:w={},slots:M={},slotProps:j={},transformOrigin:D={vertical:"top",horizontal:"left"},TransitionComponent:A,transitionDuration:O="auto",TransitionProps:_={},disableScrollLock:L=!1,...Y}=i,F=S.useRef(),K={...i,anchorOrigin:d,anchorReference:m,elevation:x,marginThreshold:R,transformOrigin:D,TransitionComponent:A,transitionDuration:O,TransitionProps:_},E=QM(K),U=S.useCallback(()=>{if(m==="anchorPosition")return p;const gt=ks(c),xt=(gt&&gt.nodeType===1?gt:Hn(F.current).body).getBoundingClientRect();return{top:xt.top+x0(xt,d.vertical),left:xt.left+C0(xt,d.horizontal)}},[c,d.horizontal,d.vertical,p,m]),X=S.useCallback(gt=>({vertical:x0(gt,D.vertical),horizontal:C0(gt,D.horizontal)}),[D.horizontal,D.vertical]),at=S.useCallback(gt=>{const _t={width:gt.offsetWidth,height:gt.offsetHeight},xt=X(_t);if(m==="none")return{top:null,left:null,transformOrigin:E0(xt)};const qt=U();let jt=qt.top-xt.vertical,Xt=qt.left-xt.horizontal;const He=jt+_t.height,Kt=Xt+_t.width,se=Ca(ks(c)),ie=se.innerHeight-R,de=se.innerWidth-R;if(R!==null&&jt<R){const Yt=jt-R;jt-=Yt,xt.vertical+=Yt}else if(R!==null&&He>ie){const Yt=He-ie;jt-=Yt,xt.vertical+=Yt}if(R!==null&&Xt<R){const Yt=Xt-R;Xt-=Yt,xt.horizontal+=Yt}else if(Kt>de){const Yt=Kt-de;Xt-=Yt,xt.horizontal+=Yt}return{top:`${Math.round(jt)}px`,left:`${Math.round(Xt)}px`,transformOrigin:E0(xt)}},[c,m,U,X,R]),[it,Q]=S.useState(C),k=S.useCallback(()=>{const gt=F.current;if(!gt)return;const _t=at(gt);_t.top!==null&&gt.style.setProperty("top",_t.top),_t.left!==null&&(gt.style.left=_t.left),gt.style.transformOrigin=_t.transformOrigin,Q(!0)},[at]);S.useEffect(()=>(L&&window.addEventListener("scroll",k),()=>window.removeEventListener("scroll",k)),[c,L,k]);const I=()=>{k()},ot=()=>{Q(!1)};S.useEffect(()=>{C&&k()}),S.useImperativeHandle(s,()=>C?{updatePosition:()=>{k()}}:null,[C,k]),S.useEffect(()=>{if(!C)return;const gt=Lv(()=>{k()}),_t=Ca(ks(c));return _t.addEventListener("resize",gt),()=>{gt.clear(),_t.removeEventListener("resize",gt)}},[c,C,k]);let Z=O;const N={slots:{transition:A,...M},slotProps:{transition:_,paper:w,...j}},[G,et]=kt("transition",{elementType:Ed,externalForwardedProps:N,ownerState:K,getSlotProps:gt=>({...gt,onEntering:(_t,xt)=>{var qt;(qt=gt.onEntering)==null||qt.call(gt,_t,xt),I()},onExited:_t=>{var xt;(xt=gt.onExited)==null||xt.call(gt,_t),ot()}}),additionalProps:{appear:!0,in:C}});O==="auto"&&!G.muiSupportAuto&&(Z=void 0);const tt=b||(c?Hn(ks(c)).body:void 0),[ut,{slots:lt,slotProps:ct,...bt}]=kt("root",{ref:o,elementType:WM,externalForwardedProps:{...N,...Y},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:M.backdrop},slotProps:{backdrop:PT(typeof j.backdrop=="function"?j.backdrop(K):j.backdrop,{invisible:!0})},container:tt,open:C},ownerState:K,className:mt(E.root,y)}),[wt,zt]=kt("paper",{ref:F,className:E.paper,elementType:rb,externalForwardedProps:N,shouldForwardComponentProp:!0,additionalProps:{elevation:x,style:it?void 0:{opacity:0}},ownerState:K});return g.jsx(ut,{...bt,...!bd(ut)&&{slots:lt,slotProps:ct,disableScrollLock:L},children:g.jsx(G,{...et,timeout:Z,children:g.jsx(wt,{...zt,children:h})})})});function JM(n){return Mt("MuiMenu",n)}Rt("MuiMenu",["root","paper","list"]);const t4={vertical:"top",horizontal:"right"},e4={vertical:"top",horizontal:"left"},n4=n=>{const{classes:r}=n;return At({root:["root"],paper:["paper"],list:["list"]},JM,r)},a4=st(ZM,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiMenu",slot:"Root"})({}),r4=st(rb,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),o4=st(XM,{name:"MuiMenu",slot:"List"})({outline:0}),l4=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiMenu"}),{autoFocus:s=!0,children:c,className:d,disableAutoFocusItem:p=!1,MenuListProps:m={},onClose:h,open:y,PaperProps:b={},PopoverClasses:x,transitionDuration:R="auto",TransitionProps:{onEntering:C,...w}={},variant:M="selectedMenu",slots:j={},slotProps:D={},...A}=i,O=Mv(),_={...i,autoFocus:s,disableAutoFocusItem:p,MenuListProps:m,onEntering:C,PaperProps:b,transitionDuration:R,TransitionProps:w,variant:M},L=n4(_),Y=s&&!p&&y,F=S.useRef(null),K=(Z,N)=>{F.current&&F.current.adjustStyleForScrollbar(Z,{direction:O?"rtl":"ltr"}),C&&C(Z,N)},E=Z=>{Z.key==="Tab"&&(Z.preventDefault(),h&&h(Z,"tabKeyDown"))};let U=-1;S.Children.map(c,(Z,N)=>{S.isValidElement(Z)&&(Z.props.disabled||(M==="selectedMenu"&&Z.props.selected||U===-1)&&(U=N))});const X={slots:j,slotProps:{list:m,transition:w,paper:b,...D}},at=VR({elementType:j.root,externalSlotProps:D.root,ownerState:_,className:[L.root,d]}),[it,Q]=kt("paper",{className:L.paper,elementType:r4,externalForwardedProps:X,shouldForwardComponentProp:!0,ownerState:_}),[k,I]=kt("list",{className:mt(L.list,m.className),elementType:o4,shouldForwardComponentProp:!0,externalForwardedProps:X,getSlotProps:Z=>({...Z,onKeyDown:N=>{var G;E(N),(G=Z.onKeyDown)==null||G.call(Z,N)}}),ownerState:_}),ot=typeof X.slotProps.transition=="function"?X.slotProps.transition(_):X.slotProps.transition;return g.jsx(a4,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:O?"right":"left"},transformOrigin:O?t4:e4,slots:{root:j.root,paper:it,backdrop:j.backdrop,...j.transition&&{transition:j.transition}},slotProps:{root:at,paper:Q,backdrop:typeof D.backdrop=="function"?D.backdrop(_):D.backdrop,transition:{...ot,onEntering:(...Z)=>{var N;K(...Z),(N=ot==null?void 0:ot.onEntering)==null||N.call(ot,...Z)}}},open:y,ref:o,transitionDuration:R,ownerState:_,...A,classes:x,children:g.jsx(k,{actions:F,autoFocus:s&&(U===-1||p),autoFocusItem:Y,variant:M,...I,children:c})})});function i4(n){return Mt("MuiMenuItem",n)}const Pl=Rt("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),s4=(n,r)=>{const{ownerState:o}=n;return[r.root,o.dense&&r.dense,o.divider&&r.divider,!o.disableGutters&&r.gutters]},u4=n=>{const{disabled:r,dense:o,divider:i,disableGutters:s,selected:c,classes:d}=n,m=At({root:["root",o&&"dense",r&&"disabled",!s&&"gutters",i&&"divider",c&&"selected"]},i4,d);return{...d,...m}},c4=st(pu,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:s4})($t(({theme:n})=>({...n.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(n.vars||n).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Pl.selected}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:Oe(n.palette.primary.main,n.palette.action.selectedOpacity),[`&.${Pl.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:Oe(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}},[`&.${Pl.selected}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:Oe(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:Oe(n.palette.primary.main,n.palette.action.selectedOpacity)}},[`&.${Pl.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`&.${Pl.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity},[`& + .${m0.root}`]:{marginTop:n.spacing(1),marginBottom:n.spacing(1)},[`& + .${m0.inset}`]:{marginLeft:52},[`& .${b0.root}`]:{marginTop:0,marginBottom:0},[`& .${b0.inset}`]:{paddingLeft:36},[`& .${v0.root}`]:{minWidth:36},variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:r})=>r.divider,style:{borderBottom:`1px solid ${(n.vars||n).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:r})=>!r.dense,style:{[n.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:r})=>r.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...n.typography.body2,[`& .${v0.root} svg`]:{fontSize:"1.25rem"}}}]}))),ob=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiMenuItem"}),{autoFocus:s=!1,component:c="li",dense:d=!1,divider:p=!1,disableGutters:m=!1,focusVisibleClassName:h,role:y="menuitem",tabIndex:b,className:x,...R}=i,C=S.useContext(Td),w=S.useMemo(()=>({dense:d||C.dense||!1,disableGutters:m}),[C.dense,d,m]),M=S.useRef(null);ta(()=>{s&&M.current&&M.current.focus()},[s]);const j={...i,dense:w.dense,divider:p,disableGutters:m},D=u4(i),A=tn(M,o);let O;return i.disabled||(O=b!==void 0?b:-1),g.jsx(Td.Provider,{value:w,children:g.jsx(c4,{ref:A,role:y,tabIndex:O,component:c,focusVisibleClassName:mt(D.focusVisible,h),className:mt(D.root,x),...R,ownerState:j,classes:D})})});function f4(n){return Mt("MuiNativeSelect",n)}const ap=Rt("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),d4=n=>{const{classes:r,variant:o,disabled:i,multiple:s,open:c,error:d}=n,p={select:["select",o,i&&"disabled",s&&"multiple",d&&"error"],icon:["icon",`icon${ft(o)}`,c&&"iconOpen",i&&"disabled"]};return At(p,f4,r)},lb=st("select",{name:"MuiNativeSelect"})(({theme:n})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${ap.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},variants:[{props:({ownerState:r})=>r.variant!=="filled"&&r.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}}}]})),p4=st(lb,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:Sn,overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.select,r[o.variant],o.error&&r.error,{[`&.${ap.multiple}`]:r.multiple}]}})({}),ib=st("svg",{name:"MuiNativeSelect"})(({theme:n})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${ap.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:({ownerState:r})=>r.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),m4=st(ib,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.icon,o.variant&&r[`icon${ft(o.variant)}`],o.open&&r.iconOpen]}})({}),h4=S.forwardRef(function(r,o){const{className:i,disabled:s,error:c,IconComponent:d,inputRef:p,variant:m="standard",...h}=r,y={...r,disabled:s,variant:m,error:c},b=d4(y);return g.jsxs(S.Fragment,{children:[g.jsx(p4,{ownerState:y,className:mt(b.select,i),disabled:s,ref:p||o,...h}),r.multiple?null:g.jsx(m4,{as:d,ownerState:y,className:b.icon})]})});var T0;const g4=st("fieldset",{name:"MuiNotchedOutlined",shouldForwardProp:Sn})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),y4=st("legend",{name:"MuiNotchedOutlined",shouldForwardProp:Sn})($t(({theme:n})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:r})=>!r.withLabel,style:{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})}},{props:({ownerState:r})=>r.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:r})=>r.withLabel&&r.notched,style:{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}}]})));function v4(n){const{children:r,classes:o,className:i,label:s,notched:c,...d}=n,p=s!=null&&s!=="",m={...n,notched:c,withLabel:p};return g.jsx(g4,{"aria-hidden":!0,className:i,ownerState:m,...d,children:g.jsx(y4,{ownerState:m,children:p?g.jsx("span",{children:s}):T0||(T0=g.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const b4=n=>{const{classes:r}=n,i=At({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},ew,r);return{...r,...i}},S4=st(yu,{shouldForwardProp:n=>Sn(n)||n==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:hu})($t(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(n.vars||n).shape.borderRadius,[`&:hover .${Fn.notchedOutline}`]:{borderColor:(n.vars||n).palette.text.primary},"@media (hover: none)":{[`&:hover .${Fn.notchedOutline}`]:{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}},[`&.${Fn.focused} .${Fn.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(n.palette).filter(bn()).map(([o])=>({props:{color:o},style:{[`&.${Fn.focused} .${Fn.notchedOutline}`]:{borderColor:(n.vars||n).palette[o].main}}})),{props:{},style:{[`&.${Fn.error} .${Fn.notchedOutline}`]:{borderColor:(n.vars||n).palette.error.main},[`&.${Fn.disabled} .${Fn.notchedOutline}`]:{borderColor:(n.vars||n).palette.action.disabled}}},{props:({ownerState:o})=>o.startAdornment,style:{paddingLeft:14}},{props:({ownerState:o})=>o.endAdornment,style:{paddingRight:14}},{props:({ownerState:o})=>o.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:o,size:i})=>o.multiline&&i==="small",style:{padding:"8.5px 14px"}}]}})),x4=st(v4,{name:"MuiOutlinedInput",slot:"NotchedOutline"})($t(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}})),C4=st(vu,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:gu})($t(({theme:n})=>({padding:"16.5px 14px",...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:r})=>r.multiline,style:{padding:0}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}}]}))),rp=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiOutlinedInput"}),{components:s={},fullWidth:c=!1,inputComponent:d="input",label:p,multiline:m=!1,notched:h,slots:y={},slotProps:b={},type:x="text",...R}=i,C=b4(i),w=jr(),M=$o({props:i,muiFormControl:w,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),j={...i,color:M.color||"primary",disabled:M.disabled,error:M.error,focused:M.focused,formControl:w,fullWidth:c,hiddenLabel:M.hiddenLabel,multiline:m,size:M.size,type:x},D=y.root??s.Root??S4,A=y.input??s.Input??C4,[O,_]=kt("notchedOutline",{elementType:x4,className:C.notchedOutline,shouldForwardComponentProp:!0,ownerState:j,externalForwardedProps:{slots:y,slotProps:b},additionalProps:{label:p!=null&&p!==""&&M.required?g.jsxs(S.Fragment,{children:[p," ","*"]}):p}});return g.jsx(bu,{slots:{root:D,input:A},slotProps:b,renderSuffix:L=>g.jsx(O,{..._,notched:typeof h<"u"?h:!!(L.startAdornment||L.filled||L.focused)}),fullWidth:c,inputComponent:d,multiline:m,ref:o,type:x,...R,classes:{...C,notchedOutline:null}})});rp.muiName="Input";const E4=Ue(g.jsx("path",{d:"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"})),T4=Ue(g.jsx("path",{d:"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"}));function sb(n){return Mt("MuiSelect",n)}const ql=Rt("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var R0;const R4=st(lb,{name:"MuiSelect",slot:"Select",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`&.${ql.select}`]:r.select},{[`&.${ql.select}`]:r[o.variant]},{[`&.${ql.error}`]:r.error},{[`&.${ql.multiple}`]:r.multiple}]}})({[`&.${ql.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),w4=st(ib,{name:"MuiSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.icon,o.variant&&r[`icon${ft(o.variant)}`],o.open&&r.iconOpen]}})({}),M4=st("input",{shouldForwardProp:n=>$v(n)&&n!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function w0(n,r){return typeof r=="object"&&r!==null?n===r:String(n)===String(r)}function A4(n){return n==null||typeof n=="string"&&!n.trim()}const O4=n=>{const{classes:r,variant:o,disabled:i,multiple:s,open:c,error:d}=n,p={select:["select",o,i&&"disabled",s&&"multiple",d&&"error"],icon:["icon",`icon${ft(o)}`,c&&"iconOpen",i&&"disabled"],nativeInput:["nativeInput"]};return At(p,sb,r)},_4=S.forwardRef(function(r,o){var xn,tr;const{"aria-describedby":i,"aria-label":s,autoFocus:c,autoWidth:d,children:p,className:m,defaultOpen:h,defaultValue:y,disabled:b,displayEmpty:x,error:R=!1,IconComponent:C,inputRef:w,labelId:M,MenuProps:j={},multiple:D,name:A,onBlur:O,onChange:_,onClose:L,onFocus:Y,onOpen:F,open:K,readOnly:E,renderValue:U,required:X,SelectDisplayProps:at={},tabIndex:it,type:Q,value:k,variant:I="standard",...ot}=r,[Z,N]=pd({controlled:k,default:y,name:"Select"}),[G,et]=pd({controlled:K,default:h,name:"Select"}),tt=S.useRef(null),ut=S.useRef(null),[lt,ct]=S.useState(null),{current:bt}=S.useRef(K!=null),[wt,zt]=S.useState(),gt=tn(o,w),_t=S.useCallback(vt=>{ut.current=vt,vt&&ct(vt)},[]),xt=lt==null?void 0:lt.parentNode;S.useImperativeHandle(gt,()=>({focus:()=>{ut.current.focus()},node:tt.current,value:Z}),[Z]),S.useEffect(()=>{h&&G&&lt&&!bt&&(zt(d?null:xt.clientWidth),ut.current.focus())},[lt,d]),S.useEffect(()=>{c&&ut.current.focus()},[c]),S.useEffect(()=>{if(!M)return;const vt=Hn(ut.current).getElementById(M);if(vt){const ne=()=>{getSelection().isCollapsed&&ut.current.focus()};return vt.addEventListener("click",ne),()=>{vt.removeEventListener("click",ne)}}},[M]);const qt=(vt,ne)=>{vt?F&&F(ne):L&&L(ne),bt||(zt(d?null:xt.clientWidth),et(vt))},jt=vt=>{vt.button===0&&(vt.preventDefault(),ut.current.focus(),qt(!0,vt))},Xt=vt=>{qt(!1,vt)},He=S.Children.toArray(p),Kt=vt=>{const ne=He.find(Re=>Re.props.value===vt.target.value);ne!==void 0&&(N(ne.props.value),_&&_(vt,ne))},se=vt=>ne=>{let Re;if(ne.currentTarget.hasAttribute("tabindex")){if(D){Re=Array.isArray(Z)?Z.slice():[];const na=Z.indexOf(vt.props.value);na===-1?Re.push(vt.props.value):Re.splice(na,1)}else Re=vt.props.value;if(vt.props.onClick&&vt.props.onClick(ne),Z!==Re&&(N(Re),_)){const na=ne.nativeEvent||ne,Nr=new na.constructor(na.type,na);Object.defineProperty(Nr,"target",{writable:!0,value:{value:Re,name:A}}),_(Nr,vt)}D||qt(!1,ne)}},ie=vt=>{E||[" ","ArrowUp","ArrowDown","Enter"].includes(vt.key)&&(vt.preventDefault(),qt(!0,vt))},de=lt!==null&&G,Yt=vt=>{!de&&O&&(Object.defineProperty(vt,"target",{writable:!0,value:{value:Z,name:A}}),O(vt))};delete ot["aria-invalid"];let ht,un;const he=[];let kn=!1;(Zs({value:Z})||x)&&(U?ht=U(Z):kn=!0);const en=He.map(vt=>{if(!S.isValidElement(vt))return null;let ne;if(D){if(!Array.isArray(Z))throw new Error(Sa(2));ne=Z.some(Re=>w0(Re,vt.props.value)),ne&&kn&&he.push(vt.props.children)}else ne=w0(Z,vt.props.value),ne&&kn&&(un=vt.props.children);return S.cloneElement(vt,{"aria-selected":ne?"true":"false",onClick:se(vt),onKeyUp:Re=>{Re.key===" "&&Re.preventDefault(),vt.props.onKeyUp&&vt.props.onKeyUp(Re)},role:"option",selected:ne,value:void 0,"data-value":vt.props.value})});kn&&(D?he.length===0?ht=null:ht=he.reduce((vt,ne,Re)=>(vt.push(ne),Re<he.length-1&&vt.push(", "),vt),[]):ht=un);let ge=wt;!d&&bt&&lt&&(ge=xt.clientWidth);let De;typeof it<"u"?De=it:De=b?null:0;const pe=at.id||(A?`mui-component-select-${A}`:void 0),ue={...r,variant:I,value:Z,open:de,error:R},Et=O4(ue),Ye={...j.PaperProps,...(xn=j.slotProps)==null?void 0:xn.paper},Te={...j.MenuListProps,...(tr=j.slotProps)==null?void 0:tr.list},je=Ja();return g.jsxs(S.Fragment,{children:[g.jsx(R4,{as:"div",ref:_t,tabIndex:De,role:"combobox","aria-controls":de?je:void 0,"aria-disabled":b?"true":void 0,"aria-expanded":de?"true":"false","aria-haspopup":"listbox","aria-label":s,"aria-labelledby":[M,pe].filter(Boolean).join(" ")||void 0,"aria-describedby":i,"aria-required":X?"true":void 0,"aria-invalid":R?"true":void 0,onKeyDown:ie,onMouseDown:b||E?null:jt,onBlur:Yt,onFocus:Y,...at,ownerState:ue,className:mt(at.className,Et.select,m),id:pe,children:A4(ht)?R0||(R0=g.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):ht}),g.jsx(M4,{"aria-invalid":R,value:Array.isArray(Z)?Z.join(","):Z,name:A,ref:tt,"aria-hidden":!0,onChange:Kt,tabIndex:-1,disabled:b,className:Et.nativeInput,autoFocus:c,required:X,...ot,ownerState:ue}),g.jsx(w4,{as:C,className:Et.icon,ownerState:ue}),g.jsx(l4,{id:`menu-${A||""}`,anchorEl:xt,open:de,onClose:Xt,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...j,slotProps:{...j.slotProps,list:{"aria-labelledby":M,role:"listbox","aria-multiselectable":D?"true":void 0,disableListWrap:!0,id:je,...Te},paper:{...Ye,style:{minWidth:ge,...Ye!=null?Ye.style:null}}},children:en})]})}),j4=n=>{const{classes:r}=n,i=At({root:["root"]},sb,r);return{...r,...i}},op={name:"MuiSelect",slot:"Root",shouldForwardProp:n=>Sn(n)&&n!=="variant"},N4=st(np,op)(""),z4=st(rp,op)(""),D4=st(ep,op)(""),Su=S.forwardRef(function(r,o){const i=Ot({name:"MuiSelect",props:r}),{autoWidth:s=!1,children:c,classes:d={},className:p,defaultOpen:m=!1,displayEmpty:h=!1,IconComponent:y=aw,id:b,input:x,inputProps:R,label:C,labelId:w,MenuProps:M,multiple:j=!1,native:D=!1,onClose:A,onOpen:O,open:_,renderValue:L,SelectDisplayProps:Y,variant:F="outlined",...K}=i,E=D?h4:_4,U=jr(),X=$o({props:i,muiFormControl:U,states:["variant","error"]}),at=X.variant||F,it={...i,variant:at,classes:d},Q=j4(it),{root:k,...I}=Q,ot=x||{standard:g.jsx(N4,{ownerState:it}),outlined:g.jsx(z4,{label:C,ownerState:it}),filled:g.jsx(D4,{ownerState:it})}[at],Z=tn(o,yi(ot));return g.jsx(S.Fragment,{children:S.cloneElement(ot,{inputComponent:E,inputProps:{children:c,error:X.error,IconComponent:y,variant:at,type:void 0,multiple:j,...D?{id:b}:{autoWidth:s,defaultOpen:m,displayEmpty:h,labelId:w,MenuProps:M,onClose:A,onOpen:O,open:_,renderValue:L,SelectDisplayProps:{id:b,...Y}},...R,classes:R?We(I,R.classes):I,...x?x.props.inputProps:{}},...(j&&D||h)&&at==="outlined"?{notched:!0}:{},ref:Z,className:mt(ot.props.className,p,Q.root),...!x&&{variant:at},...K})})});Su.muiName="Select";function k4(n){return Mt("MuiSwitch",n)}const Je=Rt("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),B4=n=>{const{classes:r,edge:o,size:i,color:s,checked:c,disabled:d}=n,p={root:["root",o&&`edge${ft(o)}`,`size${ft(i)}`],switchBase:["switchBase",`color${ft(s)}`,c&&"checked",d&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},m=At(p,k4,r);return{...r,...m}},$4=st("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.edge&&r[`edge${ft(o.edge)}`],r[`size${ft(o.size)}`]]}})({display:"inline-flex",width:34+12*2,height:14+12*2,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${Je.thumb}`]:{width:16,height:16},[`& .${Je.switchBase}`]:{padding:4,[`&.${Je.checked}`]:{transform:"translateX(16px)"}}}}]}),L4=st(_w,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.switchBase,{[`& .${Je.input}`]:r.input},o.color!=="default"&&r[`color${ft(o.color)}`]]}})($t(({theme:n})=>({position:"absolute",top:0,left:0,zIndex:1,color:n.vars?n.vars.palette.Switch.defaultColor:`${n.palette.mode==="light"?n.palette.common.white:n.palette.grey[300]}`,transition:n.transitions.create(["left","transform"],{duration:n.transitions.duration.shortest}),[`&.${Je.checked}`]:{transform:"translateX(20px)"},[`&.${Je.disabled}`]:{color:n.vars?n.vars.palette.Switch.defaultDisabledColor:`${n.palette.mode==="light"?n.palette.grey[100]:n.palette.grey[600]}`},[`&.${Je.checked} + .${Je.track}`]:{opacity:.5},[`&.${Je.disabled} + .${Je.track}`]:{opacity:n.vars?n.vars.opacity.switchTrackDisabled:`${n.palette.mode==="light"?.12:.2}`},[`& .${Je.input}`]:{left:"-100%",width:"300%"}})),$t(({theme:n})=>({"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette.action.active,n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(n.palette).filter(bn(["light"])).map(([r])=>({props:{color:r},style:{[`&.${Je.checked}`]:{color:(n.vars||n).palette[r].main,"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:Oe(n.palette[r].main,n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Je.disabled}`]:{color:n.vars?n.vars.palette.Switch[`${r}DisabledColor`]:`${n.palette.mode==="light"?Mr(n.palette[r].main,.62):wr(n.palette[r].main,.55)}`}},[`&.${Je.checked} + .${Je.track}`]:{backgroundColor:(n.vars||n).palette[r].main}}}))]}))),U4=st("span",{name:"MuiSwitch",slot:"Track"})($t(({theme:n})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:n.transitions.create(["opacity","background-color"],{duration:n.transitions.duration.shortest}),backgroundColor:n.vars?n.vars.palette.common.onBackground:`${n.palette.mode==="light"?n.palette.common.black:n.palette.common.white}`,opacity:n.vars?n.vars.opacity.switchTrack:`${n.palette.mode==="light"?.38:.3}`}))),H4=st("span",{name:"MuiSwitch",slot:"Thumb"})($t(({theme:n})=>({boxShadow:(n.vars||n).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),P4=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiSwitch"}),{className:s,color:c="primary",edge:d=!1,size:p="medium",sx:m,slots:h={},slotProps:y={},...b}=i,x={...i,color:c,edge:d,size:p},R=B4(x),C={slots:h,slotProps:y},[w,M]=kt("root",{className:mt(R.root,s),elementType:$4,externalForwardedProps:C,ownerState:x,additionalProps:{sx:m}}),[j,D]=kt("thumb",{className:R.thumb,elementType:H4,externalForwardedProps:C,ownerState:x}),A=g.jsx(j,{...D}),[O,_]=kt("track",{className:R.track,elementType:U4,externalForwardedProps:C,ownerState:x});return g.jsxs(w,{...M,children:[g.jsx(L4,{type:"checkbox",icon:A,checkedIcon:A,ref:o,ownerState:x,...b,classes:{...R,root:R.switchBase},slots:{...h.switchBase&&{root:h.switchBase},...h.input&&{input:h.input}},slotProps:{...y.switchBase&&{root:typeof y.switchBase=="function"?y.switchBase(x):y.switchBase},...y.input&&{input:typeof y.input=="function"?y.input(x):y.input}}}),g.jsx(O,{..._})]})}),ub=S.createContext();function q4(n){return Mt("MuiTable",n)}Rt("MuiTable",["root","stickyHeader"]);const G4=n=>{const{classes:r,stickyHeader:o}=n;return At({root:["root",o&&"stickyHeader"]},q4,r)},I4=st("table",{name:"MuiTable",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.stickyHeader&&r.stickyHeader]}})($t(({theme:n})=>({display:"table",width:"100%",borderCollapse:"collapse",borderSpacing:0,"& caption":{...n.typography.body2,padding:n.spacing(2),color:(n.vars||n).palette.text.secondary,textAlign:"left",captionSide:"bottom"},variants:[{props:({ownerState:r})=>r.stickyHeader,style:{borderCollapse:"separate"}}]}))),M0="table",V4=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTable"}),{className:s,component:c=M0,padding:d="normal",size:p="medium",stickyHeader:m=!1,...h}=i,y={...i,component:c,padding:d,size:p,stickyHeader:m},b=G4(y),x=S.useMemo(()=>({padding:d,size:p,stickyHeader:m}),[d,p,m]);return g.jsx(ub.Provider,{value:x,children:g.jsx(I4,{as:c,role:c===M0?null:"table",ref:o,className:mt(b.root,s),ownerState:y,...h})})}),xu=S.createContext();function Y4(n){return Mt("MuiTableBody",n)}Rt("MuiTableBody",["root"]);const F4=n=>{const{classes:r}=n;return At({root:["root"]},Y4,r)},X4=st("tbody",{name:"MuiTableBody",slot:"Root"})({display:"table-row-group"}),K4={variant:"body"},A0="tbody",Q4=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTableBody"}),{className:s,component:c=A0,...d}=i,p={...i,component:c},m=F4(p);return g.jsx(xu.Provider,{value:K4,children:g.jsx(X4,{className:mt(m.root,s),as:c,ref:o,role:c===A0?null:"rowgroup",ownerState:p,...d})})});function W4(n){return Mt("MuiTableCell",n)}const Z4=Rt("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),J4=n=>{const{classes:r,variant:o,align:i,padding:s,size:c,stickyHeader:d}=n,p={root:["root",o,d&&"stickyHeader",i!=="inherit"&&`align${ft(i)}`,s!=="normal"&&`padding${ft(s)}`,`size${ft(c)}`]};return At(p,W4,r)},t5=st("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`size${ft(o.size)}`],o.padding!=="normal"&&r[`padding${ft(o.padding)}`],o.align!=="inherit"&&r[`align${ft(o.align)}`],o.stickyHeader&&r.stickyHeader]}})($t(({theme:n})=>({...n.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:n.vars?`1px solid ${n.vars.palette.TableCell.border}`:`1px solid
    ${n.palette.mode==="light"?Mr(Oe(n.palette.divider,1),.88):wr(Oe(n.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(n.vars||n).palette.text.primary,lineHeight:n.typography.pxToRem(24),fontWeight:n.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(n.vars||n).palette.text.primary}},{props:{variant:"footer"},style:{color:(n.vars||n).palette.text.secondary,lineHeight:n.typography.pxToRem(21),fontSize:n.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${Z4.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:({ownerState:r})=>r.stickyHeader,style:{position:"sticky",top:0,zIndex:2,backgroundColor:(n.vars||n).palette.background.default}}]}))),No=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTableCell"}),{align:s="inherit",className:c,component:d,padding:p,scope:m,size:h,sortDirection:y,variant:b,...x}=i,R=S.useContext(ub),C=S.useContext(xu),w=C&&C.variant==="head";let M;d?M=d:M=w?"th":"td";let j=m;M==="td"?j=void 0:!j&&w&&(j="col");const D=b||C&&C.variant,A={...i,align:s,component:M,padding:p||(R&&R.padding?R.padding:"normal"),size:h||(R&&R.size?R.size:"medium"),sortDirection:y,stickyHeader:D==="head"&&R&&R.stickyHeader,variant:D},O=J4(A);let _=null;return y&&(_=y==="asc"?"ascending":"descending"),g.jsx(t5,{as:M,ref:o,className:mt(O.root,c),"aria-sort":_,scope:j,ownerState:A,...x})});function e5(n){return Mt("MuiTableContainer",n)}Rt("MuiTableContainer",["root"]);const n5=n=>{const{classes:r}=n;return At({root:["root"]},e5,r)},a5=st("div",{name:"MuiTableContainer",slot:"Root"})({width:"100%",overflowX:"auto"}),r5=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTableContainer"}),{className:s,component:c="div",...d}=i,p={...i,component:c},m=n5(p);return g.jsx(a5,{ref:o,as:c,className:mt(m.root,s),ownerState:p,...d})});function o5(n){return Mt("MuiTableHead",n)}Rt("MuiTableHead",["root"]);const l5=n=>{const{classes:r}=n;return At({root:["root"]},o5,r)},i5=st("thead",{name:"MuiTableHead",slot:"Root"})({display:"table-header-group"}),s5={variant:"head"},O0="thead",u5=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTableHead"}),{className:s,component:c=O0,...d}=i,p={...i,component:c},m=l5(p);return g.jsx(xu.Provider,{value:s5,children:g.jsx(i5,{as:c,className:mt(m.root,s),ref:o,role:c===O0?null:"rowgroup",ownerState:p,...d})})});function c5(n){return Mt("MuiToolbar",n)}Rt("MuiToolbar",["root","gutters","regular","dense"]);const f5=n=>{const{classes:r,disableGutters:o,variant:i}=n;return At({root:["root",!o&&"gutters",i]},c5,r)},d5=st("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disableGutters&&r.gutters,r[o.variant]]}})($t(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:n.spacing(2),paddingRight:n.spacing(2),[n.breakpoints.up("sm")]:{paddingLeft:n.spacing(3),paddingRight:n.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:n.mixins.toolbar}]}))),p5=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiToolbar"}),{className:s,component:c="div",disableGutters:d=!1,variant:p="regular",...m}=i,h={...i,component:c,disableGutters:d,variant:p},y=f5(h);return g.jsx(d5,{as:c,className:mt(y.root,s),ref:o,ownerState:h,...m})}),m5=Ue(g.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"})),h5=Ue(g.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}));function g5(n){return Mt("MuiTablePaginationActions",n)}Rt("MuiTablePaginationActions",["root"]);const y5=n=>{const{classes:r}=n;return At({root:["root"]},g5,r)},v5=st("div",{name:"MuiTablePaginationActions",slot:"Root"})({}),b5=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTablePaginationActions"}),{backIconButtonProps:s,className:c,count:d,disabled:p=!1,getItemAriaLabel:m,nextIconButtonProps:h,onPageChange:y,page:b,rowsPerPage:x,showFirstButton:R,showLastButton:C,slots:w={},slotProps:M={},...j}=i,D=Mv(),O=y5(i),_=lt=>{y(lt,0)},L=lt=>{y(lt,b-1)},Y=lt=>{y(lt,b+1)},F=lt=>{y(lt,Math.max(0,Math.ceil(d/x)-1))},K=w.firstButton??Tr,E=w.lastButton??Tr,U=w.nextButton??Tr,X=w.previousButton??Tr,at=w.firstButtonIcon??E4,it=w.lastButtonIcon??T4,Q=w.nextButtonIcon??h5,k=w.previousButtonIcon??m5,I=D?E:K,ot=D?U:X,Z=D?X:U,N=D?K:E,G=D?M.lastButton:M.firstButton,et=D?M.nextButton:M.previousButton,tt=D?M.previousButton:M.nextButton,ut=D?M.firstButton:M.lastButton;return g.jsxs(v5,{ref:o,className:mt(O.root,c),...j,children:[R&&g.jsx(I,{onClick:_,disabled:p||b===0,"aria-label":m("first",b),title:m("first",b),...G,children:D?g.jsx(it,{...M.lastButtonIcon}):g.jsx(at,{...M.firstButtonIcon})}),g.jsx(ot,{onClick:L,disabled:p||b===0,color:"inherit","aria-label":m("previous",b),title:m("previous",b),...et??s,children:D?g.jsx(Q,{...M.nextButtonIcon}):g.jsx(k,{...M.previousButtonIcon})}),g.jsx(Z,{onClick:Y,disabled:p||(d!==-1?b>=Math.ceil(d/x)-1:!1),color:"inherit","aria-label":m("next",b),title:m("next",b),...tt??h,children:D?g.jsx(k,{...M.previousButtonIcon}):g.jsx(Q,{...M.nextButtonIcon})}),C&&g.jsx(N,{onClick:F,disabled:p||b>=Math.ceil(d/x)-1,"aria-label":m("last",b),title:m("last",b),...ut,children:D?g.jsx(at,{...M.firstButtonIcon}):g.jsx(it,{...M.lastButtonIcon})})]})});function S5(n){return Mt("MuiTablePagination",n)}const Ql=Rt("MuiTablePagination",["root","toolbar","spacer","selectLabel","selectRoot","select","selectIcon","input","menuItem","displayedRows","actions"]);var _0;const x5=st(No,{name:"MuiTablePagination",slot:"Root"})($t(({theme:n})=>({overflow:"auto",color:(n.vars||n).palette.text.primary,fontSize:n.typography.pxToRem(14),"&:last-child":{padding:0}}))),C5=st(p5,{name:"MuiTablePagination",slot:"Toolbar",overridesResolver:(n,r)=>({[`& .${Ql.actions}`]:r.actions,...r.toolbar})})($t(({theme:n})=>({minHeight:52,paddingRight:2,[`${n.breakpoints.up("xs")} and (orientation: landscape)`]:{minHeight:52},[n.breakpoints.up("sm")]:{minHeight:52,paddingRight:2},[`& .${Ql.actions}`]:{flexShrink:0,marginLeft:20}}))),E5=st("div",{name:"MuiTablePagination",slot:"Spacer"})({flex:"1 1 100%"}),T5=st("p",{name:"MuiTablePagination",slot:"SelectLabel"})($t(({theme:n})=>({...n.typography.body2,flexShrink:0}))),R5=st(Su,{name:"MuiTablePagination",slot:"Select",overridesResolver:(n,r)=>({[`& .${Ql.selectIcon}`]:r.selectIcon,[`& .${Ql.select}`]:r.select,...r.input,...r.selectRoot})})({color:"inherit",fontSize:"inherit",flexShrink:0,marginRight:32,marginLeft:8,[`& .${Ql.select}`]:{paddingLeft:8,paddingRight:24,textAlign:"right",textAlignLast:"right"}}),w5=st(ob,{name:"MuiTablePagination",slot:"MenuItem"})({}),M5=st("p",{name:"MuiTablePagination",slot:"DisplayedRows"})($t(({theme:n})=>({...n.typography.body2,flexShrink:0})));function A5({from:n,to:r,count:o}){return`${n}–${r} of ${o!==-1?o:`more than ${r}`}`}function O5(n){return`Go to ${n} page`}const _5=n=>{const{classes:r}=n;return At({root:["root"],toolbar:["toolbar"],spacer:["spacer"],selectLabel:["selectLabel"],select:["select"],input:["input"],selectIcon:["selectIcon"],menuItem:["menuItem"],displayedRows:["displayedRows"],actions:["actions"]},S5,r)},j5=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTablePagination"}),{ActionsComponent:s=b5,backIconButtonProps:c,colSpan:d,component:p=No,count:m,disabled:h=!1,getItemAriaLabel:y=O5,labelDisplayedRows:b=A5,labelRowsPerPage:x="Rows per page:",nextIconButtonProps:R,onPageChange:C,onRowsPerPageChange:w,page:M,rowsPerPage:j,rowsPerPageOptions:D=[10,25,50,100],SelectProps:A={},showFirstButton:O=!1,showLastButton:_=!1,slotProps:L={},slots:Y={},...F}=i,K=i,E=_5(K),U=(L==null?void 0:L.select)??A,X=U.native?"option":w5;let at;(p===No||p==="td")&&(at=d||1e3);const it=Ja(U.id),Q=Ja(U.labelId),k=()=>m===-1?(M+1)*j:j===-1?m:Math.min(m,(M+1)*j),I={slots:Y,slotProps:L},[ot,Z]=kt("root",{ref:o,className:E.root,elementType:x5,externalForwardedProps:{...I,component:p,...F},ownerState:K,additionalProps:{colSpan:at}}),[N,G]=kt("toolbar",{className:E.toolbar,elementType:C5,externalForwardedProps:I,ownerState:K}),[et,tt]=kt("spacer",{className:E.spacer,elementType:E5,externalForwardedProps:I,ownerState:K}),[ut,lt]=kt("selectLabel",{className:E.selectLabel,elementType:T5,externalForwardedProps:I,ownerState:K,additionalProps:{id:Q}}),[ct,bt]=kt("select",{className:E.select,elementType:R5,externalForwardedProps:I,ownerState:K}),[wt,zt]=kt("menuItem",{className:E.menuItem,elementType:X,externalForwardedProps:I,ownerState:K}),[gt,_t]=kt("displayedRows",{className:E.displayedRows,elementType:M5,externalForwardedProps:I,ownerState:K});return g.jsx(ot,{...Z,children:g.jsxs(N,{...G,children:[g.jsx(et,{...tt}),D.length>1&&g.jsx(ut,{...lt,children:x}),D.length>1&&g.jsx(ct,{variant:"standard",...!U.variant&&{input:_0||(_0=g.jsx(bu,{}))},value:j,onChange:w,id:it,labelId:Q,...U,classes:{...U.classes,root:mt(E.input,E.selectRoot,(U.classes||{}).root),select:mt(E.select,(U.classes||{}).select),icon:mt(E.selectIcon,(U.classes||{}).icon)},disabled:h,...bt,children:D.map(xt=>S.createElement(wt,{...zt,key:xt.label?xt.label:xt,value:xt.value?xt.value:xt},xt.label?xt.label:xt))}),g.jsx(gt,{..._t,children:b({from:m===0?0:M*j+1,to:k(),count:m===-1?-1:m,page:M})}),g.jsx(s,{className:E.actions,backIconButtonProps:c,count:m,nextIconButtonProps:R,onPageChange:C,page:M,rowsPerPage:j,showFirstButton:O,showLastButton:_,slotProps:L.actions,slots:Y.actions,getItemAriaLabel:y,disabled:h})]})})});function N5(n){return Mt("MuiTableRow",n)}const j0=Rt("MuiTableRow",["root","selected","hover","head","footer"]),z5=n=>{const{classes:r,selected:o,hover:i,head:s,footer:c}=n;return At({root:["root",o&&"selected",i&&"hover",s&&"head",c&&"footer"]},N5,r)},D5=st("tr",{name:"MuiTableRow",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.head&&r.head,o.footer&&r.footer]}})($t(({theme:n})=>({color:"inherit",display:"table-row",verticalAlign:"middle",outline:0,[`&.${j0.hover}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${j0.selected}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:Oe(n.palette.primary.main,n.palette.action.selectedOpacity),"&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:Oe(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)}}}))),N0="tr",cb=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTableRow"}),{className:s,component:c=N0,hover:d=!1,selected:p=!1,...m}=i,h=S.useContext(xu),y={...i,component:c,hover:d,selected:p,head:h&&h.variant==="head",footer:h&&h.variant==="footer"},b=z5(y);return g.jsx(D5,{as:c,ref:o,className:mt(b.root,s),role:c===N0?null:"row",ownerState:y,...m})});function k5(n){return Mt("MuiTextField",n)}Rt("MuiTextField",["root"]);const B5={standard:np,filled:ep,outlined:rp},$5=n=>{const{classes:r}=n;return At({root:["root"]},k5,r)},L5=st(eb,{name:"MuiTextField",slot:"Root"})({}),Un=S.forwardRef(function(r,o){const i=Ot({props:r,name:"MuiTextField"}),{autoComplete:s,autoFocus:c=!1,children:d,className:p,color:m="primary",defaultValue:h,disabled:y=!1,error:b=!1,FormHelperTextProps:x,fullWidth:R=!1,helperText:C,id:w,InputLabelProps:M,inputProps:j,InputProps:D,inputRef:A,label:O,maxRows:_,minRows:L,multiline:Y=!1,name:F,onBlur:K,onChange:E,onFocus:U,placeholder:X,required:at=!1,rows:it,select:Q=!1,SelectProps:k,slots:I={},slotProps:ot={},type:Z,value:N,variant:G="outlined",...et}=i,tt={...i,autoFocus:c,color:m,disabled:y,error:b,fullWidth:R,multiline:Y,required:at,select:Q,variant:G},ut=$5(tt),lt=Ja(w),ct=C&&lt?`${lt}-helper-text`:void 0,bt=O&&lt?`${lt}-label`:void 0,wt=B5[G],zt={slots:I,slotProps:{input:D,inputLabel:M,htmlInput:j,formHelperText:x,select:k,...ot}},gt={},_t=zt.slotProps.inputLabel;G==="outlined"&&(_t&&typeof _t.shrink<"u"&&(gt.notched=_t.shrink),gt.label=O),Q&&((!k||!k.native)&&(gt.id=void 0),gt["aria-describedby"]=void 0);const[xt,qt]=kt("root",{elementType:L5,shouldForwardComponentProp:!0,externalForwardedProps:{...zt,...et},ownerState:tt,className:mt(ut.root,p),ref:o,additionalProps:{disabled:y,error:b,fullWidth:R,required:at,color:m,variant:G}}),[jt,Xt]=kt("input",{elementType:wt,externalForwardedProps:zt,additionalProps:gt,ownerState:tt}),[He,Kt]=kt("inputLabel",{elementType:nb,externalForwardedProps:zt,ownerState:tt}),[se,ie]=kt("htmlInput",{elementType:"input",externalForwardedProps:zt,ownerState:tt}),[de,Yt]=kt("formHelperText",{elementType:jM,externalForwardedProps:zt,ownerState:tt}),[ht,un]=kt("select",{elementType:Su,externalForwardedProps:zt,ownerState:tt}),he=g.jsx(jt,{"aria-describedby":ct,autoComplete:s,autoFocus:c,defaultValue:h,fullWidth:R,multiline:Y,name:F,rows:it,maxRows:_,minRows:L,type:Z,value:N,id:lt,inputRef:A,onBlur:K,onChange:E,onFocus:U,placeholder:X,inputProps:ie,slots:{input:I.htmlInput?se:void 0},...Xt});return g.jsxs(xt,{...qt,children:[O!=null&&O!==""&&g.jsx(He,{htmlFor:lt,id:bt,...Kt,children:O}),Q?g.jsx(ht,{"aria-describedby":ct,id:lt,labelId:bt,value:N,input:he,...un,children:d}):he,C&&g.jsx(de,{id:ct,...Yt,children:C})]})}),U5=Ue(g.jsx("path",{d:"M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5M2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1m18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1M11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1m0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1M5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0z"})),H5=Ue(g.jsx("path",{d:"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.81.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1"})),fb=S.createContext({mode:"light",toggleMode:()=>{}}),P5=()=>S.useContext(fb);function q5(){const{mode:n,toggleMode:r}=P5();return g.jsxs(Js,{display:"flex",alignItems:"center",gap:1,children:[n==="light"?g.jsx(U5,{}):g.jsx(H5,{}),g.jsx(P4,{checked:n==="dark",onChange:r})]})}const G5=[{to:"/",label:"Accueil"},{to:"/sessions",label:"Sessions"},{to:"/reservation",label:"Réservation"},{to:"/contact",label:"Contact"}];function I5(){const n=Do();return g.jsxs("nav",{className:Hf.nav,children:[G5.map(({to:r,label:o})=>g.jsx(Yl,{to:r,className:`${Hf.link} ${n.pathname===r?Hf.linkActive:""}`,children:o},r)),g.jsx(q5,{})]})}const V5="_footer_1fzlg_1",Y5="_list_1fzlg_21",F5="_link_1fzlg_39",X5="_linkListTitle_1fzlg_57",K5="_linkSection_1fzlg_65",Oo={footer:V5,list:Y5,link:F5,linkListTitle:X5,linkSection:K5};function Q5({href:n,children:r,title:o,...i}){return n.startsWith("http")||n.startsWith("mailto:")||n.startsWith("tel:")?g.jsx("a",{href:n,className:Oo.link,target:"_blank",rel:"noopener noreferrer",...i,children:o||r}):g.jsx(Yl,{to:n,className:Oo.link,...i,children:o||r})}function td({links:n,className:r,listTitle:o}){return!n||n.length==0?null:g.jsxs("section",{className:Oo.linkSection,children:[o&&g.jsx("h3",{className:Oo.linkListTitle,children:o}),g.jsx("ul",{className:Oo.list,children:n.map(i=>g.jsx("li",{children:g.jsx(Q5,{href:i.href,title:i.title})},i.href))})]})}const W5=[{href:"https://facebook.com/",title:"Facebook"},{href:"https://www.instagram.com/",title:"Instagram"}],Z5=[{href:"/sessions",title:"Sessions"},{href:"/mentions",title:"Mention Légales"},{href:"/login",title:"Administration"}],J5=[{href:"mailto:<EMAIL>",title:"<EMAIL>"},{href:"tel:+33123456789",title:"01 23 45 67 89"},{href:"/contact",title:"Formulaire de contact"}];function t3(){return g.jsxs("footer",{className:Oo.footer,children:[g.jsx(td,{links:W5,listTitle:"Réseaux"}),g.jsx(td,{links:Z5,listTitle:"Contenu"}),g.jsx(td,{links:J5,listTitle:"Contacts"})]})}const e3="_app_ls84x_1",n3="_main_ls84x_15",a3="_form_ls84x_31",r3="_input_ls84x_43",o3="_select_ls84x_45",l3="_textarea_ls84x_47",i3="_button_ls84x_59",s3="_foldableList_ls84x_87",u3="_foldableListItem_ls84x_95",c3="_sessionCard_ls84x_103",f3="_sessionHeader_ls84x_131",d3="_sessionMeta_ls84x_151",p3="_theme_ls84x_167",m3="_duration_ls84x_169",h3="_price_ls84x_171",g3="_description_ls84x_183",y3="_sessionDetails_ls84x_195",v3="_participants_ls84x_209",b3="_availability_ls84x_219",S3="_availableSpots_ls84x_233",x3="_noSpots_ls84x_243",C3="_nextDate_ls84x_253",E3="_reserveButton_ls84x_263",T3="_slotSelector_ls84x_295",R3="_formGroup_ls84x_303",w3="_noSlots_ls84x_325",M3="_sessionInfo_ls84x_343",A3="_sessionDescription_ls84x_369",O3="_bookingForm_ls84x_383",_3="_participantHint_ls84x_393",j3="_errorList_ls84x_409",N3="_error_ls84x_409",z3="_successMessage_ls84x_437",D3="_bookingDetails_ls84x_455",k3="_errorBoundary_ls84x_489",B3="_errorDetails_ls84x_521",$3="_loadingContainer_ls84x_561",L3="_spinner_ls84x_601",U3="_spinnerInner_ls84x_613",H3="_spin_ls84x_601",P3="_loadingMessage_ls84x_651",q3="_errorAlertContainer_ls84x_663",G3="_errorAlert_ls84x_663",I3="_errorValidation_ls84x_691",V3="_errorNetwork_ls84x_703",Y3="_errorServer_ls84x_715",F3="_errorUnknown_ls84x_727",X3="_errorContent_ls84x_739",K3="_errorIcon_ls84x_751",Q3="_errorText_ls84x_761",W3="_errorDismiss_ls84x_771",Z3="_notFoundContainer_ls84x_801",J3="_notFoundContent_ls84x_819",tA="_notFoundTitle_ls84x_829",eA="_spookyGlow_ls84x_1",nA="_notFoundSubtitle_ls84x_865",aA="_notFoundMessage_ls84x_877",rA="_notFoundActions_ls84x_899",oA="_notFoundButton_ls84x_915",lA="_notFoundFooter_ls84x_953",iA="_notFoundLink_ls84x_965",dt={app:e3,main:n3,form:a3,input:r3,select:o3,textarea:l3,button:i3,foldableList:s3,foldableListItem:u3,sessionCard:c3,sessionHeader:f3,sessionMeta:d3,theme:p3,duration:m3,price:h3,description:g3,sessionDetails:y3,participants:v3,availability:b3,availableSpots:S3,noSpots:x3,nextDate:C3,reserveButton:E3,slotSelector:T3,formGroup:R3,noSlots:w3,sessionInfo:M3,sessionDescription:A3,bookingForm:O3,participantHint:_3,errorList:j3,error:N3,successMessage:z3,bookingDetails:D3,errorBoundary:k3,errorDetails:B3,loadingContainer:$3,"loading-small":"_loading-small_ls84x_577","loading-medium":"_loading-medium_ls84x_585","loading-large":"_loading-large_ls84x_593",spinner:L3,spinnerInner:U3,spin:H3,loadingMessage:P3,errorAlertContainer:q3,errorAlert:G3,errorValidation:I3,errorNetwork:V3,errorServer:Y3,errorUnknown:F3,errorContent:X3,errorIcon:K3,errorText:Q3,errorDismiss:W3,notFoundContainer:Z3,notFoundContent:J3,notFoundTitle:tA,spookyGlow:eA,notFoundSubtitle:nA,notFoundMessage:aA,notFoundActions:rA,notFoundButton:oA,notFoundFooter:lA,notFoundLink:iA};class z0 extends S.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,o){console.error("ErrorBoundary caught an error:",r,o)}render(){var r;return this.state.hasError?this.props.fallback?this.props.fallback:g.jsxs("div",{className:dt.errorBoundary,children:[g.jsx("h2",{children:"🎭 Quelque chose d'effrayant s'est produit..."}),g.jsx("p",{children:"Une erreur inattendue est survenue dans l'application. Nos fantômes techniques travaillent à la résoudre !"}),g.jsxs("details",{className:dt.errorDetails,children:[g.jsx("summary",{children:"Détails techniques"}),g.jsx("pre",{children:(r=this.state.error)==null?void 0:r.stack})]}),g.jsx("button",{className:dt.button,onClick:()=>window.location.reload(),children:"🔄 Recharger la page"})]}):this.props.children}}function sA({elements:n}){return g.jsx("ul",{children:n.map((r,o)=>g.jsxs("li",{children:[g.jsx("strong",{children:r.header})," — ",r.text]},r.header+o))})}const Ve="https://maison.hor",uA=["Game Master","Manager","Technicien","Receptioniste"],D0={pending:"En attente",confirmed:"Confirmée",cancelled:"Annulée"},Ea=()=>{const[n,r]=S.useState([]),[o,i]=S.useState(!1),s=S.useCallback(h=>{r(y=>[...y,h])},[]),c=S.useCallback(h=>{r(y=>y.filter((b,x)=>x!==h))},[]),d=S.useCallback(()=>{r([])},[]),p=S.useCallback(h=>{if(h.name==="TypeError"&&h.message.includes("fetch"))return{message:"Impossible de se connecter au serveur. Vérifiez votre connexion internet.",type:"network"};if(h.status)switch(h.status){case 400:return{message:h.message||"Données invalides",type:"validation"};case 404:return{message:"Ressource non trouvée",type:"server"};case 500:return{message:"Erreur interne du serveur",type:"server"};default:return{message:h.message||"Une erreur est survenue",type:"server"}}return{message:h.message||"Une erreur inattendue est survenue",type:"unknown"}},[]),m=S.useCallback(async(h,y,b)=>{i(!0),d();try{const x=await h();return y==null||y(x),x}catch(x){const R=p(x);return s(R),b==null||b(R),null}finally{i(!1)}},[p,s,d]);return{errors:n,isLoading:o,addError:s,removeError:c,clearErrors:d,handleApiError:p,executeWithErrorHandling:m}};function Lo({message:n="Chargement...",size:r="medium"}){return g.jsxs("div",{className:`${dt.loadingContainer} ${dt[`loading-${r}`]}`,children:[g.jsx("div",{className:dt.spinner,children:g.jsx("div",{className:dt.spinnerInner})}),g.jsx("p",{className:dt.loadingMessage,children:n})]})}function cA(){const{executeWithErrorHandling:n}=Ea(),[r,o]=S.useState(),i=async()=>{n(async()=>{const s=await fetch(Ve+"/rooms?limit=10");if(!s.ok)throw new Error(`Erreur ${s.status}: ${s.statusText}`);return await s.json()},s=>{o(s)})};return S.useEffect(()=>{i()},[]),g.jsxs(g.Fragment,{children:[g.jsx("h1",{children:"La Maison Horrifique"}),g.jsx("section",{children:r===void 0?g.jsx(Lo,{message:"Chargement des utilisateurs...",size:"large"}):g.jsx(g.Fragment,{children:r.data.length>0&&g.jsxs(g.Fragment,{children:[g.jsx("h2",{children:"Nos sessions d’escape game"}),g.jsx(sA,{elements:r.data.map(s=>({header:s.name,text:s.description}))})]})})}),g.jsxs("section",{children:[g.jsx("h2",{children:"À propos de l’entreprise"}),g.jsx("p",{children:"La Maison Horrifique propose des expériences immersives et terrifiantes, parfaites pour les amateurs de sensations fortes. Notre équipe dévouée veille à créer des univers riches et effrayants pour tous les aventuriers."})]})]})}function fA({room:n,onReserve:r}){const o=h=>h?`${h}€`:"Prix non défini",i=h=>h?`${h} min`:"Durée non définie",s=()=>n.availableSlots?n.availableSlots.reduce((h,y)=>h+y.availableSpots,0):0,c=()=>{if(!n.availableSlots||n.availableSlots.length===0)return null;const h=[...n.availableSlots].filter(y=>y.availableSpots>0).sort((y,b)=>new Date(y.date).getTime()-new Date(b.date).getTime());return h.length>0?h[0].date:null},d=h=>new Date(h).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),p=c(),m=s();return g.jsxs("div",{className:dt.sessionCard,children:[g.jsxs("div",{className:dt.sessionHeader,children:[g.jsx("h3",{children:n.name}),g.jsxs("div",{className:dt.sessionMeta,children:[n.theme&&g.jsxs("span",{className:dt.theme,children:["🎭 ",n.theme]}),g.jsxs("span",{className:dt.duration,children:["⏱️ ",i(n.duration)]}),g.jsxs("span",{className:dt.price,children:["💰 ",o(n.price)]})]})]}),g.jsx("p",{className:dt.description,children:n.description}),g.jsxs("div",{className:dt.sessionDetails,children:[g.jsxs("div",{className:dt.participants,children:[g.jsx("strong",{children:"Participants:"})," ",n.minParticipants||1," - ",n.maxParticipants||"illimité"]}),g.jsx("div",{className:dt.availability,children:m>0?g.jsxs(g.Fragment,{children:[g.jsxs("span",{className:dt.availableSpots,children:["✅ ",m," places disponibles"]}),p&&g.jsxs("span",{className:dt.nextDate,children:["📅 Prochaine date: ",d(p)]})]}):g.jsx("span",{className:dt.noSpots,children:"❌ Aucune place disponible"})})]}),r&&m>0&&g.jsx("button",{className:dt.reserveButton,onClick:()=>r(n.id),children:"Réserver cette session"})]})}const dA=n=>{switch(n){case"validation":return"⚠️";case"network":return"🌐";case"server":return"🔧";default:return"❌"}},pA=n=>{switch(n){case"validation":return dt.errorValidation;case"network":return dt.errorNetwork;case"server":return dt.errorServer;default:return dt.errorUnknown}};function db({errors:n,onDismiss:r,className:o}){return n.length===0?null:g.jsx("div",{className:`${dt.errorAlertContainer} ${o||""}`,children:n.map((i,s)=>g.jsxs("div",{className:`${dt.errorAlert} ${pA(i.type)}`,children:[g.jsxs("div",{className:dt.errorContent,children:[g.jsx("span",{className:dt.errorIcon,children:dA(i.type)}),g.jsxs("div",{className:dt.errorText,children:[g.jsx("strong",{children:i.field?`${i.field}: `:""}),i.message]})]}),r&&g.jsx("button",{className:dt.errorDismiss,onClick:()=>r(s),"aria-label":"Fermer l'erreur",children:"✕"})]},s))})}function mA(){const[n,r]=S.useState([]),o=oi(),{errors:i,isLoading:s,executeWithErrorHandling:c,removeError:d}=Ea();S.useEffect(()=>{p()},[]);const p=async()=>{await c(async()=>{const y=await fetch(Ve+"/rooms");if(!y.ok)throw new Error(`Erreur ${y.status}: ${y.statusText}`);const b=await y.json();return console.log(b),b.data},y=>{r(y)})},m=y=>{o(`/reservation?roomId=${y}`)},h=()=>{p()};return g.jsxs(g.Fragment,{children:[g.jsx("h1",{children:"Nos Sessions"}),g.jsx(db,{errors:i,onDismiss:d}),s?g.jsx(Lo,{message:"Chargement des sessions d'escape game...",size:"large"}):i.length>0?g.jsxs("div",{style:{textAlign:"center",padding:"2rem"},children:[g.jsx("p",{children:"Impossible de charger les sessions."}),g.jsx("button",{onClick:h,style:{marginTop:"1rem"},children:"🔄 Réessayer"})]}):n.length===0?g.jsxs("div",{style:{textAlign:"center",padding:"2rem"},children:[g.jsx("p",{children:"Aucune session disponible pour le moment."}),g.jsx("button",{onClick:h,style:{marginTop:"1rem"},children:"🔄 Actualiser"})]}):g.jsx("div",{children:n.map(y=>g.jsx(fA,{room:y,onReserve:m},y.id))})]})}function hA({selectedRoomId:n,selectedSlotId:r,onSlotChange:o,onRoomChange:i}){const[s,c]=S.useState([]),[d,p]=S.useState(null),{errors:m,isLoading:h,executeWithErrorHandling:y,removeError:b}=Ea();S.useEffect(()=>{x()},[]),S.useEffect(()=>{if(n&&s.length>0){const j=s.find(D=>D.id===n);j&&(!d||d.id!==j.id)&&(p(j),i==null||i(j.id,j))}},[n,s]);const x=async()=>{await y(async()=>{const j=await fetch(Ve+"/rooms");if(!j.ok)throw new Error(`Erreur ${j.status}: ${j.statusText}`);return(await j.json()).data},j=>{c(j)})},R=j=>{const D=parseInt(j.target.value);if(isNaN(D)){p(null);return}const A=s.find(O=>O.id===D);A?(p(A),i==null||i(A.id,A)):p(null)},C=j=>{var O;const D=parseInt(j.target.value);if(isNaN(D)||!d)return;const A=(O=d.availableSlots)==null?void 0:O.find(_=>_.id===D);A&&o(d.id,D,A)},w=j=>`${new Date(j.date).toLocaleDateString("fr-FR",{weekday:"short",month:"short",day:"numeric"})} - ${j.startTime} à ${j.endTime} (${j.availableSpots} places)`,M=()=>d!=null&&d.availableSlots?d.availableSlots.filter(j=>j.availableSpots>0):[];return h?g.jsx(Lo,{message:"Chargement des sessions...",size:"small"}):g.jsxs("div",{className:dt.slotSelector,children:[g.jsx(db,{errors:m,onDismiss:b}),g.jsxs("div",{className:dt.formGroup,children:[g.jsx("label",{htmlFor:"room-select",children:"Session d'escape game *"}),g.jsxs("select",{id:"room-select",value:(d==null?void 0:d.id)||"",onChange:R,className:dt.input,required:!0,children:[g.jsx("option",{value:"",children:"-- Choisissez une session --"}),s.map(j=>g.jsxs("option",{value:j.id,children:[j.name," - ",j.price,"€ (",j.duration," min)"]},j.id))]})]}),d&&g.jsxs("div",{className:dt.formGroup,children:[g.jsx("label",{htmlFor:"slot-select",children:"Créneau disponible *"}),M().length>0?g.jsxs("select",{id:"slot-select",value:r||"",onChange:C,className:dt.input,required:!0,children:[g.jsx("option",{value:"",children:"-- Choisissez un créneau --"}),M().map(j=>g.jsx("option",{value:j.id,children:w(j)},j.id))]}):g.jsx("p",{className:dt.noSlots,children:"❌ Aucun créneau disponible pour cette session"})]}),d&&g.jsxs("div",{className:dt.sessionInfo,children:[g.jsx("h4",{children:"Informations de la session"}),g.jsxs("div",{className:dt.sessionMeta,children:[g.jsxs("span",{children:["🎭 ",d.theme]}),g.jsxs("span",{children:["⏱️ ",d.duration," minutes"]}),g.jsxs("span",{children:["💰 ",d.price,"€"]}),g.jsxs("span",{children:["👥 ",d.minParticipants," - ",d.maxParticipants," participants"]})]}),g.jsx("p",{className:dt.sessionDescription,children:d.description})]})]})}function Rd(n){const{label:r,name:o,value:i,required:s}=n;let c;switch(n.type){case"textarea":c=g.jsx("textarea",{name:o,value:i,onChange:n.onChange,rows:n.rows,required:s===void 0?!0:s,className:dt.textarea});break;case"select":c=g.jsx("select",{name:o,value:i,onChange:n.onChange,required:s===void 0?!0:s,className:dt.input,children:n.options.map((d,p)=>g.jsx("option",{value:d.value,children:d.text},d.value+p))});break;default:c=g.jsx("input",{type:n.type,name:o,value:i,onChange:n.onChange,required:s===void 0?!0:s,className:dt.input})}return g.jsxs("div",{children:[g.jsx("label",{htmlFor:o,children:r}),c]})}function pb({type:n,children:r,onClick:o,disabled:i}){return g.jsx("button",{type:n,className:dt.button,onClick:o,disabled:i,children:r})}function gA({initialRoomId:n,onSubmitted:r}){const[o,i]=S.useState({customerEmail:"",roomId:n||0,slotId:0,participantCount:1}),[s,c]=S.useState(null),[d,p]=S.useState(null),[m,h]=S.useState([]),[y,b]=S.useState(!1),x=(D,A,O)=>{i(_=>({..._,roomId:D,slotId:A})),p(O),h([])},R=(D,A)=>{i(O=>({...O,roomId:D,slotId:0})),c(A),p(null),h([])},C=D=>{i(A=>({...A,customerEmail:D.target.value})),h([])},w=D=>{const A=parseInt(D.target.value)||1;i(O=>({...O,participantCount:A})),h([])},M=()=>{const D=[];return o.customerEmail?/\S+@\S+\.\S+/.test(o.customerEmail)||D.push("L'adresse email n'est pas valide"):D.push("L'adresse email est requise"),o.roomId||D.push("Veuillez sélectionner une session"),o.slotId||D.push("Veuillez sélectionner un créneau"),s&&o.participantCount<(s.minParticipants||1)&&D.push(`Minimum ${s.minParticipants} participants requis pour cette session`),s&&s.maxParticipants&&o.participantCount>s.maxParticipants&&D.push(`Maximum ${s.maxParticipants} participants autorisés pour cette session`),d&&o.participantCount>d.availableSpots&&D.push(`Seulement ${d.availableSpots} places disponibles pour ce créneau`),D},j=async D=>{D.preventDefault();const A=M();if(A.length>0){h(A);return}b(!0),h([]);try{const O={roomId:o.roomId,slotId:o.slotId,customerEmail:o.customerEmail,participantCount:o.participantCount},_=await fetch(Ve+"/bookings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(O)});if(!_.ok){const Y=await _.json();throw new Error(Y.error||"Erreur lors de la réservation")}const L=await _.json();r(L)}catch(O){h([O instanceof Error?O.message:"Erreur lors de la réservation"])}finally{b(!1)}};return g.jsxs("form",{onSubmit:j,className:dt.bookingForm,children:[g.jsx(hA,{selectedRoomId:o.roomId||void 0,selectedSlotId:o.slotId||void 0,onSlotChange:x,onRoomChange:R}),g.jsx("div",{className:dt.formGroup,children:g.jsx(Rd,{label:"Adresse email",type:"email",name:"customerEmail",value:o.customerEmail,onChange:C,required:!0})}),g.jsxs("div",{className:dt.formGroup,children:[g.jsx(Rd,{label:"Nombre de participants",type:"number",name:"participantCount",value:o.participantCount.toString(),onChange:w,required:!0}),s&&g.jsxs("small",{className:dt.participantHint,children:["Cette session accepte de ",s.minParticipants," à ",s.maxParticipants," participants"]})]}),m.length>0&&g.jsx("div",{className:dt.errorList,children:m.map((D,A)=>g.jsxs("p",{className:dt.error,children:["❌ ",D]},A))}),g.jsx(pb,{type:"submit",disabled:y,children:y?"Réservation en cours...":"Confirmer la réservation"})]})}function yA(){const[n]=Vx(),[r,o]=S.useState(null),[i,s]=S.useState();S.useEffect(()=>{const p=n.get("roomId");if(p){const m=parseInt(p);isNaN(m)||s(m)}},[n]);const c=p=>{o(p)},d=p=>new Date(p).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"});return g.jsxs("div",{className:dt.form,children:[g.jsx("h1",{children:"Réservation"}),r?g.jsxs("div",{className:dt.successMessage,children:[g.jsx("h2",{children:"✅ Réservation confirmée !"}),g.jsx("p",{children:"Merci pour votre réservation ! Voici les détails :"}),g.jsxs("div",{className:dt.bookingDetails,children:[g.jsx("h4",{children:"Détails de votre réservation"}),g.jsxs("p",{children:[g.jsx("strong",{children:"Numéro de réservation :"})," #",r.id]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Email :"})," ",r.customerEmail]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Nombre de participants :"})," ",r.participantCount]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Statut :"})," ",r.status==="confirmed"?"Confirmée":r.status]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Date de réservation :"})," ",d(r.createdAt)]})]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Important :"})," Vous recevrez un email de confirmation avec tous les détails de votre session d'escape game. Pensez à arriver 15 minutes avant l'heure prévue !"]})]}):g.jsx(gA,{initialRoomId:i,onSubmitted:c})]})}function vA({formdata:n,setFormdata:r,setSubmitted:o,fields:i,submitText:s}){const c=p=>{r({...n,[p.target.name]:p.target.value})},d=p=>{p.preventDefault(),o(!0)};return g.jsxs("form",{onSubmit:d,children:[i.map((p,m)=>g.jsx(Rd,{...p,onChange:c},m)),g.jsx(pb,{type:"submit",children:s})]})}function bA({formdata:n,setFormdata:r,setSubmitted:o}){const i=[{label:"Nom",type:"text",name:"name",value:n.name},{label:"Email",type:"email",name:"email",value:n.email},{label:"Message",type:"textarea",name:"message",value:n.message,rows:5}];return g.jsx(vA,{formdata:n,setFormdata:r,setSubmitted:o,fields:i,submitText:"Envoyer"})}function SA(){const[n,r]=S.useState({name:"",email:"",message:""}),[o,i]=S.useState(!1);return g.jsxs("div",{className:dt.form,children:[g.jsx("h1",{children:"Contact"}),o?g.jsxs("p",{children:["Merci pour votre message, ",n.name," ! Nous vous répondrons rapidement."]}):g.jsx(bA,{formdata:n,setFormdata:r,setSubmitted:i})]})}function xA(){return g.jsxs(g.Fragment,{children:[g.jsx("h1",{children:"Mentions légales"}),g.jsx("p",{children:"La Maison Horrifique - SARL au capital de 10 000€"}),g.jsx("p",{children:"Siège social : 12 rue de l'horreur, 75000 Paris"}),g.jsx("p",{children:"Directeur de publication : Alain Térieur"}),g.jsx("p",{children:"Contact : <EMAIL>"}),g.jsx("p",{children:"Ce site est une réalisation pédagogique."})]})}function CA(){return g.jsx("div",{className:dt.notFoundContainer,children:g.jsxs("div",{className:dt.notFoundContent,children:[g.jsx("h1",{className:dt.notFoundTitle,children:"👻 404"}),g.jsx("h2",{className:dt.notFoundSubtitle,children:"Page Introuvable"}),g.jsxs("div",{className:dt.notFoundMessage,children:[g.jsx("p",{children:"🕯️ Cette page s'est volatilisée dans les ténèbres..."}),g.jsx("p",{children:"Nos fantômes l'ont peut-être emportée dans l'au-delà !"})]}),g.jsxs("div",{className:dt.notFoundActions,children:[g.jsx(Yl,{to:"/",className:dt.notFoundButton,children:"🏠 Retour à l'accueil"}),g.jsx(Yl,{to:"/sessions",className:dt.notFoundButton,children:"🎭 Voir nos sessions"})]}),g.jsx("div",{className:dt.notFoundFooter,children:g.jsxs("p",{children:["Si vous pensez qu'il s'agit d'une erreur,",g.jsx(Yl,{to:"/contact",className:dt.notFoundLink,children:" contactez-nous"})]})})]})})}const EA=Ue(g.jsx("path",{d:"M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2M7 18V6h10v12zm9-7V9.14C16 8.51 15.55 8 15 8H9c-.55 0-1 .51-1 1.14v1.96c.55 0 1 .45 1 1s-.45 1-1 1v1.76c0 .63.45 1.14 1 1.14h6c.55 0 1-.51 1-1.14V13c-.55 0-1-.45-1-1s.45-1 1-1m-3.5 3.5h-1v-1h1zm0-2h-1v-1h1zm0-2h-1v-1h1z"})),TA=Ue(g.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"})),RA=Ue(g.jsx("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"})),wA=Ue(g.jsx("path",{d:"M12 10.9c-.61 0-1.1.49-1.1 1.1s.49 1.1 1.1 1.1 1.1-.49 1.1-1.1-.49-1.1-1.1-1.1M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m2.19 12.19L6 18l3.81-8.19L18 6z"})),MA=Ue(g.jsx("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"})),AA=Ue(g.jsx("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"})),OA="_containerGrid_1dz5a_1",_A="_cardAdmin_1dz5a_13",k0={containerGrid:OA,cardAdmin:_A},jA=[{title:"Utilisateurs",icon:g.jsx(MA,{fontSize:"large"}),to:"/admin/users"},{title:"Sessions",icon:g.jsx(wA,{fontSize:"large"}),to:"/admin/sessions"},{title:"Réservations",icon:g.jsx(EA,{fontSize:"large"}),to:"/admin/bookings"}];function NA(){const n=oi();return g.jsx(y0,{className:k0.containerGrid,container:!0,spacing:3,children:jA.map((r,o)=>g.jsx(y0,{margin:2,minWidth:201,children:g.jsx(xw,{className:k0.cardAdmin,onClick:()=>n(r.to),children:g.jsxs(Rw,{sx:{textAlign:"center"},children:[g.jsx("div",{style:{marginBottom:12},children:r.icon}),g.jsx(mu,{variant:"h6",children:r.title})]})})},o))})}function zA(){return g.jsxs(g.Fragment,{children:[g.jsx("h1",{children:"Admin"}),g.jsx(NA,{})]})}function lp({title:n,children:r}){return g.jsxs(jw,{maxWidth:"lg",sx:{mt:4,mb:4},children:[g.jsx(mu,{variant:"h4",gutterBottom:!0,children:n}),r]})}function DA({item:n,onDeleteClick:r,onActionClick:o,readOnly:i=!1}){return g.jsxs(cb,{children:[...Object.values(n).map(s=>g.jsx(No,{children:s})),g.jsxs(No,{children:[g.jsx(Tr,{onClick:()=>o(n),children:i?g.jsx(AA,{}):g.jsx(RA,{})}),g.jsx(Tr,{onClick:()=>r(n),children:g.jsx(TA,{})})]})]})}function ip({tableHeaders:n,data:r,readonlyTable:o=!1,updateData:i,hiddenProps:s=[],handleDelete:c,handleAction:d}){const[p,m]=S.useState(0),[h,y]=S.useState(10),b=x=>{y(parseInt(x.target.value,10)),m(0)};return S.useEffect(()=>{i(p,h)},[p,h]),g.jsxs(r5,{component:_r,children:[g.jsxs(V4,{children:[g.jsx(u5,{children:g.jsx(cb,{children:n.map((x,R)=>g.jsx(No,{children:x},R))})}),g.jsx(Q4,{children:r.data.map((x,R)=>{const C=Object.fromEntries(Object.entries(x).filter(([w])=>!s.includes(w)));return g.jsx(DA,{item:C,onActionClick:()=>d(x),onDeleteClick:()=>c(x),readOnly:o},R)})})]}),g.jsx(j5,{rowsPerPageOptions:[5,10,25],component:"div",count:r.total,rowsPerPage:h,page:p,onPageChange:(x,R)=>m(R),onRowsPerPageChange:b})]})}function sp({isOpened:n,title:r,setIsOpened:o,onCancel:i=()=>{},children:s,item:c,handleDelete:d,handleEdit:p}){const m=()=>{o(!1),setTimeout(()=>i(),150)};return g.jsxs(sM,{open:n,onClose:m,maxWidth:"md",fullWidth:!0,children:[g.jsx(xM,{children:r}),g.jsx(vM,{children:s}),g.jsx(dM,{children:g.jsxs(Js,{sx:{display:"flex",justifyContent:"space-between",width:"100%"},children:[g.jsx(Ps,{color:"error",onClick:d,children:"Supprimer"}),g.jsxs(Js,{children:[g.jsx(Ps,{color:"success",onClick:m,children:"Annuler"}),p!==void 0&&g.jsx(Ps,{color:"success",onClick:p,children:"Enregistrer"})]})]})})]})}function wd({label:n,value:r,onChange:o,selectOptions:i,optionValues:s}){return g.jsxs(eb,{fullWidth:!0,margin:"normal",children:[g.jsx(nb,{id:"select-label",children:n}),g.jsx(Su,{labelId:"select-label",value:r,label:n,onChange:o,fullWidth:!0,children:i.map((c,d)=>{const p=s&&s[d]?s[d]:c;return g.jsx(ob,{value:p,children:c},d)})})]})}function kA({isOpened:n,setIsOpened:r,editedUser:o,setEditedUser:i,onEdit:s=()=>{},onDelete:c=()=>{}}){const{executeWithErrorHandling:d}=Ea(),[p,m]=S.useState({}),h=async()=>{!o||!window.confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?")||d(async()=>{const b=await fetch(Ve+"/users/"+o.id,{method:"DELETE"});if(!b.ok)throw new Error(`Erreur ${b.status}: ${b.statusText}`)},()=>{c(),i(null),r(!1)})},y=async()=>{!p||!o||d(async()=>{const b=await fetch(Ve+"/users/"+o.id,{method:"PATCH",body:JSON.stringify(p)});if(!b.ok)throw new Error(`Erreur ${b.status}: ${b.statusText}`);return await b.json()},b=>{i(b),s(b),r(!1)})};return S.useEffect(()=>{m(o?{email:o.email,role:o.role}:{})},[o]),o&&g.jsxs(sp,{title:`Modification de ${o.firstName} ${o.lastName}`,isOpened:n,setIsOpened:r,item:o,handleDelete:h,handleEdit:y,children:[g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Email",value:p.email??"",onChange:b=>m({...p,email:b.target.value})}),g.jsx(wd,{label:"Rôle",value:p.role,onChange:b=>m({...p,role:b.target.value}),selectOptions:uA})]})}function BA(){const{executeWithErrorHandling:n}=Ea(),[r,o]=S.useState(),[i,s]=S.useState(!1),[c,d]=S.useState(null),p=async(x=0,R=10)=>{await n(async()=>{const C=await fetch(Ve+`/users?page=${x}&limit=${R}`);if(!C.ok)throw new Error(`Erreur ${C.status}: ${C.statusText}`);return await C.json()},C=>{o(C)})},m=async x=>{window.confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?")&&n(async()=>{const R=await fetch(Ve+"/users/"+x.id,{method:"DELETE"});if(!R.ok)throw new Error(`Erreur ${R.status}: ${R.statusText}`)},()=>{if(r===void 0)return;const R={...r};R.data=r.data.filter(C=>C.id!==x.id),o(R)})},h=x=>{d(x),s(!0)},y=()=>{if(r===void 0||c===null)return;const x={...r};x.data=r.data.filter(R=>R.id!==c.id),o(x)},b=x=>{if(r===void 0)return;const R=r.data.map(C=>x.id===C.id?x:C);o({...r,data:R})};return S.useEffect(()=>{p()},[]),g.jsx(lp,{title:"Utilisateurs",children:r===void 0?g.jsx(Lo,{message:"Chargement des utilisateurs...",size:"large"}):g.jsxs(g.Fragment,{children:[g.jsx(ip,{tableHeaders:["ID","Prénom","Nom","Courriel","Rôle","Embauche","Actions"],data:r,updateData:p,handleAction:h,handleDelete:m}),g.jsx(kA,{isOpened:i,setIsOpened:s,editedUser:c,setEditedUser:d,onEdit:b,onDelete:y})]})})}function $A({isOpened:n,setIsOpened:r,editedSession:o,setEditedSession:i,onDelete:s=()=>{},onEdit:c=()=>{}}){const{executeWithErrorHandling:d}=Ea(),[p,m]=S.useState({}),h=async()=>{!o||!window.confirm("Êtes-vous sûr de vouloir supprimer cette session ?")||d(async()=>{const b=await fetch(Ve+"/rooms/"+o.id,{method:"DELETE"});if(!b.ok)throw new Error(`Erreur ${b.status}: ${b.statusText}`)},()=>{s(),i(null),r(!1)})},y=async()=>{!p||!o||d(async()=>{const b=await fetch(Ve+"/rooms/"+o.id,{method:"PATCH",body:JSON.stringify(p)});if(!b.ok)throw new Error(`Erreur ${b.status}: ${b.statusText}`);return await b.json()},b=>{i(b),c(b),r(!1)})};return S.useEffect(()=>{m(o??{})},[o]),o&&g.jsxs(sp,{title:"Modification de "+o.name,isOpened:n,setIsOpened:r,item:o,handleDelete:h,handleEdit:y,children:[g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Nom",value:p.name??"",onChange:b=>m({...p,name:b.target.value})}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Description",value:p.description??"",onChange:b=>m({...p,description:b.target.value}),multiline:!0,maxRows:4}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Thème",value:p.theme??"",onChange:b=>m({...p,theme:b.target.value})}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Prix en Euros",value:p.price??"",onChange:b=>m({...p,price:parseInt(b.target.value,10)}),slotProps:{htmlInput:{type:"number"}}}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Durée en minutes",value:p.duration??"",onChange:b=>m({...p,duration:parseInt(b.target.value,10)}),slotProps:{htmlInput:{type:"number"}}}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Nombre de participants minimum",value:p.minParticipants??"",onChange:b=>m({...p,minParticipants:parseInt(b.target.value,10)}),slotProps:{htmlInput:{type:"number"}}}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Nombre de participants maximum",value:p.maxParticipants??"",onChange:b=>m({...p,maxParticipants:parseInt(b.target.value,10)}),slotProps:{htmlInput:{type:"number"}}})]})}function LA(){const{executeWithErrorHandling:n}=Ea(),[r,o]=S.useState(),[i,s]=S.useState(!1),[c,d]=S.useState(null),p=async(x=0,R=10)=>{n(async()=>{const C=await fetch(Ve+`/rooms?page=${x}&limit=${R}`);if(!C.ok)throw new Error(`Erreur ${C.status}: ${C.statusText}`);return await C.json()},C=>{o(C)})},m=async x=>{window.confirm("Êtes-vous sûr de vouloir supprimer cette session ?")&&n(async()=>{const R=await fetch(Ve+"/rooms/"+x.id,{method:"DELETE"});if(!R.ok)throw new Error(`Erreur ${R.status}: ${R.statusText}`)},()=>{if(r===void 0)return;const R={...r};R.data=r.data.filter(C=>C.id!==x.id),o(R)})},h=x=>{d(x),s(!0)},y=()=>{if(r===void 0||c===null)return;const x={...r};x.data=r.data.filter(R=>R.id!==c.id),o(x)},b=x=>{if(r===void 0)return;const R=r.data.map(C=>x.id===C.id?x:C);o({...r,data:R})};return S.useEffect(()=>{p()},[]),g.jsx(lp,{title:"Utilisateurs",children:r===void 0?g.jsx(Lo,{message:"Chargement des utilisateurs...",size:"large"}):g.jsxs(g.Fragment,{children:[g.jsx(ip,{tableHeaders:["ID","Nom","Thème","Durée","Prix","Participants min","Participants max","Actions"],data:r,updateData:p,hiddenProps:["description","availableSlots"],handleAction:h,handleDelete:m}),g.jsx($A,{isOpened:i,setIsOpened:s,editedSession:c,setEditedSession:d,onDelete:y,onEdit:b})]})})}function UA({isOpened:n,setIsOpened:r,editedBooking:o,setEditedBooking:i,onDelete:s=()=>{},onEdit:c=()=>{}}){const{executeWithErrorHandling:d}=Ea(),[p,m]=S.useState({}),[h,y]=S.useState([]),b=async()=>{!o||!window.confirm("Êtes-vous sûr de vouloir supprimer cette réservation ?")||d(async()=>{const C=await fetch(Ve+"/bookings/"+o.id,{method:"DELETE"});if(!C.ok)throw new Error(`Erreur ${C.status}: ${C.statusText}`)},()=>{s(),i(null),r(!1)})},x=async()=>{!p||!o||d(async()=>{const C=await fetch(Ve+"/bookings/"+o.id,{method:"PATCH",body:JSON.stringify(p)});if(!C.ok)throw new Error(`Erreur ${C.status}: ${C.statusText}`);return await C.json()},C=>{i(C),c(C),r(!1)})},R=async()=>{d(async()=>{const C=await fetch(Ve+"/rooms?limit=100");if(!C.ok)throw new Error(`Erreur ${C.status}: ${C.statusText}`);return(await C.json()).data},C=>{y(C)})};return S.useEffect(()=>{R()},[]),S.useEffect(()=>{m(o??{})},[o]),o&&g.jsxs(sp,{title:"Modification de la réservation ID "+o.id,isOpened:n,setIsOpened:r,item:o,handleDelete:b,handleEdit:x,children:[g.jsx(wd,{label:"Session",value:o.roomId,onChange:C=>i({...o,roomId:C.target.value}),selectOptions:h.map(C=>C.name),optionValues:h.map(C=>C.id)}),g.jsx(Un,{margin:"normal",fullWidth:!0,label:"Email du client",value:p.customerEmail??"",onChange:C=>m({...p,customerEmail:C.target.value})}),g.jsx(wd,{label:"Statut",value:o.status,onChange:C=>i({...o,status:C.target.value}),selectOptions:Object.values(D0),optionValues:Object.keys(D0)})]})}function HA(){const{executeWithErrorHandling:n}=Ea(),[r,o]=S.useState(),[i,s]=S.useState(!1),[c,d]=S.useState(null),p=async(x=0,R=10)=>{n(async()=>{const C=await fetch(Ve+`/bookings?page=${x}&limit=${R}`);if(!C.ok)throw new Error(`Erreur ${C.status}: ${C.statusText}`);return await C.json()},C=>{o(C)})},m=async x=>{window.confirm("Êtes-vous sûr de vouloir supprimer cette réservation ?")&&n(async()=>{const R=await fetch(Ve+"/bookings/"+x.id,{method:"DELETE"});if(!R.ok)throw new Error(`Erreur ${R.status}: ${R.statusText}`)},()=>{if(r===void 0)return;const R={...r};R.data=r.data.filter(C=>C.id!==x.id),o(R)})},h=x=>{d(x),s(!0)},y=()=>{if(r===void 0||c===null)return;const x={...r};x.data=r.data.filter(R=>R.id!==c.id),o(x)},b=x=>{if(r===void 0)return;const R=r.data.map(C=>x.id===C.id?x:C);o({...r,data:R})};return S.useEffect(()=>{p()},[]),g.jsx(lp,{title:"Réservations",children:r===void 0?g.jsx(Lo,{message:"Chargement des utilisateurs...",size:"large"}):g.jsxs(g.Fragment,{children:[g.jsx(ip,{tableHeaders:["ID","Session","Client","Participants","Créée le","Statut","Actions"],data:r,updateData:p,hiddenProps:["slotId"],handleAction:h,handleDelete:m}),g.jsx(UA,{isOpened:i,setIsOpened:s,editedBooking:c,setEditedBooking:d,onDelete:y,onEdit:b})]})})}function PA(){const[n,r]=S.useState(""),[o,i]=S.useState(""),[s,c]=S.useState(""),d=oi(),p=m=>{m.preventDefault(),n==="admin"&&o==="admin"?d("/admin"):c("Nom d'utilisateur ou mot de passe incorrect")};return g.jsx(Js,{display:"flex",justifyContent:"center",alignItems:"center",paddingTop:10,children:g.jsxs(_r,{elevation:3,sx:{padding:4,width:300},children:[g.jsx(mu,{variant:"h5",align:"center",gutterBottom:!0,children:"Connexion"}),s&&g.jsx(UR,{severity:"error",children:s}),g.jsxs("form",{onSubmit:p,children:[g.jsx(Un,{label:"Nom d'utilisateur",variant:"outlined",fullWidth:!0,margin:"normal",value:n,onChange:m=>r(m.target.value)}),g.jsx(Un,{label:"Mot de passe",type:"password",variant:"outlined",fullWidth:!0,margin:"normal",value:o,onChange:m=>i(m.target.value)}),g.jsx(Ps,{type:"submit",variant:"contained",color:"primary",fullWidth:!0,sx:{mt:2},children:"Se connecter"})]})]})})}function qA(){return g.jsx(z0,{children:g.jsx("div",{className:dt.app,children:g.jsxs(Px,{children:[g.jsx(I5,{}),g.jsx("main",{className:dt.main,children:g.jsx(z0,{children:g.jsxs(zx,{children:[g.jsx(jn,{path:"/",element:g.jsx(cA,{})}),g.jsx(jn,{path:"/sessions",element:g.jsx(mA,{})}),g.jsx(jn,{path:"/reservation",element:g.jsx(yA,{})}),g.jsx(jn,{path:"/contact",element:g.jsx(SA,{})}),g.jsx(jn,{path:"/mentions",element:g.jsx(xA,{})}),g.jsx(jn,{path:"/admin",element:g.jsx(zA,{})}),g.jsx(jn,{path:"/admin/users",element:g.jsx(BA,{})}),g.jsx(jn,{path:"/admin/sessions",element:g.jsx(LA,{})}),g.jsx(jn,{path:"/admin/bookings",element:g.jsx(HA,{})}),g.jsx(jn,{path:"/login",element:g.jsx(PA,{})}),g.jsx(jn,{path:"*",element:g.jsx(CA,{})})]})})}),g.jsx(t3,{})]})})})}const GA=hi({palette:{mode:"light",primary:{main:"#1976d2"},secondary:{main:"#9c27b0"},background:{default:"#f5f5f5",paper:"#fff"}},typography:{fontFamily:"Roboto, sans-serif"},shape:{borderRadius:8}}),IA=hi({palette:{mode:"dark",primary:{main:"#dc2626"},secondary:{main:"#7c2d12"},background:{default:"#0f0f0f",paper:"#1a1a1a"},text:{primary:"#f5f5f5",secondary:"#b0b0b0"},error:{main:"#ef4444"},warning:{main:"#f59e0b"},success:{main:"#10b981"}},typography:{fontFamily:'"Creepster", "Roboto", sans-serif'},shape:{borderRadius:8}}),mb="horror-house-theme",VA=()=>{try{const n=localStorage.getItem(mb);if(n==="light"||n==="dark")return n}catch(n){console.warn("Erreur lors de la lecture du thème depuis localStorage:",n)}return"dark"},YA=n=>{try{localStorage.setItem(mb,n)}catch(r){console.warn("Erreur lors de la sauvegarde du thème dans localStorage:",r)}};function FA({children:n}){const[r,o]=S.useState(()=>VA());S.useEffect(()=>{YA(r)},[r]);const i=()=>{o(c=>c==="light"?"dark":"light")},s=S.useMemo(()=>r==="dark"?IA:GA,[r]);return g.jsx(fb.Provider,{value:{mode:r,toggleMode:i},children:g.jsxs(DT,{theme:s,children:[g.jsx(Bw,{}),n]})})}async function XA(){const{worker:n}=await zS(async()=>{const{worker:r}=await import("./browser-U0HD-oSA.js");return{worker:r}},[]);return n.start()}const hb=document.getElementById("root");if(!hb)throw new Error("Failed to find root element.");XA().then(()=>{qS.createRoot(hb).render(g.jsx(S.StrictMode,{children:g.jsx(FA,{children:g.jsx(qA,{})})}))});
