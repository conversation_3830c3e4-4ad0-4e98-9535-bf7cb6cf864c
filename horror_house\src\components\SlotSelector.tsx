import { useState, useEffect } from 'react';
import { Room, TimeSlot } from '../mocks/types';
import { API_DOMAIN } from '../const';
import styles from '../css/App.module.css';

interface SlotSelectorProps {
  selectedRoomId?: number;
  selectedSlotId?: number;
  onSlotChange: (roomId: number, slotId: number, slot: TimeSlot) => void;
  onRoomChange?: (roomId: number, room: Room) => void;
}

export default function SlotSelector({ 
  selectedRoomId, 
  selectedSlotId, 
  onSlotChange, 
  onRoomChange 
}: SlotSelectorProps) {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRooms();
  }, []);

  useEffect(() => {
    if (selectedRoomId && rooms.length > 0) {
      const room = rooms.find(r => r.id === selectedRoomId);
      if (room) {
        setSelectedRoom(room);
        onRoomChange?.(room.id, room);
      }
    }
  }, [selectedRoomId, rooms, onRoomChange]);

  const fetchRooms = async () => {
    try {
      const res = await fetch(API_DOMAIN + "/client/rooms");
      const roomsData: Room[] = await res.json();
      setRooms(roomsData);
    } catch (error) {
      console.error('Erreur lors du chargement des sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoomChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const roomId = parseInt(e.target.value);
    const room = rooms.find(r => r.id === roomId);
    if (room) {
      setSelectedRoom(room);
      onRoomChange?.(room.id, room);
    } else {
      setSelectedRoom(null);
    }
  };

  const handleSlotChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const slotId = parseInt(e.target.value);
    if (selectedRoom) {
      const slot = selectedRoom.availableSlots?.find(s => s.id === slotId);
      if (slot) {
        onSlotChange(selectedRoom.id, slotId, slot);
      }
    }
  };

  const formatSlotOption = (slot: TimeSlot) => {
    const date = new Date(slot.date);
    const dateStr = date.toLocaleDateString('fr-FR', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
    return `${dateStr} - ${slot.startTime} à ${slot.endTime} (${slot.availableSpots} places)`;
  };

  const getAvailableSlots = () => {
    if (!selectedRoom?.availableSlots) return [];
    return selectedRoom.availableSlots.filter(slot => slot.availableSpots > 0);
  };

  if (loading) {
    return <div>Chargement des sessions...</div>;
  }

  return (
    <div className={styles.slotSelector}>
      <div className={styles.formGroup}>
        <label htmlFor="room-select">Session d'escape game *</label>
        <select
          id="room-select"
          value={selectedRoom?.id || ''}
          onChange={handleRoomChange}
          className={styles.input}
          required
        >
          <option value="">-- Choisissez une session --</option>
          {rooms.map(room => (
            <option key={room.id} value={room.id}>
              {room.name} - {room.price}€ ({room.duration} min)
            </option>
          ))}
        </select>
      </div>

      {selectedRoom && (
        <div className={styles.formGroup}>
          <label htmlFor="slot-select">Créneau disponible *</label>
          {getAvailableSlots().length > 0 ? (
            <select
              id="slot-select"
              value={selectedSlotId || ''}
              onChange={handleSlotChange}
              className={styles.input}
              required
            >
              <option value="">-- Choisissez un créneau --</option>
              {getAvailableSlots().map(slot => (
                <option key={slot.id} value={slot.id}>
                  {formatSlotOption(slot)}
                </option>
              ))}
            </select>
          ) : (
            <p className={styles.noSlots}>
              ❌ Aucun créneau disponible pour cette session
            </p>
          )}
        </div>
      )}

      {selectedRoom && (
        <div className={styles.sessionInfo}>
          <h4>Informations de la session</h4>
          <div className={styles.sessionMeta}>
            <span>🎭 {selectedRoom.theme}</span>
            <span>⏱️ {selectedRoom.duration} minutes</span>
            <span>💰 {selectedRoom.price}€</span>
            <span>👥 {selectedRoom.minParticipants} - {selectedRoom.maxParticipants} participants</span>
          </div>
          <p className={styles.sessionDescription}>{selectedRoom.description}</p>
        </div>
      )}
    </div>
  );
}
