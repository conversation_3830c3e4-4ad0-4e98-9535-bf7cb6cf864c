import { createTheme } from '@mui/material/styles';

export const lightTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#9c27b0',
    },
    background: {
      default: '#f5f5f5',
      paper: '#fff',
    },
  },
  typography: {
    fontFamily: 'Roboto, sans-serif',
  },
  shape: {
    borderRadius: 8,
  },
});

export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#dc2626', // <PERSON> sang pour le thème horrifique
    },
    secondary: {
      main: '#7c2d12', // Brun sombre
    },
    background: {
      default: '#0f0f0f', // Noir profond
      paper: '#1a1a1a', // Gris très sombre
    },
    text: {
      primary: '#f5f5f5', // Blanc cassé
      secondary: '#b0b0b0', // Gris clair
    },
    error: {
      main: '#ef4444', // Rouge vif pour les erreurs
    },
    warning: {
      main: '#f59e0b', // Orange pour les avertissements
    },
    success: {
      main: '#10b981', // Vert pour les succès
    },
  },
  typography: {
    fontFamily: '"Creepster", "Roboto", sans-serif', // Police horrifique si disponible
  },
  shape: {
    borderRadius: 8,
  },
});
